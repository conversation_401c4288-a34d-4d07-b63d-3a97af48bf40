@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font imports */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;500&display=swap');

/* Base styles */
body {
  @apply font-sans bg-background text-foreground antialiased;
  font-family: 'Poppins', sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  @apply w-2;
}

::-webkit-scrollbar-track {
  @apply bg-background;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary/30 rounded-full hover:bg-primary/50;
}

/* Text balance utility */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Gradient text */
  .text-gradient {
    @apply bg-clip-text text-transparent;
    background-image: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--accent)));
  }
}

/* Animation styles */
@keyframes slideHero {
  from { transform: translateX(0); }
  to { transform: translateX(-100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* Floating background animations for resume builder */
@keyframes float-1 {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.1; }
  25% { transform: translate(20px, -30px) scale(1.1); opacity: 0.15; }
  50% { transform: translate(-15px, -20px) scale(0.9); opacity: 0.08; }
  75% { transform: translate(25px, 10px) scale(1.05); opacity: 0.12; }
}

@keyframes float-2 {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.1; }
  33% { transform: translate(-25px, 20px) scale(1.2); opacity: 0.18; }
  66% { transform: translate(30px, -15px) scale(0.8); opacity: 0.06; }
}

@keyframes float-3 {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.1; }
  20% { transform: translate(15px, 25px) scale(0.9); opacity: 0.14; }
  40% { transform: translate(-20px, -10px) scale(1.15); opacity: 0.16; }
  60% { transform: translate(10px, -25px) scale(0.95); opacity: 0.09; }
  80% { transform: translate(-10px, 15px) scale(1.08); opacity: 0.13; }
}

@keyframes float-4 {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.1; }
  50% { transform: translate(-30px, 20px) scale(1.3); opacity: 0.2; }
}

@keyframes float-5 {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.1; }
  25% { transform: translate(-20px, -25px) scale(0.85); opacity: 0.07; }
  50% { transform: translate(25px, 15px) scale(1.1); opacity: 0.15; }
  75% { transform: translate(-15px, 30px) scale(1.05); opacity: 0.12; }
}

@keyframes float-6 {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.1; }
  40% { transform: translate(20px, -20px) scale(1.25); opacity: 0.17; }
  80% { transform: translate(-25px, 25px) scale(0.9); opacity: 0.08; }
}

@keyframes float-7 {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.1; }
  30% { transform: translate(-15px, -30px) scale(0.95); opacity: 0.11; }
  60% { transform: translate(35px, 10px) scale(1.15); opacity: 0.16; }
}

@keyframes float-8 {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.1; }
  25% { transform: translate(10px, 20px) scale(1.05); opacity: 0.13; }
  50% { transform: translate(-20px, -15px) scale(0.88); opacity: 0.07; }
  75% { transform: translate(15px, -25px) scale(1.12); opacity: 0.14; }
}

.logo-slide {
  animation: 10s slideHero infinite linear;
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* AI-themed glass morphism */
.glass-effect {
  @apply backdrop-blur-lg bg-white/10 border border-white/10;
}

/* Team carousel overrides */
.team-carousel {
  .slick-list {
    @apply mx-[-10px];
  }
  
  .slick-slide > div {
    @apply px-[10px];
  }
  
  .slick-dots {
    li button:before {
      @apply text-primary;
    }
    
    li.slick-active button:before {
      @apply text-primary;
    }
  }
  
  .slick-prev, .slick-next {
    @apply z-10;
    
    &:before {
      @apply text-primary;
    }
  }
  
  .slick-prev {
    @apply left-[-25px];
  }
  
  .slick-next {
    @apply right-[-25px];
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .team-carousel {
    .slick-prev {
      @apply left-[-15px];
    }
    
    .slick-next {
      @apply right-[-15px];
    }
  }
}

/* Color variables - moved to tailwind.config.js */
/* Remove the @layer base section since these are now in your config file */