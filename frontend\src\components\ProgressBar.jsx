import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { CheckCir<PERSON>, Loader2 } from 'lucide-react';

const ProgressBar = ({ isVisible, onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const steps = [
    { id: 0, label: 'Processing your information', duration: 1500 },
    { id: 1, label: 'Generating AI-optimized content', duration: 3000 },
    { id: 2, label: 'Analyzing ATS compatibility', duration: 2000 },
    { id: 3, label: 'Formatting professional resume', duration: 1500 },
    { id: 4, label: 'Finalizing document', duration: 1000 }
  ];

  useEffect(() => {
    if (!isVisible) {
      setCurrentStep(0);
      setProgress(0);
      return;
    }

    let totalDuration = 0;
    let currentDuration = 0;

    const executeSteps = async () => {
      for (let i = 0; i < steps.length; i++) {
        setCurrentStep(i);
        
        // Animate progress for current step
        const stepDuration = steps[i].duration;
        const startProgress = (i / steps.length) * 100;
        const endProgress = ((i + 1) / steps.length) * 100;
        
        const startTime = Date.now();
        const animateProgress = () => {
          const elapsed = Date.now() - startTime;
          const stepProgress = Math.min(elapsed / stepDuration, 1);
          const currentProgress = startProgress + (endProgress - startProgress) * stepProgress;
          
          setProgress(currentProgress);
          
          if (stepProgress < 1) {
            requestAnimationFrame(animateProgress);
          }
        };
        
        animateProgress();
        await new Promise(resolve => setTimeout(resolve, stepDuration));
      }
      
      // Complete
      setProgress(100);
      setTimeout(() => {
        onComplete?.();
      }, 500);
    };

    executeSteps();
  }, [isVisible, onComplete]);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <div className="bg-gray-900/90 backdrop-blur-md rounded-xl p-8 border border-white/10 max-w-md w-full mx-4">
        <div className="text-center mb-6">
          <h3 className="text-xl font-semibold text-white mb-2">
            Creating Your AI-Optimized Resume
          </h3>
          <p className="text-gray-400 text-sm">
            Our AI is crafting your professional, ATS-friendly resume...
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-400 mb-2">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <motion.div
              className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Steps */}
        <div className="space-y-3">
          {steps.map((step, index) => (
            <motion.div
              key={step.id}
              className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                index === currentStep
                  ? 'bg-purple-500/20 border border-purple-500/30'
                  : index < currentStep
                  ? 'bg-green-500/20 border border-green-500/30'
                  : 'bg-gray-800/50 border border-gray-700/30'
              }`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="flex-shrink-0">
                {index < currentStep ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : index === currentStep ? (
                  <Loader2 className="h-5 w-5 text-purple-500 animate-spin" />
                ) : (
                  <div className="h-5 w-5 rounded-full border-2 border-gray-600" />
                )}
              </div>
              <span
                className={`text-sm ${
                  index <= currentStep ? 'text-white' : 'text-gray-500'
                }`}
              >
                {step.label}
              </span>
            </motion.div>
          ))}
        </div>

        {/* Estimated time */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Estimated time: {Math.max(0, Math.round((100 - progress) / 10))} seconds remaining
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default ProgressBar;
