"use client";
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  FileText, 
  Sparkles, 
  CheckCircle, 
  ArrowRight,
  Eye,
  Download,
  Zap,
  Target,
  TrendingUp,
  RefreshCw
} from 'lucide-react';
import ResumeUpload from './ResumeUpload';
import JobDescriptionInput from './JobDescriptionInput';
import ATSAnalysisDisplay from './ATSAnalysisDisplay';
import BeforeAfterComparison from './BeforeAfterComparison';
import { toast } from 'react-hot-toast';

const UploadEnhancementWorkflow = ({ onComplete, onBack }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [uploadedData, setUploadedData] = useState(null);
  const [jobData, setJobData] = useState(null);
  const [enhancedData, setEnhancedData] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const steps = [
    {
      id: 'upload',
      title: 'Upload Resume',
      description: 'Upload your existing resume for analysis',
      icon: Upload,
      features: ['Text extraction', 'Format detection', 'Content parsing']
    },
    {
      id: 'analyze',
      title: 'AI Analysis',
      description: 'Get comprehensive ATS analysis',
      icon: Sparkles,
      features: ['ATS scoring', 'Keyword analysis', 'Structure review']
    },
    {
      id: 'target',
      title: 'Job Targeting',
      description: 'Optimize for specific job description',
      icon: Target,
      features: ['Job matching', 'Keyword optimization', 'Content tailoring']
    },
    {
      id: 'enhance',
      title: 'AI Enhancement',
      description: 'Get AI-powered improvements',
      icon: Zap,
      features: ['Content enhancement', 'Achievement quantification', 'Language optimization']
    },
    {
      id: 'compare',
      title: 'Before/After',
      description: 'Review improvements made',
      icon: TrendingUp,
      features: ['Side-by-side comparison', 'Score improvement', 'Change highlights']
    }
  ];

  const handleUploadComplete = (analysisData) => {
    console.log('📊 Upload analysis received:', analysisData);
    setUploadedData(analysisData);
    setCurrentStep(1);
    toast.success('Resume uploaded and analyzed successfully!');
  };

  const handleJobDescriptionSubmit = async (jobDescriptionData) => {
    if (!uploadedData?.extractedData) {
      toast.error('No resume data available. Please upload a resume first.');
      return;
    }

    setIsProcessing(true);
    setJobData(jobDescriptionData);

    try {
      console.log('🎯 Generating targeted resume with job data:', jobDescriptionData);

      const response = await fetch('/api/generate-targeted-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          extractedResumeData: uploadedData.extractedData,
          jobDescription: jobDescriptionData.description,
          jobTitle: jobDescriptionData.jobTitle,
          company: jobDescriptionData.company
        }),
      });

      const data = await response.json();
      console.log('📊 Targeted resume response:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate targeted resume');
      }

      setEnhancedData(data);
      setCurrentStep(4); // Jump to comparison
      toast.success('Resume optimized for the target job!');

    } catch (error) {
      console.error('Targeted resume generation error:', error);
      toast.error(error.message || 'Failed to generate targeted resume');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleContinueToForm = () => {
    if (enhancedData && onComplete) {
      onComplete(enhancedData);
    } else if (uploadedData && onComplete) {
      onComplete(uploadedData);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-white mb-4">Upload Your Resume</h2>
              <p className="text-gray-300">
                Upload your existing resume to get started with AI-powered enhancements
              </p>
            </div>
            <ResumeUpload
              onAnalysisComplete={handleUploadComplete}
              analysisType="full"
            />
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-white mb-4">Analysis Complete!</h2>
              <p className="text-gray-300">
                Your resume has been analyzed. Review the results below.
              </p>
            </div>
            
            {uploadedData && (
              <ATSAnalysisDisplay
                analysisData={uploadedData}
                analysisType="full"
              />
            )}

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                onClick={() => setCurrentStep(2)}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Target className="h-5 w-5" />
                Optimize for Job
              </motion.button>

              <motion.button
                onClick={handleContinueToForm}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <ArrowRight className="h-5 w-5" />
                Continue to Editor
              </motion.button>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-white mb-4">Target a Specific Job</h2>
              <p className="text-gray-300">
                Provide job details to optimize your resume for better ATS compatibility
              </p>
            </div>

            <JobDescriptionInput
              onJobDescriptionSubmit={handleJobDescriptionSubmit}
              isLoading={isProcessing}
            />

            <div className="flex justify-center">
              <motion.button
                onClick={() => setCurrentStep(1)}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Back to Analysis
              </motion.button>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-white mb-4">Enhancement Complete!</h2>
              <p className="text-gray-300">
                Your resume has been optimized. See the improvements below.
              </p>
            </div>

            {uploadedData && enhancedData && (
              <BeforeAfterComparison
                originalData={uploadedData.extractedData}
                enhancedData={enhancedData.enhancedResume}
                analysisData={enhancedData}
              />
            )}

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                onClick={handleContinueToForm}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <ArrowRight className="h-5 w-5" />
                Continue to Editor
              </motion.button>

              <motion.button
                onClick={() => setCurrentStep(2)}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <RefreshCw className="h-4 w-4" />
                Try Different Job
              </motion.button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
      {/* Grid background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-16">
        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex justify-center items-center space-x-4 overflow-x-auto pb-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep || (index === 4 && enhancedData);
              const isAccessible = index <= currentStep || isCompleted;

              return (
                <motion.div
                  key={step.id}
                  className={`flex flex-col items-center space-y-2 min-w-fit ${
                    isAccessible ? 'cursor-pointer' : 'cursor-not-allowed'
                  }`}
                  onClick={() => isAccessible && setCurrentStep(index)}
                  whileHover={isAccessible ? { scale: 1.05 } : {}}
                  whileTap={isAccessible ? { scale: 0.95 } : {}}
                >
                  <div className={`w-12 h-12 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
                    isActive
                      ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg'
                      : isCompleted
                      ? 'bg-green-500 border-green-500'
                      : 'bg-gray-800 border-gray-600'
                  }`}>
                    {isCompleted && index !== currentStep ? (
                      <CheckCircle className="h-6 w-6 text-white" />
                    ) : (
                      <Icon className={`h-6 w-6 ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`} />
                    )}
                  </div>
                  <div className="text-center">
                    <div className={`text-sm font-medium ${
                      isActive ? 'text-neural-purple' : 
                      isCompleted ? 'text-green-400' : 'text-gray-400'
                    }`}>
                      {step.title}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStepContent()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Back Button */}
        <div className="flex justify-center mt-12">
          <motion.button
            onClick={onBack}
            className="inline-flex items-center gap-2 px-6 py-3 text-gray-400 hover:text-white transition-colors"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <ArrowRight className="h-4 w-4 rotate-180" />
            Back to Mode Selection
          </motion.button>
        </div>
      </div>
    </div>
  );
};

export default UploadEnhancementWorkflow;
