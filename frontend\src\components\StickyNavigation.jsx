"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { 
  ChevronLeft, 
  ChevronRight, 
  Save, 
  <PERSON>rk<PERSON>,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';

const StickyNavigation = ({ 
  currentStep, 
  totalSteps, 
  onPrevious, 
  onNext, 
  onGenerate,
  isGenerating = false,
  canProceed = true,
  steps = [],
  className = "" 
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;
  const currentStepData = steps[currentStep];

  return (
    <motion.div
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className={`fixed bottom-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-sm border-t border-gray-700 ${className}`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4">
          {/* Left Side - Previous Button */}
          <div className="flex items-center space-x-4">
            {!isFirstStep ? (
              <motion.button
                onClick={onPrevious}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center gap-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg transition-all duration-200 text-gray-300 hover:text-white"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline">Previous</span>
              </motion.button>
            ) : (
              <div className="w-20"></div> // Spacer for alignment
            )}
          </div>

          {/* Center - Step Info */}
          <div className="flex-1 text-center px-4">
            <div className="text-sm font-medium text-white whitespace-nowrap overflow-hidden text-ellipsis">
              {currentStepData?.title || `Step ${currentStep + 1}`}
            </div>
            <div className="text-xs text-gray-400 mt-1 whitespace-nowrap">
              {currentStep + 1} of {totalSteps} • {Math.round(((currentStep + 1) / totalSteps) * 100)}% Complete
            </div>
          </div>

          {/* Right Side - Next/Generate Button */}
          <div className="flex items-center space-x-4">
            {!isLastStep ? (
              <motion.button
                onClick={onNext}
                disabled={!canProceed}
                whileHover={canProceed ? { scale: 1.02 } : {}}
                whileTap={canProceed ? { scale: 0.98 } : {}}
                className={`
                  flex items-center gap-2 px-6 py-2 rounded-lg transition-all duration-200 font-medium
                  ${canProceed 
                    ? 'bg-neural-purple hover:bg-neural-purple/80 text-white' 
                    : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                  }
                `}
              >
                <span>Next</span>
                <ArrowRight className="h-4 w-4" />
              </motion.button>
            ) : (
              <motion.button
                onClick={onGenerate}
                disabled={isGenerating || !canProceed}
                whileHover={!isGenerating && canProceed ? { scale: 1.02 } : {}}
                whileTap={!isGenerating && canProceed ? { scale: 0.98 } : {}}
                className={`
                  flex items-center gap-2 px-6 py-2 rounded-lg transition-all duration-200 font-medium
                  ${!isGenerating && canProceed
                    ? 'bg-gradient-to-r from-neural-purple to-neural-pink hover:from-neural-purple/80 hover:to-neural-pink/80 text-white' 
                    : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                  }
                `}
              >
                {isGenerating ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Save className="h-4 w-4" />
                    </motion.div>
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" />
                    <span>Generate Resume</span>
                  </>
                )}
              </motion.button>
            )}
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-800 h-1">
        <motion.div
          className="bg-gradient-to-r from-neural-purple to-neural-pink h-1"
          initial={{ width: 0 }}
          animate={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>
    </motion.div>
  );
};

export default StickyNavigation;
