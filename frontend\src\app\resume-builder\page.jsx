"use client";
import { useEffect, useState, useRef } from "react";
import { Mic, Keyboard, Check, Edit2, Download } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { Card } from "@/components/ui/card";
import { WelcomeScreen } from "@/components/WelcomeScreen";
import { ConfirmationDialog } from "@/components/ConfirmationDialog";
import { VoiceInput } from "@/components/InputSection/VoiceInput";
import { TextInput } from "@/components/InputSection/TextInput";
import { PreviewSection } from "@/components/PreviewSection";
import { SuccessScreen } from "@/components/SuccessScreen";
import { FAQSection } from "@/components/FAQ";
import { ComingSoonModal } from "@/components/ComingSoonModal";
import ProgressBar from "@/components/ProgressBar";

const ResumeBuilder = () => {
  // State for initial setup
  const [setupComplete, setSetupComplete] = useState(false);
  const [language, setLanguage] = useState("");
  const [inputMode, setInputMode] = useState("");
  const [activeFaq, setActiveFaq] = useState(null);


  // State for form data
  const [formData, setFormData] = useState({
    personal: {
      name: "",
      email: "",
      phone: "",
    },
    education: {
      degree: "",
      institution: "",
      field: "",
      graduationYear: "",
    },
  });

  // UI state
  const [currentSection, setCurrentSection] = useState("personal");
  const [isRecording, setIsRecording] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resumeGenerated, setResumeGenerated] = useState(false);
  const [resumeUrl, setResumeUrl] = useState("");
  const [transcript, setTranscript] = useState("");
  const recognitionRef = useRef(null);
  const previewRef = useRef(null);
  const synthRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);

  // Add these new states
  const [needsConfirmation, setNeedsConfirmation] = useState(false);
  const [confirmedTranscript, setConfirmedTranscript] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedVoice, setSelectedVoice] = useState(null);
  const [showComingSoon, setShowComingSoon] = useState(true);
  const [showProgressBar, setShowProgressBar] = useState(false);
  // Refs

  // Questions in both languages
  const questions = {
    personal: {
      english: [
        { id: "name", text: "What is your full name?" },
        {
          id: "email",
          text: "What is your email address?",
          forceEnglish: true,
        },
        { id: "phone", text: "What is your phone number?", forceEnglish: true },
      ],
      hindi: [
        { id: "name", text: "आपका पूरा नाम क्या है?" },
        { id: "email", text: "आपका ईमेल पता क्या है?", forceEnglish: true },
        { id: "phone", text: "आपका फोन नंबर क्या है?", forceEnglish: true },
      ],
    },
    education: {
      english: [
        { id: "degree", text: "What degree did you earn?" },
        { id: "institution", text: "Which institution did you attend?" },
        { id: "field", text: "What was your field of study?" },
        {
          id: "graduationYear",
          text: "When did you graduate? (YYYY)",
          forceEnglish: true,
        },
      ],
      hindi: [
        { id: "degree", text: "आपने कौन सी डिग्री प्राप्त की?" },
        { id: "institution", text: "आपने किस संस्थान में अध्ययन किया?" },
        { id: "field", text: "आपका अध्ययन क्षेत्र क्या था?" },
        {
          id: "graduationYear",
          text: "आपने कब स्नातक किया? (YYYY)",
          forceEnglish: true,
        },
      ],
    },
  };

  // Initialize speech recognition and synthesis
  useEffect(() => {
    if (typeof window !== "undefined" && setupComplete) {
      // Initialize speech recognition with fallback to MediaRecorder
      const SpeechRecognition =
        window.SpeechRecognition || window.webkitSpeechRecognition;

      if (SpeechRecognition) {
        recognitionRef.current = new SpeechRecognition();
        recognitionRef.current.continuous = true;
        recognitionRef.current.interimResults = true;
        recognitionRef.current.lang = language === "hindi" ? "hi-IN" : "en-US";

        recognitionRef.current.onresult = (event) => {
          const currentTranscript = Array.from(event.results)
            .map((result) => result[0])
            .map((result) => result.transcript)
            .join("");

          setTranscript(currentTranscript);

          if (event.results[0].isFinal) {
            processTranscript(currentTranscript);
          }
        };

        recognitionRef.current.onerror = (event) => {
          console.error("Speech recognition error", event.error);
          setIsRecording(false);
          toast({
            title: "Recording Error",
            description: `Could not record: ${event.error}`,
            variant: "destructive",
          });
        };

        recognitionRef.current.onend = () => {
          if (isRecording) {
            // Automatically restart recording if it ended unexpectedly
            recognitionRef.current.start();
          }
        };
      }

      // Initialize speech synthesis
      if ("speechSynthesis" in window) {
        synthRef.current = window.speechSynthesis;

        // Load voices and select appropriate one
        const loadVoices = () => {
          const voices = synthRef.current.getVoices();
          const preferredVoice = voices.find((v) =>
            language === "hindi"
              ? v.lang.startsWith("hi") || v.name.includes("Hindi")
              : v.lang.startsWith("en") && v.name.includes("Female")
          );
          setSelectedVoice(preferredVoice || voices[0]);
        };

        synthRef.current.onvoiceschanged = loadVoices;
        loadVoices();
      }
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
      }
      if (synthRef.current) {
        synthRef.current.cancel();
      }
    };
  }, [language, setupComplete]);

  const processTranscript = (transcript) => {
    // Apply Hindi corrections if needed
    if (language === "hindi") {
      transcript = correctHindiTranscript(transcript);
    }

    const currentQuestionObj =
      questions[currentSection][language][currentQuestion];

    // For critical fields, ask for confirmation
    if (currentQuestionObj.forceEnglish || currentSection === "personal") {
      setConfirmedTranscript(transcript);
      setNeedsConfirmation(true);
    } else {
      updateFormField(transcript);
    }
  };

  const correctHindiTranscript = (text) => {
    // Common corrections for Hindi speech recognition
    const corrections = {
      नाम: ["नम", "नामा", "नमः"],
      ईमेल: ["इमेल", "मेल"],
      फोन: ["फ़ोन", "पोन"],
      डिग्री: ["डिग्री", "डिग्रि"],
      संस्थान: ["संस्था", "सस्थान"],
    };

    Object.entries(corrections).forEach(([correct, incorrects]) => {
      incorrects.forEach((incorrect) => {
        const regex = new RegExp(incorrect, "g");
        text = text.replace(regex, correct);
      });
    });

    return text;
  };

  const handleConfirmTranscript = (confirmed) => {
    if (confirmed) {
      updateFormField(confirmedTranscript);
    }
    setNeedsConfirmation(false);
    setConfirmedTranscript("");
  };

  // Speak the current question when question changes or input mode changes to voice
  useEffect(() => {
    if (inputMode === "voice" && synthRef.current && setupComplete) {
      speakQuestion();
    }
  }, [currentQuestion, currentSection, language, inputMode, setupComplete]);

  const startRecording = async () => {
    try {
      setIsRecording(true);
      setTranscript("");

      if (recognitionRef.current) {
        recognitionRef.current.start();
      } else {
        // Fallback to MediaRecorder API if SpeechRecognition not available
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        mediaRecorderRef.current = new MediaRecorder(stream);
        audioChunksRef.current = [];

        mediaRecorderRef.current.ondataavailable = (e) => {
          audioChunksRef.current.push(e.data);
        };

        mediaRecorderRef.current.onstop = async () => {
          const audioBlob = new Blob(audioChunksRef.current);
          await processAudioBlob(audioBlob);
          stream.getTracks().forEach((track) => track.stop());
        };

        mediaRecorderRef.current.start();
      }

      // Speak the question
      speakQuestion();
    } catch (error) {
      console.error("Recording error:", error);
      setIsRecording(false);
      toast({
        title: "Recording Error",
        description: "Could not access microphone",
        variant: "destructive",
      });
    }
  };

  const stopRecording = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
    }
    setIsRecording(false);
  };

  const speakQuestion = () => {
    if (!synthRef.current || !selectedVoice) return;

    synthRef.current.cancel();

    const questionText =
      questions[currentSection][language][currentQuestion].text;
    const utterance = new SpeechSynthesisUtterance(questionText);

    utterance.voice = selectedVoice;
    utterance.lang = language === "hindi" ? "hi-IN" : "en-US";
    utterance.rate = 0.9; // Slightly slower for better comprehension

    // Add emphasis on important words
    if (language === "hindi") {
      utterance.pitch = 1.1;
    }

    synthRef.current.speak(utterance);
  };

  const updateFormField = (value) => {
    const currentQuestionObj =
      questions[currentSection][language][currentQuestion];
    const fieldId = currentQuestionObj.id;

    // Additional validation for specific fields
    if (currentQuestionObj.forceEnglish) {
      value = value.replace(/[^a-zA-Z0-9@._+-]/g, "");

      // Special handling for email
      if (fieldId === "email" && !value.includes("@")) {
        toast({
          title: "Invalid Email",
          description: "Please include @ in your email address",
          variant: "destructive",
        });
        return;
      }

      // Special handling for phone numbers
      if (fieldId === "phone" && !/^\d+$/.test(value)) {
        toast({
          title: "Invalid Phone",
          description: "Please enter numbers only",
          variant: "destructive",
        });
        return;
      }
    }

    setFormData((prev) => ({
      ...prev,
      [currentSection]: {
        ...prev[currentSection],
        [fieldId]: value,
      },
    }));

    // Auto-advance for short fields if voice mode
    if (
      inputMode === "voice" &&
      value.length > 3 &&
      (fieldId === "email" ||
        fieldId === "phone" ||
        fieldId === "graduationYear")
    ) {
      setTimeout(handleNext, 500);
    }
  };

  const handleNext = () => {
    if (currentQuestion < questions[currentSection][language].length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // Move to next section or submit
      if (currentSection === "personal") {
        setCurrentSection("education");
        setCurrentQuestion(0);
      } else {
        handleSubmit();
      }
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Validate required fields
      const requiredFields = [
        ...questions.personal.english.map((q) => q.id),
        ...questions.education.english.map((q) => q.id),
      ];

      const isValid = requiredFields.every((field) => {
        const section = field in formData.personal ? "personal" : "education";
        return formData[section][field];
      });

      if (!isValid) {
        toast({
          title: "Missing Information",
          description: "Please fill all required fields",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      // Show progress bar
      setShowProgressBar(true);

      // Call Gemini API for resume generation
      const response = await fetch('/api/generate-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate resume');
      }

      // Set the resume URL from the API response
      setResumeUrl(data.downloadUrl);

      // Progress bar will handle the completion timing
    } catch (error) {
      console.error("Error generating resume:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to generate resume",
        variant: "destructive",
      });
      setIsSubmitting(false);
      setShowProgressBar(false);
    }
  };

  const handleProgressComplete = () => {
    setShowProgressBar(false);
    setResumeGenerated(true);
    setIsSubmitting(false);
    toast({
      title: "Success!",
      description: "Your resume has been generated successfully",
    });
  };

  const handleEdit = () => {
    setResumeGenerated(false);
    setCurrentSection("personal");
    setCurrentQuestion(0);
  };

  const handleDownload = () => {
    if (!resumeUrl) return;

    const link = document.createElement("a");
    link.href = resumeUrl;
    link.download = `${formData.personal.name || "resume"}.pdf`;
    link.click();
  };

  const getCurrentFieldValue = () => {
    const currentQuestionObj =
      questions[currentSection][language][currentQuestion];
    const fieldId = currentQuestionObj.id;
    return formData[currentSection]?.[fieldId] || "";
  };

  const handleInputModeChange = (mode) => {
    setInputMode(mode);
    if (mode === "voice" && setupComplete) {
      // Start recording automatically when switching to voice mode
      setTimeout(() => startRecording(), 100);
    } else if (isRecording) {
      stopRecording();
    }
  };

  const completeSetup = () => {
    if (!language || !inputMode) {
      toast({
        title: "Selection Required",
        description: "Please select both language and input method",
        variant: "destructive",
      });
      return;
    }
    setSetupComplete(true);
  };
  useEffect(() => {
  const hasSeenModal = localStorage.getItem('hasSeenComingSoon');
  if (hasSeenModal) {
    setShowComingSoon(false);
  }
}, []);

const handleCloseModal = () => {
  localStorage.setItem('hasSeenComingSoon', 'true');
  setShowComingSoon(false);
};

// Then update the modal usage:
{showComingSoon && (
  <ComingSoonModal onClose={handleCloseModal} />
)}

 

  return (
    <div className="min-h-screen pt-20 md:pt-24 bg-gradient-to-b from-black to-[#0A0A0A] text-white p-4 md:p-8">
      {showComingSoon && (
        <ComingSoonModal onClose={() => setShowComingSoon(false)} />
      )}

      <ProgressBar
        isVisible={showProgressBar}
        onComplete={handleProgressComplete}
      />

      {needsConfirmation && (
        <ConfirmationDialog
          language={language}
          confirmedTranscript={confirmedTranscript}
          onConfirm={() => handleConfirmTranscript(true)}
          onRetry={() => setNeedsConfirmation(false)}
        />
      )}
      <div className="max-w-6xl mx-auto">
        {!setupComplete ? (
          <WelcomeScreen
            language={language}
            inputMode={inputMode}
            onLanguageChange={setLanguage}
            onInputModeChange={setInputMode}
            onCompleteSetup={completeSetup}
          />
        ) : !resumeGenerated ? (
          <>
            <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500">
              AI Resume Builder
            </h1>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {inputMode === "voice" ? (
                <VoiceInput
                  transcript={transcript}
                  currentValue={getCurrentFieldValue()}
                  isRecording={isRecording}
                  isProcessing={isProcessing}
                  language={language}
                  onSwitchToText={() => handleInputModeChange("text")}
                  onToggleRecording={isRecording ? stopRecording : startRecording}
                />
              ) : (
                <TextInput
                  value={getCurrentFieldValue()}
                  onChange={updateFormField}
                  placeholder={
                    questions[currentSection][language][currentQuestion]
                      .forceEnglish
                      ? language === "hindi"
                        ? "केवल अंग्रेजी में टाइप करें"
                        : "Type in English only"
                      : language === "hindi"
                      ? "अपना उत्तर टाइप करें..."
                      : "Type your answer..."
                  }
                />
              )}

              <PreviewSection
                formData={formData}
                language={language}
                isProcessing={isProcessing}
              />
            </div>
          </>
        ) : (
          <SuccessScreen
            formData={formData}
            language={language}
            resumeUrl={resumeUrl}
            onDownload={handleDownload}
            onEdit={handleEdit}
          />
        )}

        {!resumeGenerated && (
          <FAQSection
            language={language}
            activeFaq={activeFaq}
            setActiveFaq={setActiveFaq}
          />
        )}
      </div>
    </div>
  );
};

export default ResumeBuilder;
