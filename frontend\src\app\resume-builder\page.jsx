"use client";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  User, 
  GraduationCap, 
  Briefcase, 
  Award,
  FileText, 
  ArrowLeft,
  ArrowRight,
  Sparkles,
  Eye,
  Wand2,
  Plus,
  Trash2,
  MapPin,
  Calendar,
  Globe,
  Mail,
  Phone,
  Link,
  Upload,
  PlusCircle,
  Palette
} from "lucide-react";
import { SparklesIcon } from '@heroicons/react/24/solid';
import { toast } from "react-hot-toast";
import ProgressBar from "@/components/ProgressBar";
import ATSScoreCircle from "@/components/ATSScoreCircle";
import EnhancedResumePreview from "@/components/EnhancedResumePreview";
import PDFDownload, { ViewResumeButton } from "@/components/PDFDownload";
import ResumeBuilderModeToggle from "@/components/ResumeBuilderModeToggle";
import ResumeUpload from "@/components/ResumeUpload";
import ATSAnalysisDisplay from "@/components/ATSAnalysisDisplay";
import JobDescriptionInput from "@/components/JobDescriptionInput";
import BeforeAfterComparison from "@/components/BeforeAfterComparison";
import AIContentSuggestions from "@/components/AIContentSuggestions";
import ATSOptimizationPanel from "@/components/ATSOptimizationPanel";
import TemplateSelector from "@/components/TemplateSelector";
import useATSAnalysis from "@/hooks/useATSAnalysis";
import ATSFieldIndicator from "@/components/ATSFieldIndicator";
import ClientOnly from "@/components/ClientOnly";
// import SuccessScreen from "@/components/SuccessScreen";
import { ExperienceForm, SkillsProjectsForm, ReviewForm } from "@/components/ResumeFormComponents";

const ResumeBuilder = () => {
  // Main state management
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showProgressBar, setShowProgressBar] = useState(false);
  const [resumeGenerated, setResumeGenerated] = useState(false);
  const [resumeUrl, setResumeUrl] = useState("");
  const [resumeData, setResumeData] = useState(null);
  const [atsScore, setAtsScore] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [showPreview, setShowPreview] = useState(false);

  // New state for upload functionality
  const [builderMode, setBuilderMode] = useState('create'); // 'create', 'upload', 'analyze'
  const [uploadAnalysis, setUploadAnalysis] = useState(null);
  const [showAnalysis, setShowAnalysis] = useState(false);

  // Enhanced upload workflow state
  const [showJobDescriptionInput, setShowJobDescriptionInput] = useState(false);
  const [isGeneratingTargeted, setIsGeneratingTargeted] = useState(false);
  const [targetedResumeData, setTargetedResumeData] = useState(null);

  // Template selection state
  const [selectedTemplate, setSelectedTemplate] = useState('modern');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    personal: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      location: "",
      linkedin: "",
      portfolio: "",
      summary: ""
    },
    education: [
      {
        id: 1,
        degree: "",
        institution: "",
        location: "",
        startDate: "",
        endDate: "",
        gpa: "",
        relevant: ""
      }
    ],
    experience: [
      {
        id: 1,
        title: "",
        company: "",
        location: "",
        startDate: "",
        endDate: "",
        current: false,
        description: ""
      }
    ],
    skills: {
      technical: [],
      languages: [],
      certifications: []
    },
    projects: [
      {
        id: 1,
        name: "",
        description: "",
        technologies: "",
        link: ""
      }
    ]
  });

  // Real-time ATS analysis
  const atsAnalysis = useATSAnalysis(formData);

  // Steps configuration
  const steps = [
    {
      id: 0,
      title: "Personal Information",
      icon: User,
      description: "Tell us about yourself"
    },
    {
      id: 1,
      title: "Education",
      icon: GraduationCap,
      description: "Your academic background"
    },
    {
      id: 2,
      title: "Experience",
      icon: Briefcase,
      description: "Your work experience"
    },
    {
      id: 3,
      title: "Skills & Projects",
      icon: Award,
      description: "Showcase your abilities"
    },
    {
      id: 4,
      title: "Review & Generate",
      icon: FileText,
      description: "Finalize your resume"
    }
  ];

  // Update form data
  const updateFormData = (section, field, value, index = null) => {
    setFormData(prev => {
      if (index !== null && Array.isArray(prev[section])) {
        const newArray = [...prev[section]];
        newArray[index] = { ...newArray[index], [field]: value };
        return { ...prev, [section]: newArray };
      } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {
        return {
          ...prev,
          [section]: { ...prev[section], [field]: value }
        };
      }
      return prev;
    });
  };

  // Add new item to array sections
  const addArrayItem = (section, template) => {
    setFormData(prev => ({
      ...prev,
      [section]: [...prev[section], { ...template, id: Math.random().toString(36).substring(2, 11) }]
    }));
  };

  // Remove item from array sections
  const removeArrayItem = (section, id) => {
    setFormData(prev => ({
      ...prev,
      [section]: prev[section].filter(item => item.id !== id)
    }));
  };

  // Navigation functions
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Generate resume with AI
  const generateResume = async () => {
    try {
      console.log('🚀 Starting resume generation...');
      console.log('📝 Form data:', formData);

      setIsGenerating(true);
      setShowProgressBar(true);

      const response = await fetch('/api/generate-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formData,
          templateId: selectedTemplate
        }),
      });

      console.log('📡 API response status:', response.status);
      console.log('📡 API response headers:', Object.fromEntries(response.headers.entries()));

      // Check if response is actually JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        console.error('❌ Non-JSON response received:', textResponse);
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();
      console.log('📊 API response data:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate resume');
      }

      // Store the enhanced resume data and ATS information
      console.log('✅ Setting resume data...');
      setResumeUrl(data.downloadUrl);
      setResumeData(data.resumeData);
      setAtsScore(data.atsScore || data.resumeData?.atsScore?.overall || 75);
      setSuggestions(data.suggestions || data.resumeData?.atsScore?.improvements || []);

      console.log('🎉 Resume generation completed successfully');

    } catch (error) {
      console.error("💥 Error generating resume:", error);
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      toast.error(error.message || "Failed to generate resume");
      setIsGenerating(false);
      setShowProgressBar(false);
    }
  };

  const handleProgressComplete = () => {
    setShowProgressBar(false);
    setResumeGenerated(true);
    setIsGenerating(false);

    // Show success message with ATS score
    const scoreMessage = atsScore
      ? `Resume generated! ATS Score: ${atsScore}%`
      : "Your resume has been generated successfully!";
    toast.success(scoreMessage);
  };

  // Handle upload analysis completion
  const handleUploadAnalysis = (analysisData) => {
    console.log('📊 Upload analysis received:', analysisData);
    setUploadAnalysis(analysisData);

    // For upload & enhance mode, show job description input
    if (builderMode === 'upload') {
      setShowJobDescriptionInput(true);
    } else {
      // For quick analysis, show results immediately
      setShowAnalysis(true);
    }

    // If it's a full analysis, populate form data (even with minimal data)
    if (analysisData.analysisType === 'full' && analysisData.extractedData) {
      const extracted = analysisData.extractedData;
      console.log('📋 Extracted data for form population:', extracted);

      // Update form data with extracted information
      setFormData(prevData => ({
        ...prevData,
        personal: {
          ...prevData.personal,
          firstName: extracted.personal?.firstName || prevData.personal.firstName,
          lastName: extracted.personal?.lastName || prevData.personal.lastName,
          email: extracted.personal?.email || prevData.personal.email,
          phone: extracted.personal?.phone || prevData.personal.phone,
          location: extracted.personal?.location || prevData.personal.location,
          linkedin: extracted.personal?.linkedin || prevData.personal.linkedin,
          portfolio: extracted.personal?.portfolio || prevData.personal.portfolio,
          summary: extracted.personal?.summary || prevData.personal.summary
        },
        education: extracted.education?.length > 0 ? extracted.education.map(edu => ({
          ...edu,
          id: edu.id || Date.now() + Math.random()
        })) : prevData.education,
        experience: extracted.experience?.length > 0 ? extracted.experience.map(exp => ({
          ...exp,
          id: exp.id || Date.now() + Math.random()
        })) : prevData.experience,
        skills: {
          technical: extracted.skills?.technical?.length > 0 ? extracted.skills.technical : prevData.skills.technical,
          languages: extracted.skills?.languages?.length > 0 ? extracted.skills.languages : prevData.skills.languages,
          certifications: extracted.skills?.certifications?.length > 0 ? extracted.skills.certifications : prevData.skills.certifications
        },
        projects: extracted.projects?.length > 0 ? extracted.projects.map(proj => ({
          ...proj,
          id: proj.id || Date.now() + Math.random()
        })) : prevData.projects
      }));

      // Set ATS score and suggestions
      if (analysisData.atsScore) {
        setAtsScore(analysisData.atsScore.overall);
        setSuggestions(analysisData.enhancements?.suggestions || analysisData.analysis?.recommendations || []);
      }

      console.log('✅ Form data updated with extracted information');
    } else if (analysisData.fallback) {
      console.log('⚠️ Using fallback data - minimal extraction');
      // Even with fallback, try to extract any available information
      if (analysisData.extractedData) {
        const extracted = analysisData.extractedData;
        setFormData(prevData => ({
          ...prevData,
          personal: {
            ...prevData.personal,
            summary: extracted.personal?.summary || 'Please add your professional summary here.'
          }
        }));
      }
    }
  };

  // Handle job description submission for targeted resume generation
  const handleJobDescriptionSubmit = async (jobData) => {
    if (!uploadAnalysis?.extractedData) {
      toast.error('No resume data available. Please upload a resume first.');
      return;
    }

    try {
      setIsGeneratingTargeted(true);
      console.log('🎯 Generating targeted resume with job data:', jobData);

      const response = await fetch('/api/generate-targeted-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          extractedResumeData: uploadAnalysis.extractedData,
          jobDescription: jobData.description,
          jobTitle: jobData.jobTitle,
          company: jobData.company
        }),
      });

      const data = await response.json();
      console.log('📊 Targeted resume response:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate targeted resume');
      }

      // Update form data with enhanced resume
      if (data.enhancedResume) {
        // Properly structure the enhanced resume data to match form data structure
        const enhancedFormData = {
          personal: {
            firstName: data.enhancedResume.personal?.firstName || '',
            lastName: data.enhancedResume.personal?.lastName || '',
            email: data.enhancedResume.personal?.email || '',
            phone: data.enhancedResume.personal?.phone || '',
            location: data.enhancedResume.personal?.location || '',
            linkedin: data.enhancedResume.personal?.linkedin || '',
            portfolio: data.enhancedResume.personal?.portfolio || '',
            summary: data.enhancedResume.personal?.summary || ''
          },
          education: data.enhancedResume.education?.length > 0
            ? data.enhancedResume.education.map(edu => ({
                id: edu.id || Date.now() + Math.random(),
                degree: edu.degree || '',
                institution: edu.institution || '',
                location: edu.location || '',
                startDate: edu.startDate || '',
                endDate: edu.endDate || '',
                gpa: edu.gpa || '',
                relevant: edu.relevant || ''
              }))
            : [{ id: 1, degree: "", institution: "", location: "", startDate: "", endDate: "", gpa: "", relevant: "" }],
          experience: data.enhancedResume.experience?.length > 0
            ? data.enhancedResume.experience.map(exp => ({
                id: exp.id || Date.now() + Math.random(),
                title: exp.title || '',
                company: exp.company || '',
                location: exp.location || '',
                startDate: exp.startDate || '',
                endDate: exp.endDate || '',
                current: exp.current || false,
                description: exp.description || (exp.achievements ? exp.achievements.join('\n') : '')
              }))
            : [{ id: 1, title: "", company: "", location: "", startDate: "", endDate: "", current: false, description: "" }],
          skills: {
            technical: data.enhancedResume.skills?.technical || [],
            languages: data.enhancedResume.skills?.languages || [],
            certifications: data.enhancedResume.skills?.certifications || []
          },
          projects: data.enhancedResume.projects?.length > 0
            ? data.enhancedResume.projects.map(proj => ({
                id: proj.id || Date.now() + Math.random(),
                name: proj.name || '',
                description: proj.description || '',
                technologies: proj.technologies || '',
                link: proj.link || ''
              }))
            : [{ id: 1, name: "", description: "", technologies: "", link: "" }]
        };

        setFormData(enhancedFormData);
        setTargetedResumeData(data);
        setAtsScore(data.atsScore?.overall || 85);
        setSuggestions(data.recommendations || []);

        // Show success and move to form editing
        setShowJobDescriptionInput(false);
        setCurrentStep(0);
        toast.success('Resume optimized for the target job!');
      }

    } catch (error) {
      console.error('Targeted resume generation error:', error);
      toast.error(error.message || 'Failed to generate targeted resume');
    } finally {
      setIsGeneratingTargeted(false);
    }
  };

  // Handle mode change
  const handleModeChange = (mode) => {
    setBuilderMode(mode);
    setUploadAnalysis(null);
    setShowAnalysis(false);
    setResumeGenerated(false);
    setCurrentStep(0);
    setShowJobDescriptionInput(false);
    setIsGeneratingTargeted(false);
    setTargetedResumeData(null);
  };

  // Reset to create mode
  const resetToCreateMode = () => {
    setBuilderMode('create');
    setUploadAnalysis(null);
    setShowAnalysis(false);
    setResumeGenerated(false);
    setCurrentStep(0);
    setFormData({
      personal: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        location: "",
        linkedin: "",
        portfolio: "",
        summary: ""
      },
      education: [{ id: 1, degree: "", institution: "", location: "", startDate: "", endDate: "", gpa: "", relevant: "" }],
      experience: [{ id: 1, title: "", company: "", location: "", startDate: "", endDate: "", current: false, description: "" }],
      skills: { technical: [], languages: [], certifications: [] },
      projects: [{ id: 1, name: "", description: "", technologies: "", link: "" }]
    });
  };

  // Floating background elements with deterministic values to avoid hydration mismatch
  const FloatingElements = () => {
    // Use deterministic values to avoid hydration mismatch
    const elements = [
      { width: 180, height: 160, left: 10, top: 20, duration: 15, x: 30, y: 40 },
      { width: 220, height: 190, left: 80, top: 60, duration: 18, x: -25, y: -30 },
      { width: 150, height: 140, left: 60, top: 80, duration: 12, x: 35, y: 25 },
      { width: 200, height: 170, left: 30, top: 40, duration: 20, x: -40, y: 35 },
      { width: 170, height: 200, left: 90, top: 10, duration: 16, x: 20, y: -45 },
      { width: 190, height: 150, left: 20, top: 70, duration: 14, x: -30, y: 20 },
      { width: 160, height: 180, left: 70, top: 30, duration: 22, x: 45, y: -25 },
      { width: 210, height: 160, left: 50, top: 90, duration: 17, x: -20, y: 30 }
    ];

    return (
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {elements.map((element, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full opacity-10 blur-xl"
            style={{
              backgroundColor: '#832ED3',
              width: element.width,
              height: element.height,
              left: element.left + '%',
              top: element.top + '%',
            }}
            animate={{
              x: [0, element.x, 0],
              y: [0, element.y, 0],
            }}
            transition={{
              duration: element.duration,
              repeat: Infinity,
              repeatType: 'reverse',
              ease: "easeInOut"
            }}
          />
        ))}
      </div>
    );
  };

  // Show upload analysis results for quick ATS check
  if (builderMode === 'analyze' && showAnalysis && uploadAnalysis) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
        </div>

        <FloatingElements />

        <div className="relative z-10 container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-center mb-8">
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
                ATS Analysis Results
              </h1>
              <p className="text-gray-300">
                Here's your comprehensive resume analysis and recommendations
              </p>
            </div>

            <ATSAnalysisDisplay
              analysisData={uploadAnalysis}
              analysisType="quick"
            />

            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
              <motion.button
                onClick={() => handleModeChange('upload')}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Upload className="h-5 w-5" />
                Enhance This Resume
              </motion.button>

              <motion.button
                onClick={resetToCreateMode}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <PlusCircle className="h-5 w-5" />
                Start Fresh
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  // Show success screen if resume is generated
  if (resumeGenerated && resumeData) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
        {/* Grid background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
        </div>

        <FloatingElements />

        <div className="relative z-10 container mx-auto px-4 py-16">
          <motion.div
            className="max-w-4xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Success Header */}
            <div className="mb-8">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6"
              >
                <Sparkles className="h-10 w-10 text-white" />
              </motion.div>

              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Resume Generated Successfully!
              </h1>
              <p className="text-xl text-gray-300">
                Your AI-optimized resume is ready for download
              </p>
            </div>

            {/* ATS Score Display */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* ATS Score Circle */}
              <motion.div
                className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <h3 className="text-xl font-semibold mb-6 text-center">ATS Compatibility Score</h3>
                <div className="flex justify-center">
                  <ATSScoreCircle score={atsScore || 75} size={150} />
                </div>
              </motion.div>

              {/* Suggestions */}
              <motion.div
                className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <h3 className="text-xl font-semibold mb-6">AI Recommendations</h3>
                <div className="space-y-3 text-left">
                  {suggestions.length > 0 ? (
                    suggestions.slice(0, 4).map((suggestion, index) => (
                      <motion.div
                        key={index}
                        className="flex items-start gap-3 p-3 bg-neural-purple/10 rounded-lg border border-neural-purple/20"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}
                      >
                        <div className="w-2 h-2 bg-neural-purple rounded-full mt-2 flex-shrink-0"></div>
                        <p className="text-sm text-gray-300">
                          {typeof suggestion === 'string' ? suggestion : suggestion.message || suggestion}
                        </p>
                      </motion.div>
                    ))
                  ) : (
                    <p className="text-gray-400 text-center">No specific recommendations at this time.</p>
                  )}
                </div>
              </motion.div>
            </div>

            {/* Action Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1 }}
            >
              <PDFDownload
                formData={formData}
                resumeData={resumeData}
                templateId={selectedTemplate}
              />

              <ViewResumeButton
                formData={formData}
                resumeData={resumeData}
                templateId={selectedTemplate}
              />

              <motion.button
                onClick={() => setShowPreview(!showPreview)}
                className="inline-flex items-center gap-2 px-8 py-4 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 text-neural-blue hover:text-white font-medium rounded-xl transition-all duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Eye className="h-5 w-5" />
                {showPreview ? 'Hide Preview' : 'View Preview'}
              </motion.button>

              <motion.button
                onClick={() => {
                  setResumeGenerated(false);
                  setResumeData(null);
                  setAtsScore(null);
                  setSuggestions([]);
                  setCurrentStep(0);
                }}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Wand2 className="h-5 w-5" />
                Create Another Resume
              </motion.button>
            </motion.div>

            {/* Resume Preview */}
            {showPreview && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mb-8"
              >
                <div className="max-w-4xl mx-auto">
                  <EnhancedResumePreview formData={formData} />
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-16">
      {/* Grid background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      <FloatingElements />
      
      <ProgressBar 
        isVisible={showProgressBar} 
        onComplete={handleProgressComplete} 
      />

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          className="text-center mb-8 md:mb-12 relative z-20"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center gap-3 mb-4 md:mb-6">
            <div className="relative">
              <SparklesIcon className="h-8 w-8 md:h-10 md:w-10 text-neural-pink animate-pulse" />
              <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
            </div>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white">
              AI Resume Builder
            </h1>
          </div>
          <p className="text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-4">
            Create professional, ATS-friendly resumes in minutes with our AI-powered builder
          </p>

          {/* Progress indicator - only show for create mode */}
          {builderMode === 'create' && (
            <div className="mt-6 flex items-center justify-center">
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-700">
                <span className="text-sm text-gray-400">Step {currentStep + 1} of {steps.length}</span>
              </div>
            </div>
          )}
        </motion.div>

        {/* Mode Toggle - only show if not in create mode or at step 0 */}
        {(builderMode !== 'create' || currentStep === 0) && !resumeGenerated && (
          <ResumeBuilderModeToggle
            currentMode={builderMode}
            onModeChange={handleModeChange}
          />
        )}

        {/* Upload Section */}
        {(builderMode === 'upload' || builderMode === 'analyze') && !showAnalysis && !showJobDescriptionInput && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto mb-8"
          >
            <ResumeUpload
              onAnalysisComplete={handleUploadAnalysis}
              analysisType={builderMode === 'analyze' ? 'quick' : 'full'}
            />
          </motion.div>
        )}

        {/* Job Description Input for Upload & Enhance */}
        {builderMode === 'upload' && showJobDescriptionInput && uploadAnalysis && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto mb-8"
          >
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Resume Uploaded Successfully!</h2>
              <p className="text-gray-300">Now provide the job description to create a targeted, ATS-optimized resume</p>
            </div>

            <JobDescriptionInput
              onJobDescriptionSubmit={handleJobDescriptionSubmit}
              isLoading={isGeneratingTargeted}
            />

            <div className="flex justify-center mt-6">
              <motion.button
                onClick={() => {
                  setShowJobDescriptionInput(false);
                  setCurrentStep(0);
                }}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Skip Job Targeting
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Show analysis results for upload mode */}
        {builderMode === 'upload' && showAnalysis && uploadAnalysis && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-4xl mx-auto mb-8"
          >
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Resume Analysis Complete</h2>
              <p className="text-gray-300">Review the extracted information and continue to enhance your resume</p>
            </div>

            <ATSAnalysisDisplay
              analysisData={uploadAnalysis}
              analysisType="full"
            />

            <div className="flex justify-center mt-6">
              <motion.button
                onClick={() => setCurrentStep(0)}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <ArrowRight className="h-5 w-5" />
                Continue to Form Editor
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Regular form flow - only show for create mode or after upload analysis */}
        {(builderMode === 'create' || (builderMode === 'upload' && currentStep >= 0 && !showJobDescriptionInput)) && !resumeGenerated && (
          <>
            {/* Progress Steps - only for create mode */}
            {builderMode === 'create' && (
              <div className="mb-8 md:mb-12">
                <div className="flex justify-center items-center space-x-2 md:space-x-4 overflow-x-auto pb-4 px-4">
                  {steps.map((step, index) => {
                    const Icon = step.icon;
                    const isActive = index === currentStep;
                    const isCompleted = index < currentStep;

                    return (
                      <motion.div
                        key={step.id}
                        className={`flex items-center space-x-2 px-3 md:px-4 py-2 md:py-3 rounded-full border transition-all duration-300 min-w-fit ${
                          isActive
                            ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25'
                            : isCompleted
                            ? 'bg-neural-purple/20 border-neural-purple/50'
                            : 'bg-gray-800/50 border-gray-700'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Icon className={`h-4 w-4 md:h-5 md:w-5 ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`} />
                        <span className={`text-xs md:text-sm font-medium hidden sm:block ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`}>
                          {step.title}
                        </span>
                        {/* Mobile step number */}
                        <span className={`text-xs font-medium sm:hidden ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`}>
                          {index + 1}
                        </span>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            )}



        {/* Main Content Area */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto">
          {/* Form Section */}
          <div className="xl:col-span-2">
            <motion.div
              className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/10 shadow-2xl"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {/* Form Header */}
              <div className="mb-6 md:mb-8">
                <div className="flex items-center gap-3 mb-2">
                  {React.createElement(steps[currentStep].icon, {
                    className: "h-6 w-6 md:h-7 md:w-7 text-neural-purple"
                  })}
                  <h2 className="text-xl md:text-2xl font-bold text-white">
                    {steps[currentStep].title}
                  </h2>
                </div>
                <p className="text-gray-400 text-sm md:text-base">
                  {steps[currentStep].description}
                </p>
              </div>

              <AnimatePresence mode="wait">
                {currentStep === 0 && <PersonalInfoForm formData={formData} updateFormData={updateFormData} atsAnalysis={atsAnalysis} />}
                {currentStep === 1 && <EducationForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} atsAnalysis={atsAnalysis} />}
                {currentStep === 2 && <ExperienceForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} atsAnalysis={atsAnalysis} />}
                {currentStep === 3 && <SkillsProjectsForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} atsAnalysis={atsAnalysis} />}
                {currentStep === 4 && <ReviewForm formData={formData} atsAnalysis={atsAnalysis} />}
              </AnimatePresence>
            </motion.div>
          </div>

          {/* Preview Section */}
          <div className="xl:col-span-1">
            <motion.div
              className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-4 md:p-6 border border-white/10 sticky top-4 md:top-8 shadow-2xl"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex items-center justify-between mb-4 md:mb-6">
                <h3 className="text-base md:text-lg font-semibold flex items-center gap-2">
                  <Eye className="h-4 w-4 md:h-5 md:w-5 text-neural-blue" />
                  Live Preview
                </h3>
                <motion.button
                  onClick={() => setShowPreview(!showPreview)}
                  className="px-3 py-1.5 md:px-4 md:py-2 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 rounded-lg text-neural-blue hover:text-white transition-all duration-300 text-sm font-medium"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {showPreview ? 'Hide' : 'Show'}
                </motion.button>
              </div>

              {showPreview ? (
                <div className="max-h-[600px] md:max-h-[700px] overflow-y-auto">
                  <EnhancedResumePreview formData={formData} />
                </div>
              ) : (
                <div className="text-center py-12 md:py-16 text-gray-400">
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Eye className="h-16 w-16 md:h-20 md:w-20 mx-auto mb-4 opacity-30" />
                    <p className="text-sm md:text-base mb-2">Preview your resume</p>
                    <p className="text-xs md:text-sm text-gray-500">Click "Show" to see live updates</p>
                  </motion.div>
                </div>
              )}
            </motion.div>

            {/* ATS Optimization Panel */}
            <motion.div
              className="mt-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <ClientOnly>
                <ATSOptimizationPanel
                  formData={formData}
                  atsScore={atsAnalysis.overallScore}
                  suggestions={atsAnalysis.recommendations}
                  realTimeAnalysis={atsAnalysis}
                />
              </ClientOnly>
            </motion.div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 md:mt-12 max-w-7xl mx-auto px-4">
          <motion.button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl transition-all duration-300 font-medium text-sm md:text-base w-full sm:w-auto ${
              currentStep === 0
                ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed border border-gray-700'
                : 'bg-gray-800/80 hover:bg-gray-700 text-white border border-gray-600 hover:border-gray-500 shadow-lg hover:shadow-xl'
            }`}
            whileHover={currentStep !== 0 ? { scale: 1.02 } : {}}
            whileTap={currentStep !== 0 ? { scale: 0.98 } : {}}
          >
            <ArrowLeft className="h-4 w-4 md:h-5 md:w-5" />
            Previous
          </motion.button>

          {/* Step indicator for mobile */}
          <div className="flex items-center gap-2 sm:hidden">
            <span className="text-sm text-gray-400">
              Step {currentStep + 1} of {steps.length}
            </span>
          </div>

          {currentStep === steps.length - 1 ? (
            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
              <motion.button
                onClick={() => setShowTemplateSelector(true)}
                className="flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl bg-gray-800/80 hover:bg-gray-700 text-white font-medium border border-gray-600 hover:border-gray-500 transition-all duration-300 shadow-lg hover:shadow-xl text-sm md:text-base"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Palette className="h-4 w-4 md:h-5 md:w-5" />
                Choose Template
              </motion.button>

              <motion.button
                onClick={generateResume}
                disabled={isGenerating}
                className="flex items-center gap-2 px-8 md:px-10 py-3 md:py-4 rounded-xl bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold hover:opacity-90 transition-all duration-300 disabled:opacity-50 shadow-lg hover:shadow-xl text-sm md:text-base"
                whileHover={!isGenerating ? { scale: 1.02 } : {}}
                whileTap={!isGenerating ? { scale: 0.98 } : {}}
              >
                <Sparkles className="h-4 w-4 md:h-5 md:w-5" />
                {isGenerating ? 'Generating...' : 'Generate Resume'}
              </motion.button>
            </div>
          ) : (
            <motion.button
              onClick={nextStep}
              className="flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl text-sm md:text-base w-full sm:w-auto"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Next
              <ArrowRight className="h-4 w-4 md:h-5 md:w-5" />
            </motion.button>
          )}
        </div>
        </>
        )}

        {/* Template Selector Modal */}
        {showTemplateSelector && (
          <TemplateSelector
            selectedTemplate={selectedTemplate}
            onTemplateSelect={setSelectedTemplate}
            onClose={() => setShowTemplateSelector(false)}
          />
        )}
      </div>
    </div>
  );
};

// Personal Information Form Component
const PersonalInfoForm = ({ formData, updateFormData, atsAnalysis }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6 md:space-y-8"
  >
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          First Name *
        </label>
        <input
          type="text"
          value={formData.personal.firstName}
          onChange={(e) => updateFormData('personal', 'firstName', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="Rahul"
        />
        <ClientOnly>
          <ATSFieldIndicator
            fieldName="firstName"
            value={formData.personal.firstName}
            analysis={atsAnalysis.fieldAnalysis?.firstName}
          />
        </ClientOnly>
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          Last Name *
        </label>
        <input
          type="text"
          value={formData.personal.lastName}
          onChange={(e) => updateFormData('personal', 'lastName', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="Sharma"
        />
        <ClientOnly>
          <ATSFieldIndicator
            fieldName="lastName"
            value={formData.personal.lastName}
            analysis={atsAnalysis?.fieldAnalysis?.lastName}
          />
        </ClientOnly>
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <Mail className="inline h-4 w-4 mr-1" />
          Email *
        </label>
        <input
          type="email"
          value={formData.personal.email}
          onChange={(e) => updateFormData('personal', 'email', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="<EMAIL>"
        />
        <ClientOnly>
          <ATSFieldIndicator
            fieldName="email"
            value={formData.personal.email}
            analysis={atsAnalysis?.fieldAnalysis?.email}
          />
        </ClientOnly>
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <Phone className="inline h-4 w-4 mr-1" />
          Phone
        </label>
        <input
          type="tel"
          value={formData.personal.phone}
          onChange={(e) => updateFormData('personal', 'phone', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="+91 98765 43210"
        />
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <MapPin className="inline h-4 w-4 mr-1" />
          Location
        </label>
        <input
          type="text"
          value={formData.personal.location}
          onChange={(e) => updateFormData('personal', 'location', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="Mumbai, Maharashtra"
        />
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <Link className="inline h-4 w-4 mr-1" />
          LinkedIn
        </label>
        <input
          type="url"
          value={formData.personal.linkedin}
          onChange={(e) => updateFormData('personal', 'linkedin', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="https://linkedin.com/in/rahulsharma"
        />
      </div>

      <div className="md:col-span-2">
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <Globe className="inline h-4 w-4 mr-1" />
          Portfolio/Website
        </label>
        <input
          type="url"
          value={formData.personal.portfolio}
          onChange={(e) => updateFormData('personal', 'portfolio', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="https://rahulsharma.dev"
        />
      </div>

      <div className="md:col-span-2">
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          Professional Summary
        </label>
        <textarea
          value={formData.personal.summary}
          onChange={(e) => updateFormData('personal', 'summary', e.target.value)}
          rows={4}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 resize-none transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="Experienced software engineer with 5+ years in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable solutions and leading cross-functional teams..."
        />
        <ClientOnly>
          <ATSFieldIndicator
            fieldName="summary"
            value={formData.personal.summary}
            analysis={atsAnalysis?.fieldAnalysis?.summary}
            showDetails={true}
          />
        </ClientOnly>

        {/* AI Content Suggestions for Summary */}
        <div className="mt-3">
          <AIContentSuggestions
            fieldType="summary"
            currentValue={formData.personal.summary}
            onSuggestionApply={(suggestion) => updateFormData('personal', 'summary', suggestion)}
            context={{
              firstName: formData.personal.firstName,
              lastName: formData.personal.lastName,
              experience: formData.experience,
              skills: formData.skills
            }}
          />
        </div>
      </div>
    </div>
  </motion.div>
);

// Education Form Component
const EducationForm = ({ formData, updateFormData, addArrayItem, removeArrayItem }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <GraduationCap className="h-6 w-6 text-neural-purple" />
        <h2 className="text-2xl font-bold">Education</h2>
      </div>
      <button
        onClick={() => addArrayItem('education', {
          degree: "",
          institution: "",
          location: "",
          startDate: "",
          endDate: "",
          gpa: "",
          relevant: ""
        })}
        className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors"
      >
        <Plus className="h-4 w-4" />
        Add Education
      </button>
    </div>

    <div className="space-y-6">
      {formData.education.map((edu, index) => (
        <div key={edu.id} className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Education {index + 1}</h3>
            {formData.education.length > 1 && (
              <button
                onClick={() => removeArrayItem('education', edu.id)}
                className="text-red-400 hover:text-red-300 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Degree *
              </label>
              <input
                type="text"
                value={edu.degree}
                onChange={(e) => updateFormData('education', 'degree', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="Bachelor of Science"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Institution *
              </label>
              <input
                type="text"
                value={edu.institution}
                onChange={(e) => updateFormData('education', 'institution', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="University of Technology"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Location
              </label>
              <input
                type="text"
                value={edu.location}
                onChange={(e) => updateFormData('education', 'location', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="New York, NY"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                GPA (Optional)
              </label>
              <input
                type="text"
                value={edu.gpa}
                onChange={(e) => updateFormData('education', 'gpa', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="3.8/4.0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Start Date
              </label>
              <input
                type="month"
                value={edu.startDate}
                onChange={(e) => updateFormData('education', 'startDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                End Date
              </label>
              <input
                type="month"
                value={edu.endDate}
                onChange={(e) => updateFormData('education', 'endDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Relevant Coursework/Achievements
              </label>
              <textarea
                value={edu.relevant}
                onChange={(e) => updateFormData('education', 'relevant', e.target.value, index)}
                rows={3}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
                placeholder="Relevant coursework, honors, achievements..."
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  </motion.div>
);



export default ResumeBuilder;