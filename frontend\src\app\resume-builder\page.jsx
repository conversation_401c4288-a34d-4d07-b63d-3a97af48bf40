"use client";
import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  User, 
  GraduationCap, 
  Briefcase, 
  Award,
  FileText, 
  Download,
  ArrowLeft,
  ArrowRight,
  Sparkles,
  Eye,
  Wand2,
  Plus,
  Trash2,
  MapPin,
  Calendar,
  Globe,
  Mail,
  Phone,
  Link
} from "lucide-react";
import { SparklesIcon } from '@heroicons/react/24/solid';
import { toast } from "react-hot-toast";
import ProgressBar from "@/components/ProgressBar";
import ATSScoreCircle, { ATSScoreLoading } from "@/components/ATSScoreCircle";
// import SuccessScreen from "@/components/SuccessScreen";
import { ExperienceForm, SkillsProjectsForm, ReviewForm } from "@/components/ResumeFormComponents";

const ResumeBuilder = () => {
  // Main state management
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showProgressBar, setShowProgressBar] = useState(false);
  const [resumeGenerated, setResumeGenerated] = useState(false);
  const [resumeUrl, setResumeUrl] = useState("");
  const [resumeData, setResumeData] = useState(null);
  const [atsScore, setAtsScore] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [showPreview, setShowPreview] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    personal: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      location: "",
      linkedin: "",
      portfolio: "",
      summary: ""
    },
    education: [
      {
        id: 1,
        degree: "",
        institution: "",
        location: "",
        startDate: "",
        endDate: "",
        gpa: "",
        relevant: ""
      }
    ],
    experience: [
      {
        id: 1,
        title: "",
        company: "",
        location: "",
        startDate: "",
        endDate: "",
        current: false,
        description: ""
      }
    ],
    skills: {
      technical: [],
      languages: [],
      certifications: []
    },
    projects: [
      {
        id: 1,
        name: "",
        description: "",
        technologies: "",
        link: ""
      }
    ]
  });

  // Steps configuration
  const steps = [
    {
      id: 0,
      title: "Personal Information",
      icon: User,
      description: "Tell us about yourself"
    },
    {
      id: 1,
      title: "Education",
      icon: GraduationCap,
      description: "Your academic background"
    },
    {
      id: 2,
      title: "Experience",
      icon: Briefcase,
      description: "Your work experience"
    },
    {
      id: 3,
      title: "Skills & Projects",
      icon: Award,
      description: "Showcase your abilities"
    },
    {
      id: 4,
      title: "Review & Generate",
      icon: FileText,
      description: "Finalize your resume"
    }
  ];

  // Update form data
  const updateFormData = (section, field, value, index = null) => {
    setFormData(prev => {
      if (index !== null && Array.isArray(prev[section])) {
        const newArray = [...prev[section]];
        newArray[index] = { ...newArray[index], [field]: value };
        return { ...prev, [section]: newArray };
      } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {
        return {
          ...prev,
          [section]: { ...prev[section], [field]: value }
        };
      }
      return prev;
    });
  };

  // Add new item to array sections
  const addArrayItem = (section, template) => {
    setFormData(prev => ({
      ...prev,
      [section]: [...prev[section], { ...template, id: Math.random().toString(36).substring(2, 11) }]
    }));
  };

  // Remove item from array sections
  const removeArrayItem = (section, id) => {
    setFormData(prev => ({
      ...prev,
      [section]: prev[section].filter(item => item.id !== id)
    }));
  };

  // Navigation functions
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Generate resume with AI
  const generateResume = async () => {
    try {
      console.log('🚀 Starting resume generation...');
      console.log('📝 Form data:', formData);

      setIsGenerating(true);
      setShowProgressBar(true);

      const response = await fetch('/api/generate-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      console.log('📡 API response status:', response.status);
      console.log('📡 API response headers:', Object.fromEntries(response.headers.entries()));

      // Check if response is actually JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        console.error('❌ Non-JSON response received:', textResponse);
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();
      console.log('📊 API response data:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate resume');
      }

      // Store the enhanced resume data and ATS information
      console.log('✅ Setting resume data...');
      setResumeUrl(data.downloadUrl);
      setResumeData(data.resumeData);
      setAtsScore(data.atsScore || data.resumeData?.atsScore?.overall || 75);
      setSuggestions(data.suggestions || data.resumeData?.atsScore?.improvements || []);

      console.log('🎉 Resume generation completed successfully');

    } catch (error) {
      console.error("💥 Error generating resume:", error);
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      toast.error(error.message || "Failed to generate resume");
      setIsGenerating(false);
      setShowProgressBar(false);
    }
  };

  const handleProgressComplete = () => {
    setShowProgressBar(false);
    setResumeGenerated(true);
    setIsGenerating(false);

    // Show success message with ATS score
    const scoreMessage = atsScore
      ? `Resume generated! ATS Score: ${atsScore}%`
      : "Your resume has been generated successfully!";
    toast.success(scoreMessage);
  };

  // Floating background elements
  const FloatingElements = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full opacity-10 blur-xl"
          style={{
            backgroundColor: '#832ED3',
            width: Math.random() * 200 + 100,
            height: Math.random() * 200 + 100,
            left: Math.random() * 100 + '%',
            top: Math.random() * 100 + '%',
          }}
          animate={{
            x: [0, Math.random() * 100 - 50, 0],
            y: [0, Math.random() * 100 - 50, 0],
            transition: {
              duration: Math.random() * 20 + 20,
              repeat: Infinity,
              repeatType: 'reverse',
            },
          }}
        />
      ))}
    </div>
  );

  // Show success screen if resume is generated
  if (resumeGenerated && resumeData) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
        {/* Grid background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
        </div>

        <FloatingElements />

        <div className="relative z-10 container mx-auto px-4 py-16">
          <motion.div
            className="max-w-4xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Success Header */}
            <div className="mb-8">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6"
              >
                <Sparkles className="h-10 w-10 text-white" />
              </motion.div>

              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Resume Generated Successfully!
              </h1>
              <p className="text-xl text-gray-300">
                Your AI-optimized resume is ready for download
              </p>
            </div>

            {/* ATS Score Display */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* ATS Score Circle */}
              <motion.div
                className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <h3 className="text-xl font-semibold mb-6 text-center">ATS Compatibility Score</h3>
                <div className="flex justify-center">
                  <ATSScoreCircle score={atsScore || 75} size={150} />
                </div>
              </motion.div>

              {/* Suggestions */}
              <motion.div
                className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <h3 className="text-xl font-semibold mb-6">AI Recommendations</h3>
                <div className="space-y-3 text-left">
                  {suggestions.length > 0 ? (
                    suggestions.slice(0, 4).map((suggestion, index) => (
                      <motion.div
                        key={index}
                        className="flex items-start gap-3 p-3 bg-neural-purple/10 rounded-lg border border-neural-purple/20"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}
                      >
                        <div className="w-2 h-2 bg-neural-purple rounded-full mt-2 flex-shrink-0"></div>
                        <p className="text-sm text-gray-300">{suggestion}</p>
                      </motion.div>
                    ))
                  ) : (
                    <p className="text-gray-400 text-center">No specific recommendations at this time.</p>
                  )}
                </div>
              </motion.div>
            </div>

            {/* Action Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1 }}
            >
              <motion.a
                href={resumeUrl}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Download className="h-5 w-5" />
                Download Resume
              </motion.a>

              <motion.button
                onClick={() => {
                  setResumeGenerated(false);
                  setResumeData(null);
                  setResumeUrl("");
                  setAtsScore(null);
                  setSuggestions([]);
                  setCurrentStep(0);
                }}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Wand2 className="h-5 w-5" />
                Create Another Resume
              </motion.button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-16">
      {/* Grid background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      <FloatingElements />
      
      <ProgressBar 
        isVisible={showProgressBar} 
        onComplete={handleProgressComplete} 
      />

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          className="text-center mb-8 md:mb-12 relative z-20"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center gap-3 mb-4 md:mb-6">
            <div className="relative">
              <SparklesIcon className="h-8 w-8 md:h-10 md:w-10 text-neural-pink animate-pulse" />
              <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
            </div>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white">
              AI Resume Builder
            </h1>
          </div>
          <p className="text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-4">
            Create professional, ATS-friendly resumes in minutes with our AI-powered builder
          </p>

          {/* Progress indicator */}
          <div className="mt-6 flex items-center justify-center">
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-700">
              <span className="text-sm text-gray-400">Step {currentStep + 1} of {steps.length}</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Steps */}
        <div className="mb-8 md:mb-12">
          <div className="flex justify-center items-center space-x-2 md:space-x-4 overflow-x-auto pb-4 px-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;

              return (
                <motion.div
                  key={step.id}
                  className={`flex items-center space-x-2 px-3 md:px-4 py-2 md:py-3 rounded-full border transition-all duration-300 min-w-fit ${
                    isActive
                      ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25'
                      : isCompleted
                      ? 'bg-neural-purple/20 border-neural-purple/50'
                      : 'bg-gray-800/50 border-gray-700'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Icon className={`h-4 w-4 md:h-5 md:w-5 ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`} />
                  <span className={`text-xs md:text-sm font-medium hidden sm:block ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`}>
                    {step.title}
                  </span>
                  {/* Mobile step number */}
                  <span className={`text-xs font-medium sm:hidden ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`}>
                    {index + 1}
                  </span>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Main Content Area */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto">
          {/* Form Section */}
          <div className="xl:col-span-2">
            <motion.div
              className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/10 shadow-2xl"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {/* Form Header */}
              <div className="mb-6 md:mb-8">
                <div className="flex items-center gap-3 mb-2">
                  {React.createElement(steps[currentStep].icon, {
                    className: "h-6 w-6 md:h-7 md:w-7 text-neural-purple"
                  })}
                  <h2 className="text-xl md:text-2xl font-bold text-white">
                    {steps[currentStep].title}
                  </h2>
                </div>
                <p className="text-gray-400 text-sm md:text-base">
                  {steps[currentStep].description}
                </p>
              </div>

              <AnimatePresence mode="wait">
                {currentStep === 0 && <PersonalInfoForm formData={formData} updateFormData={updateFormData} />}
                {currentStep === 1 && <EducationForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} />}
                {currentStep === 2 && <ExperienceForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} />}
                {currentStep === 3 && <SkillsProjectsForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} />}
                {currentStep === 4 && <ReviewForm formData={formData} />}
              </AnimatePresence>
            </motion.div>
          </div>

          {/* Preview Section */}
          <div className="xl:col-span-1">
            <motion.div
              className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-4 md:p-6 border border-white/10 sticky top-4 md:top-8 shadow-2xl"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex items-center justify-between mb-4 md:mb-6">
                <h3 className="text-base md:text-lg font-semibold flex items-center gap-2">
                  <Eye className="h-4 w-4 md:h-5 md:w-5 text-neural-blue" />
                  Live Preview
                </h3>
                <motion.button
                  onClick={() => setShowPreview(!showPreview)}
                  className="px-3 py-1.5 md:px-4 md:py-2 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 rounded-lg text-neural-blue hover:text-white transition-all duration-300 text-sm font-medium"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {showPreview ? 'Hide' : 'Show'}
                </motion.button>
              </div>

              {showPreview ? (
                <div className="max-h-[600px] md:max-h-[700px] overflow-y-auto">
                  <ResumePreview formData={formData} />
                </div>
              ) : (
                <div className="text-center py-12 md:py-16 text-gray-400">
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Eye className="h-16 w-16 md:h-20 md:w-20 mx-auto mb-4 opacity-30" />
                    <p className="text-sm md:text-base mb-2">Preview your resume</p>
                    <p className="text-xs md:text-sm text-gray-500">Click "Show" to see live updates</p>
                  </motion.div>
                </div>
              )}
            </motion.div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 md:mt-12 max-w-7xl mx-auto px-4">
          <motion.button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl transition-all duration-300 font-medium text-sm md:text-base w-full sm:w-auto ${
              currentStep === 0
                ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed border border-gray-700'
                : 'bg-gray-800/80 hover:bg-gray-700 text-white border border-gray-600 hover:border-gray-500 shadow-lg hover:shadow-xl'
            }`}
            whileHover={currentStep !== 0 ? { scale: 1.02 } : {}}
            whileTap={currentStep !== 0 ? { scale: 0.98 } : {}}
          >
            <ArrowLeft className="h-4 w-4 md:h-5 md:w-5" />
            Previous
          </motion.button>

          {/* Step indicator for mobile */}
          <div className="flex items-center gap-2 sm:hidden">
            <span className="text-sm text-gray-400">
              Step {currentStep + 1} of {steps.length}
            </span>
          </div>

          {currentStep === steps.length - 1 ? (
            <motion.button
              onClick={generateResume}
              disabled={isGenerating}
              className="flex items-center gap-2 px-8 md:px-10 py-3 md:py-4 rounded-xl bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold hover:opacity-90 transition-all duration-300 disabled:opacity-50 shadow-lg hover:shadow-xl text-sm md:text-base w-full sm:w-auto"
              whileHover={!isGenerating ? { scale: 1.02 } : {}}
              whileTap={!isGenerating ? { scale: 0.98 } : {}}
            >
              <Sparkles className="h-4 w-4 md:h-5 md:w-5" />
              {isGenerating ? 'Generating...' : 'Generate Resume'}
            </motion.button>
          ) : (
            <motion.button
              onClick={nextStep}
              className="flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl text-sm md:text-base w-full sm:w-auto"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Next
              <ArrowRight className="h-4 w-4 md:h-5 md:w-5" />
            </motion.button>
          )}
        </div>
      </div>
    </div>
  );
};

// Personal Information Form Component
const PersonalInfoForm = ({ formData, updateFormData }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6 md:space-y-8"
  >
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          First Name *
        </label>
        <input
          type="text"
          value={formData.personal.firstName}
          onChange={(e) => updateFormData('personal', 'firstName', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="Rahul"
        />
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          Last Name *
        </label>
        <input
          type="text"
          value={formData.personal.lastName}
          onChange={(e) => updateFormData('personal', 'lastName', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="Sharma"
        />
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <Mail className="inline h-4 w-4 mr-1" />
          Email *
        </label>
        <input
          type="email"
          value={formData.personal.email}
          onChange={(e) => updateFormData('personal', 'email', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="<EMAIL>"
        />
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <Phone className="inline h-4 w-4 mr-1" />
          Phone
        </label>
        <input
          type="tel"
          value={formData.personal.phone}
          onChange={(e) => updateFormData('personal', 'phone', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="+91 98765 43210"
        />
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <MapPin className="inline h-4 w-4 mr-1" />
          Location
        </label>
        <input
          type="text"
          value={formData.personal.location}
          onChange={(e) => updateFormData('personal', 'location', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="Mumbai, Maharashtra"
        />
      </div>

      <div>
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <Link className="inline h-4 w-4 mr-1" />
          LinkedIn
        </label>
        <input
          type="url"
          value={formData.personal.linkedin}
          onChange={(e) => updateFormData('personal', 'linkedin', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="https://linkedin.com/in/rahulsharma"
        />
      </div>

      <div className="md:col-span-2">
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          <Globe className="inline h-4 w-4 mr-1" />
          Portfolio/Website
        </label>
        <input
          type="url"
          value={formData.personal.portfolio}
          onChange={(e) => updateFormData('personal', 'portfolio', e.target.value)}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="https://rahulsharma.dev"
        />
      </div>

      <div className="md:col-span-2">
        <label className="block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3">
          Professional Summary
        </label>
        <textarea
          value={formData.personal.summary}
          onChange={(e) => updateFormData('personal', 'summary', e.target.value)}
          rows={4}
          className="w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 resize-none transition-all duration-300 hover:border-gray-500 text-sm md:text-base"
          placeholder="Experienced software engineer with 5+ years in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable solutions and leading cross-functional teams..."
        />
      </div>
    </div>
  </motion.div>
);

// Education Form Component
const EducationForm = ({ formData, updateFormData, addArrayItem, removeArrayItem }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <GraduationCap className="h-6 w-6 text-neural-purple" />
        <h2 className="text-2xl font-bold">Education</h2>
      </div>
      <button
        onClick={() => addArrayItem('education', {
          degree: "",
          institution: "",
          location: "",
          startDate: "",
          endDate: "",
          gpa: "",
          relevant: ""
        })}
        className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors"
      >
        <Plus className="h-4 w-4" />
        Add Education
      </button>
    </div>

    <div className="space-y-6">
      {formData.education.map((edu, index) => (
        <div key={edu.id} className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Education {index + 1}</h3>
            {formData.education.length > 1 && (
              <button
                onClick={() => removeArrayItem('education', edu.id)}
                className="text-red-400 hover:text-red-300 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Degree *
              </label>
              <input
                type="text"
                value={edu.degree}
                onChange={(e) => updateFormData('education', 'degree', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="Bachelor of Science"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Institution *
              </label>
              <input
                type="text"
                value={edu.institution}
                onChange={(e) => updateFormData('education', 'institution', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="University of Technology"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Location
              </label>
              <input
                type="text"
                value={edu.location}
                onChange={(e) => updateFormData('education', 'location', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="New York, NY"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                GPA (Optional)
              </label>
              <input
                type="text"
                value={edu.gpa}
                onChange={(e) => updateFormData('education', 'gpa', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="3.8/4.0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Start Date
              </label>
              <input
                type="month"
                value={edu.startDate}
                onChange={(e) => updateFormData('education', 'startDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                End Date
              </label>
              <input
                type="month"
                value={edu.endDate}
                onChange={(e) => updateFormData('education', 'endDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Relevant Coursework/Achievements
              </label>
              <textarea
                value={edu.relevant}
                onChange={(e) => updateFormData('education', 'relevant', e.target.value, index)}
                rows={3}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
                placeholder="Relevant coursework, honors, achievements..."
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  </motion.div>
);

// Resume Preview Component
const ResumePreview = ({ formData }) => (
  <div className="bg-white text-black p-6 rounded-lg text-xs max-h-96 overflow-y-auto">
    {/* Header */}
    <div className="text-center border-b border-gray-300 pb-4 mb-4">
      <h1 className="text-xl font-bold text-gray-900">
        {formData.personal.firstName} {formData.personal.lastName}
      </h1>
      <div className="flex flex-wrap justify-center gap-2 mt-2 text-gray-600">
        {formData.personal.email && <span>{formData.personal.email}</span>}
        {formData.personal.phone && <span>•</span>}
        {formData.personal.phone && <span>{formData.personal.phone}</span>}
        {formData.personal.location && <span>•</span>}
        {formData.personal.location && <span>{formData.personal.location}</span>}
      </div>
      {formData.personal.linkedin && (
        <div className="mt-1 text-blue-600">{formData.personal.linkedin}</div>
      )}
    </div>

    {/* Summary */}
    {formData.personal.summary && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          PROFESSIONAL SUMMARY
        </h2>
        <p className="text-gray-700 leading-relaxed">{formData.personal.summary}</p>
      </div>
    )}

    {/* Experience */}
    {formData.experience.length > 0 && formData.experience[0].title && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          EXPERIENCE
        </h2>
        {formData.experience.map((exp) => (
          exp.title && (
            <div key={exp.id} className="mb-3">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-gray-900">{exp.title}</h3>
                  <p className="text-gray-700">{exp.company}</p>
                </div>
                <div className="text-right text-gray-600">
                  <p>{exp.startDate} - {exp.current ? 'Present' : exp.endDate}</p>
                  {exp.location && <p>{exp.location}</p>}
                </div>
              </div>
              {exp.description && (
                <div className="mt-1 text-gray-700 whitespace-pre-line">
                  {exp.description}
                </div>
              )}
            </div>
          )
        ))}
      </div>
    )}

    {/* Education */}
    {formData.education.length > 0 && formData.education[0].degree && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          EDUCATION
        </h2>
        {formData.education.map((edu) => (
          edu.degree && (
            <div key={edu.id} className="mb-2">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-gray-900">{edu.degree}</h3>
                  <p className="text-gray-700">{edu.institution}</p>
                </div>
                <div className="text-right text-gray-600">
                  <p>{edu.startDate} - {edu.endDate}</p>
                  {edu.location && <p>{edu.location}</p>}
                </div>
              </div>
              {edu.gpa && <p className="text-gray-600">GPA: {edu.gpa}</p>}
              {edu.relevant && <p className="text-gray-700 mt-1">{edu.relevant}</p>}
            </div>
          )
        ))}
      </div>
    )}

    {/* Skills */}
    {(formData.skills.technical.length > 0 || formData.skills.languages.length > 0) && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          SKILLS
        </h2>
        {formData.skills.technical.length > 0 && (
          <div className="mb-2">
            <span className="font-semibold text-gray-900">Technical: </span>
            <span className="text-gray-700">{formData.skills.technical.join(', ')}</span>
          </div>
        )}
        {formData.skills.languages.length > 0 && (
          <div className="mb-2">
            <span className="font-semibold text-gray-900">Languages: </span>
            <span className="text-gray-700">{formData.skills.languages.join(', ')}</span>
          </div>
        )}
        {formData.skills.certifications.length > 0 && (
          <div className="mb-2">
            <span className="font-semibold text-gray-900">Certifications: </span>
            <span className="text-gray-700">{formData.skills.certifications.join(', ')}</span>
          </div>
        )}
      </div>
    )}

    {/* Projects */}
    {formData.projects.length > 0 && formData.projects[0].name && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          PROJECTS
        </h2>
        {formData.projects.map((project) => (
          project.name && (
            <div key={project.id} className="mb-3">
              <div className="flex justify-between items-start">
                <h3 className="font-semibold text-gray-900">{project.name}</h3>
                {project.link && (
                  <a href={project.link} className="text-blue-600 hover:underline">
                    Link
                  </a>
                )}
              </div>
              {project.technologies && (
                <p className="text-gray-600 italic">{project.technologies}</p>
              )}
              {project.description && (
                <p className="text-gray-700 mt-1">{project.description}</p>
              )}
            </div>
          )
        ))}
      </div>
    )}

    {/* Empty state */}
    {!formData.personal.firstName && !formData.personal.lastName && (
      <div className="text-center py-8 text-gray-500">
        <FileText className="h-12 w-12 mx-auto mb-2" />
        <p>Start filling out the form to see your resume preview</p>
      </div>
    )}
  </div>
);

export default ResumeBuilder;