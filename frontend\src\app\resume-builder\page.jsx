"use client";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  User, 
  GraduationCap, 
  Briefcase, 
  Award, 
  FileText, 
  Download,
  ArrowLeft,
  ArrowRight,
  Sparkles,
  Eye,
  Wand2,
  Plus,
  Trash2,
  MapPin,
  Calendar,
  Globe,
  Mail,
  Phone,
  Link
} from "lucide-react";
import { SparklesIcon } from '@heroicons/react/24/solid';
import { toast } from "react-hot-toast";
import ProgressBar from "@/components/ProgressBar";
import SuccessScreen from "@/components/SuccessScreen";
import { ExperienceForm, SkillsProjectsForm, ReviewForm } from "@/components/ResumeFormComponents";

const ResumeBuilder = () => {
  // Main state management
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showProgressBar, setShowProgressBar] = useState(false);
  const [resumeGenerated, setResumeGenerated] = useState(false);
  const [resumeData, setResumeData] = useState(null);
  const [showPreview, setShowPreview] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    personal: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      location: "",
      linkedin: "",
      portfolio: "",
      summary: ""
    },
    education: [
      {
        id: 1,
        degree: "",
        institution: "",
        location: "",
        startDate: "",
        endDate: "",
        gpa: "",
        relevant: ""
      }
    ],
    experience: [
      {
        id: 1,
        title: "",
        company: "",
        location: "",
        startDate: "",
        endDate: "",
        current: false,
        description: ""
      }
    ],
    skills: {
      technical: [],
      languages: [],
      certifications: []
    },
    projects: [
      {
        id: 1,
        name: "",
        description: "",
        technologies: "",
        link: ""
      }
    ]
  });

  // Steps configuration
  const steps = [
    {
      id: 0,
      title: "Personal Information",
      icon: User,
      description: "Tell us about yourself"
    },
    {
      id: 1,
      title: "Education",
      icon: GraduationCap,
      description: "Your academic background"
    },
    {
      id: 2,
      title: "Experience",
      icon: Briefcase,
      description: "Your work experience"
    },
    {
      id: 3,
      title: "Skills & Projects",
      icon: Award,
      description: "Showcase your abilities"
    },
    {
      id: 4,
      title: "Review & Generate",
      icon: FileText,
      description: "Finalize your resume"
    }
  ];

  // Update form data
  const updateFormData = (section, field, value, index = null) => {
    setFormData(prev => {
      if (index !== null && Array.isArray(prev[section])) {
        const newArray = [...prev[section]];
        newArray[index] = { ...newArray[index], [field]: value };
        return { ...prev, [section]: newArray };
      } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {
        return {
          ...prev,
          [section]: { ...prev[section], [field]: value }
        };
      }
      return prev;
    });
  };

  // Add new item to array sections
  const addArrayItem = (section, template) => {
    setFormData(prev => ({
      ...prev,
      [section]: [...prev[section], { ...template, id: Math.random().toString(36).substring(2, 11) }]
    }));
  };

  // Remove item from array sections
  const removeArrayItem = (section, id) => {
    setFormData(prev => ({
      ...prev,
      [section]: prev[section].filter(item => item.id !== id)
    }));
  };

  // Navigation functions
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Generate resume with AI
  const generateResume = async () => {
    try {
      setIsGenerating(true);
      setShowProgressBar(true);

      const response = await fetch('/api/generate-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate resume');
      }

      // Store the resume data from the API response
      setResumeData(data);
      
    } catch (error) {
      console.error("Error generating resume:", error);
      toast.error(error.message || "Failed to generate resume");
      setIsGenerating(false);
      setShowProgressBar(false);
    }
  };

  const handleProgressComplete = () => {
    setShowProgressBar(false);
    setResumeGenerated(true);
    setIsGenerating(false);
    toast.success("Your resume has been generated successfully!");
  };

  // Success screen handlers
  const handleStartOver = () => {
    setResumeGenerated(false);
    setResumeData(null);
    setCurrentStep(0);
    setFormData({
      personal: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        location: "",
        linkedin: "",
        portfolio: "",
        summary: ""
      },
      education: [
        {
          id: 1,
          degree: "",
          institution: "",
          location: "",
          startDate: "",
          endDate: "",
          gpa: "",
          relevant: ""
        }
      ],
      experience: [
        {
          id: 1,
          title: "",
          company: "",
          location: "",
          startDate: "",
          endDate: "",
          current: false,
          description: ""
        }
      ],
      skills: {
        technical: [],
        languages: [],
        certifications: []
      },
      projects: [
        {
          id: 1,
          name: "",
          description: "",
          technologies: "",
          link: ""
        }
      ]
    });
  };

  const handleEditResume = () => {
    setResumeGenerated(false);
    setCurrentStep(4); // Go back to review step
  };

  // Simple background elements without window dependency
  const FloatingElements = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <div className="absolute top-20 left-20 w-32 h-32 bg-neural-purple opacity-10 rounded-full blur-xl animate-pulse" />
      <div className="absolute top-40 right-32 w-24 h-24 bg-neural-pink opacity-10 rounded-full blur-xl animate-pulse delay-1000" />
      <div className="absolute bottom-32 left-40 w-28 h-28 bg-neural-blue opacity-10 rounded-full blur-xl animate-pulse delay-2000" />
      <div className="absolute bottom-20 right-20 w-20 h-20 bg-neural-purple opacity-10 rounded-full blur-xl animate-pulse delay-3000" />
    </div>
  );

  // Show success screen if resume is generated
  if (resumeGenerated && resumeData) {
    return (
      <SuccessScreen
        formData={formData}
        resumeData={resumeData}
        onStartOver={handleStartOver}
        onEditResume={handleEditResume}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
      {/* Grid background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      <FloatingElements />
      
      <ProgressBar 
        isVisible={showProgressBar} 
        onComplete={handleProgressComplete} 
      />

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center gap-2 mb-4">
            <SparklesIcon className="h-8 w-8 text-neural-pink" />
            <h1 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink">
              AI Resume Builder
            </h1>
          </div>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Create a professional, ATS-friendly resume in minutes with our AI-powered builder
          </p>
        </motion.div>

        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex justify-center items-center space-x-4 overflow-x-auto pb-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;
              
              return (
                <motion.div
                  key={step.id}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-300 ${
                    isActive 
                      ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25' 
                      : isCompleted 
                      ? 'bg-neural-purple/20 border-neural-purple/50' 
                      : 'bg-gray-800/50 border-gray-700'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Icon className={`h-5 w-5 ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`} />
                  <span className={`text-sm font-medium hidden md:block ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`}>
                    {step.title}
                  </span>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Main Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {/* Form Section */}
          <div className="lg:col-span-2">
            <motion.div
              className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <AnimatePresence mode="wait">
                {currentStep === 0 && <PersonalInfoForm formData={formData} updateFormData={updateFormData} />}
                {currentStep === 1 && <EducationForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} />}
                {currentStep === 2 && <ExperienceForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} />}
                {currentStep === 3 && <SkillsProjectsForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} />}
                {currentStep === 4 && <ReviewForm formData={formData} />}
              </AnimatePresence>
            </motion.div>
          </div>

          {/* Preview Section */}
          <div className="lg:col-span-1">
            <motion.div
              className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10 sticky top-8"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Eye className="h-5 w-5 text-neural-blue" />
                  Live Preview
                </h3>
                <button
                  onClick={() => setShowPreview(!showPreview)}
                  className="text-neural-blue hover:text-neural-pink transition-colors"
                >
                  {showPreview ? 'Hide' : 'Show'}
                </button>
              </div>
              
              {showPreview ? (
                <ResumePreview formData={formData} />
              ) : (
                <div className="text-center py-8 text-gray-400">
                  <Eye className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Click "Show" to preview your resume</p>
                </div>
              )}
            </motion.div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8 max-w-7xl mx-auto">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`flex items-center gap-2 px-6 py-3 rounded-lg transition-all ${
              currentStep === 0 
                ? 'bg-gray-800 text-gray-500 cursor-not-allowed' 
                : 'bg-gray-800 hover:bg-gray-700 text-white'
            }`}
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </button>

          {currentStep === steps.length - 1 ? (
            <button
              onClick={generateResume}
              disabled={isGenerating}
              className="flex items-center gap-2 px-8 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity disabled:opacity-50"
            >
              <Sparkles className="h-4 w-4" />
              {isGenerating ? 'Generating...' : 'Generate Resume'}
            </button>
          ) : (
            <button
              onClick={nextStep}
              className="flex items-center gap-2 px-6 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity"
            >
              Next
              <ArrowRight className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Personal Information Form Component
const PersonalInfoForm = ({ formData, updateFormData }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="flex items-center gap-3 mb-6">
      <User className="h-6 w-6 text-neural-purple" />
      <h2 className="text-2xl font-bold">Personal Information</h2>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          First Name *
        </label>
        <input
          type="text"
          value={formData.personal.firstName}
          onChange={(e) => updateFormData('personal', 'firstName', e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
          placeholder="John"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Last Name *
        </label>
        <input
          type="text"
          value={formData.personal.lastName}
          onChange={(e) => updateFormData('personal', 'lastName', e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
          placeholder="Doe"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <Mail className="inline h-4 w-4 mr-1" />
          Email *
        </label>
        <input
          type="email"
          value={formData.personal.email}
          onChange={(e) => updateFormData('personal', 'email', e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
          placeholder="<EMAIL>"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <Phone className="inline h-4 w-4 mr-1" />
          Phone
        </label>
        <input
          type="tel"
          value={formData.personal.phone}
          onChange={(e) => updateFormData('personal', 'phone', e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
          placeholder="+****************"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <MapPin className="inline h-4 w-4 mr-1" />
          Location
        </label>
        <input
          type="text"
          value={formData.personal.location}
          onChange={(e) => updateFormData('personal', 'location', e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
          placeholder="New York, NY"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <Link className="inline h-4 w-4 mr-1" />
          LinkedIn
        </label>
        <input
          type="url"
          value={formData.personal.linkedin}
          onChange={(e) => updateFormData('personal', 'linkedin', e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
          placeholder="https://linkedin.com/in/johndoe"
        />
      </div>

      <div className="md:col-span-2">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <Globe className="inline h-4 w-4 mr-1" />
          Portfolio/Website
        </label>
        <input
          type="url"
          value={formData.personal.portfolio}
          onChange={(e) => updateFormData('personal', 'portfolio', e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
          placeholder="https://johndoe.com"
        />
      </div>

      <div className="md:col-span-2">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Professional Summary
        </label>
        <textarea
          value={formData.personal.summary}
          onChange={(e) => updateFormData('personal', 'summary', e.target.value)}
          rows={4}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
          placeholder="Brief overview of your professional background and key achievements..."
        />
      </div>
    </div>
  </motion.div>
);

// Education Form Component
const EducationForm = ({ formData, updateFormData, addArrayItem, removeArrayItem }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <GraduationCap className="h-6 w-6 text-neural-purple" />
        <h2 className="text-2xl font-bold">Education</h2>
      </div>
      <button
        onClick={() => addArrayItem('education', {
          degree: "",
          institution: "",
          location: "",
          startDate: "",
          endDate: "",
          gpa: "",
          relevant: ""
        })}
        className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors"
      >
        <Plus className="h-4 w-4" />
        Add Education
      </button>
    </div>

    <div className="space-y-6">
      {formData.education.map((edu, index) => (
        <div key={edu.id} className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Education {index + 1}</h3>
            {formData.education.length > 1 && (
              <button
                onClick={() => removeArrayItem('education', edu.id)}
                className="text-red-400 hover:text-red-300 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Degree *
              </label>
              <input
                type="text"
                value={edu.degree}
                onChange={(e) => updateFormData('education', 'degree', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="Bachelor of Science"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Institution *
              </label>
              <input
                type="text"
                value={edu.institution}
                onChange={(e) => updateFormData('education', 'institution', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="University of Technology"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Location
              </label>
              <input
                type="text"
                value={edu.location}
                onChange={(e) => updateFormData('education', 'location', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="New York, NY"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                GPA (Optional)
              </label>
              <input
                type="text"
                value={edu.gpa}
                onChange={(e) => updateFormData('education', 'gpa', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="3.8/4.0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Start Date
              </label>
              <input
                type="month"
                value={edu.startDate}
                onChange={(e) => updateFormData('education', 'startDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                End Date
              </label>
              <input
                type="month"
                value={edu.endDate}
                onChange={(e) => updateFormData('education', 'endDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Relevant Coursework/Achievements
              </label>
              <textarea
                value={edu.relevant}
                onChange={(e) => updateFormData('education', 'relevant', e.target.value, index)}
                rows={3}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
                placeholder="Relevant coursework, honors, achievements..."
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  </motion.div>
);

// Resume Preview Component
const ResumePreview = ({ formData }) => (
  <div className="bg-white text-black p-6 rounded-lg text-xs max-h-96 overflow-y-auto">
    {/* Header */}
    <div className="text-center border-b border-gray-300 pb-4 mb-4">
      <h1 className="text-xl font-bold text-gray-900">
        {formData.personal.firstName} {formData.personal.lastName}
      </h1>
      <div className="flex flex-wrap justify-center gap-2 mt-2 text-gray-600">
        {formData.personal.email && <span>{formData.personal.email}</span>}
        {formData.personal.phone && <span>•</span>}
        {formData.personal.phone && <span>{formData.personal.phone}</span>}
        {formData.personal.location && <span>•</span>}
        {formData.personal.location && <span>{formData.personal.location}</span>}
      </div>
      {formData.personal.linkedin && (
        <div className="mt-1 text-blue-600">{formData.personal.linkedin}</div>
      )}
    </div>

    {/* Summary */}
    {formData.personal.summary && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          PROFESSIONAL SUMMARY
        </h2>
        <p className="text-gray-700 leading-relaxed">{formData.personal.summary}</p>
      </div>
    )}

    {/* Experience */}
    {formData.experience.length > 0 && formData.experience[0].title && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          EXPERIENCE
        </h2>
        {formData.experience.map((exp) => (
          exp.title && (
            <div key={exp.id} className="mb-3">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-gray-900">{exp.title}</h3>
                  <p className="text-gray-700">{exp.company}</p>
                </div>
                <div className="text-right text-gray-600">
                  <p>{exp.startDate} - {exp.current ? 'Present' : exp.endDate}</p>
                  {exp.location && <p>{exp.location}</p>}
                </div>
              </div>
              {exp.description && (
                <div className="mt-1 text-gray-700 whitespace-pre-line">
                  {exp.description}
                </div>
              )}
            </div>
          )
        ))}
      </div>
    )}

    {/* Education */}
    {formData.education.length > 0 && formData.education[0].degree && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          EDUCATION
        </h2>
        {formData.education.map((edu) => (
          edu.degree && (
            <div key={edu.id} className="mb-2">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-gray-900">{edu.degree}</h3>
                  <p className="text-gray-700">{edu.institution}</p>
                </div>
                <div className="text-right text-gray-600">
                  <p>{edu.startDate} - {edu.endDate}</p>
                  {edu.location && <p>{edu.location}</p>}
                </div>
              </div>
              {edu.gpa && <p className="text-gray-600">GPA: {edu.gpa}</p>}
              {edu.relevant && <p className="text-gray-700 mt-1">{edu.relevant}</p>}
            </div>
          )
        ))}
      </div>
    )}

    {/* Skills */}
    {(formData.skills.technical.length > 0 || formData.skills.languages.length > 0) && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          SKILLS
        </h2>
        {formData.skills.technical.length > 0 && (
          <div className="mb-2">
            <span className="font-semibold text-gray-900">Technical: </span>
            <span className="text-gray-700">{formData.skills.technical.join(', ')}</span>
          </div>
        )}
        {formData.skills.languages.length > 0 && (
          <div className="mb-2">
            <span className="font-semibold text-gray-900">Languages: </span>
            <span className="text-gray-700">{formData.skills.languages.join(', ')}</span>
          </div>
        )}
        {formData.skills.certifications.length > 0 && (
          <div className="mb-2">
            <span className="font-semibold text-gray-900">Certifications: </span>
            <span className="text-gray-700">{formData.skills.certifications.join(', ')}</span>
          </div>
        )}
      </div>
    )}

    {/* Projects */}
    {formData.projects.length > 0 && formData.projects[0].name && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          PROJECTS
        </h2>
        {formData.projects.map((project) => (
          project.name && (
            <div key={project.id} className="mb-3">
              <div className="flex justify-between items-start">
                <h3 className="font-semibold text-gray-900">{project.name}</h3>
                {project.link && (
                  <a href={project.link} className="text-blue-600 hover:underline">
                    Link
                  </a>
                )}
              </div>
              {project.technologies && (
                <p className="text-gray-600 italic">{project.technologies}</p>
              )}
              {project.description && (
                <p className="text-gray-700 mt-1">{project.description}</p>
              )}
            </div>
          )
        ))}
      </div>
    )}

    {/* Empty state */}
    {!formData.personal.firstName && !formData.personal.lastName && (
      <div className="text-center py-8 text-gray-500">
        <FileText className="h-12 w-12 mx-auto mb-2" />
        <p>Start filling out the form to see your resume preview</p>
      </div>
    )}
  </div>
);

export default ResumeBuilder;
