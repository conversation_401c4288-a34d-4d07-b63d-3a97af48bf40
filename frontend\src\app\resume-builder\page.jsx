"use client";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  User, 
  GraduationCap, 
  Briefcase, 
  Award, 
  FileText, 
  Download,
  ArrowLeft,
  ArrowRight,
  Sparkles,
  Eye,
  Wand2,
  Plus,
  Trash2,
  MapPin,
  Calendar,
  Globe,
  Mail,
  Phone,
  Link
} from "lucide-react";
import { SparklesIcon } from '@heroicons/react/24/solid';
import { toast } from "react-hot-toast";
import ProgressBar from "@/components/ProgressBar";
import SuccessScreen from "@/components/SuccessScreen";
import { ExperienceForm, SkillsProjectsForm, ReviewForm } from "@/components/ResumeFormComponents";

const ResumeBuilder = () => {
  // Main state management
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showProgressBar, setShowProgressBar] = useState(false);
  const [resumeGenerated, setResumeGenerated] = useState(false);
  const [resumeData, setResumeData] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [idCounter, setIdCounter] = useState(10); // Start from 10 to avoid conflicts with initial IDs

  // Form data state
  const [formData, setFormData] = useState({
    personal: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      location: "",
      linkedin: "",
      portfolio: "",
      summary: ""
    },
    education: [
      {
        id: 1,
        degree: "",
        institution: "",
        location: "",
        startDate: "",
        endDate: "",
        gpa: "",
        relevant: ""
      }
    ],
    experience: [
      {
        id: 1,
        title: "",
        company: "",
        location: "",
        startDate: "",
        endDate: "",
        current: false,
        description: ""
      }
    ],
    skills: {
      technical: [],
      languages: [],
      certifications: []
    },
    projects: [
      {
        id: 1,
        name: "",
        description: "",
        technologies: "",
        link: ""
      }
    ]
  });

  // Steps configuration
  const steps = [
    {
      id: 0,
      title: "Personal Information",
      icon: User,
      description: "Tell us about yourself"
    },
    {
      id: 1,
      title: "Education",
      icon: GraduationCap,
      description: "Your academic background"
    },
    {
      id: 2,
      title: "Experience",
      icon: Briefcase,
      description: "Your work experience"
    },
    {
      id: 3,
      title: "Skills & Projects",
      icon: Award,
      description: "Showcase your abilities"
    },
    {
      id: 4,
      title: "Review & Generate",
      icon: FileText,
      description: "Finalize your resume"
    }
  ];

  // Update form data
  const updateFormData = (section, field, value, index = null) => {
    setFormData(prev => {
      if (index !== null && Array.isArray(prev[section])) {
        const newArray = [...prev[section]];
        newArray[index] = { ...newArray[index], [field]: value };
        return { ...prev, [section]: newArray };
      } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {
        return {
          ...prev,
          [section]: { ...prev[section], [field]: value }
        };
      }
      return prev;
    });
  };

  // Add new item to array sections
  const addArrayItem = (section, template) => {
    setIdCounter(prev => prev + 1);
    setFormData(prev => {
      const newId = idCounter + 1;
      return {
        ...prev,
        [section]: [...prev[section], { ...template, id: newId }]
      };
    });
  };

  // Remove item from array sections
  const removeArrayItem = (section, id) => {
    setFormData(prev => ({
      ...prev,
      [section]: prev[section].filter(item => item.id !== id)
    }));
  };

  // Navigation functions
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Generate resume with AI
  const generateResume = async () => {
    try {
      setIsGenerating(true);
      setShowProgressBar(true);

      const response = await fetch('/api/generate-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate resume');
      }

      // Store the resume data from the API response
      setResumeData(data);
      
    } catch (error) {
      console.error("Error generating resume:", error);

      // Enhanced error handling with specific messages
      let errorMessage = "Failed to generate resume. Please try again.";

      if (error.message.includes('Missing required')) {
        errorMessage = "Please fill in all required fields (First Name, Last Name, and Email).";
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (error.message.includes('API')) {
        errorMessage = "AI service temporarily unavailable. Using fallback generation.";
      }

      toast.error(errorMessage, {
        duration: 5000,
        style: {
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #ef4444',
        },
        iconTheme: {
          primary: '#ef4444',
          secondary: '#fff',
        },
      });

      setIsGenerating(false);
      setShowProgressBar(false);
    }
  };

  const handleProgressComplete = () => {
    setShowProgressBar(false);
    setResumeGenerated(true);
    setIsGenerating(false);

    // Enhanced success message
    toast.success("🎉 Resume Generated Successfully!", {
      duration: 4000,
      style: {
        background: '#1f2937',
        color: '#fff',
        border: '1px solid #10b981',
      },
      iconTheme: {
        primary: '#10b981',
        secondary: '#fff',
      },
    });
  };

  // Success screen handlers
  const handleStartOver = () => {
    setResumeGenerated(false);
    setResumeData(null);
    setCurrentStep(0);

    // Show confirmation message
    toast.success("Starting fresh! Ready to create a new resume.", {
      duration: 3000,
      style: {
        background: '#1f2937',
        color: '#fff',
        border: '1px solid #3b82f6',
      },
      iconTheme: {
        primary: '#3b82f6',
        secondary: '#fff',
      },
    });
    setFormData({
      personal: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        location: "",
        linkedin: "",
        portfolio: "",
        summary: ""
      },
      education: [
        {
          id: 1,
          degree: "",
          institution: "",
          location: "",
          startDate: "",
          endDate: "",
          gpa: "",
          relevant: ""
        }
      ],
      experience: [
        {
          id: 1,
          title: "",
          company: "",
          location: "",
          startDate: "",
          endDate: "",
          current: false,
          description: ""
        }
      ],
      skills: {
        technical: [],
        languages: [],
        certifications: []
      },
      projects: [
        {
          id: 1,
          name: "",
          description: "",
          technologies: "",
          link: ""
        }
      ]
    });
  };

  const handleEditResume = () => {
    setResumeGenerated(false);
    setCurrentStep(4); // Go back to review step

    // Show edit message
    toast.info("Back to editing mode. Make your changes and regenerate!", {
      duration: 3000,
      style: {
        background: '#1f2937',
        color: '#fff',
        border: '1px solid #f59e0b',
      },
      iconTheme: {
        primary: '#f59e0b',
        secondary: '#fff',
      },
    });
  };

  // Pure CSS floating background elements (no hydration issues)
  const FloatingElements = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Predefined floating elements with CSS animations */}
      <div className="absolute top-[10%] left-[15%] w-32 h-32 bg-neural-purple opacity-10 rounded-full blur-xl animate-float-1" />
      <div className="absolute top-[25%] right-[20%] w-24 h-24 bg-neural-pink opacity-10 rounded-full blur-xl animate-float-2" />
      <div className="absolute top-[45%] left-[10%] w-28 h-28 bg-neural-blue opacity-10 rounded-full blur-xl animate-float-3" />
      <div className="absolute top-[60%] right-[15%] w-20 h-20 bg-neural-purple opacity-10 rounded-full blur-xl animate-float-4" />
      <div className="absolute top-[80%] left-[25%] w-36 h-36 bg-neural-pink opacity-10 rounded-full blur-xl animate-float-5" />
      <div className="absolute top-[35%] right-[40%] w-22 h-22 bg-neural-blue opacity-10 rounded-full blur-xl animate-float-6" />
      <div className="absolute top-[70%] left-[60%] w-26 h-26 bg-neural-purple opacity-10 rounded-full blur-xl animate-float-7" />
      <div className="absolute top-[15%] right-[60%] w-30 h-30 bg-neural-pink opacity-10 rounded-full blur-xl animate-float-8" />
    </div>
  );

  // Show success screen if resume is generated
  if (resumeGenerated && resumeData) {
    return (
      <SuccessScreen
        formData={formData}
        resumeData={resumeData}
        onStartOver={handleStartOver}
        onEditResume={handleEditResume}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-20 md:pt-24">
      {/* Grid background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      <FloatingElements />
      
      <ProgressBar 
        isVisible={showProgressBar} 
        onComplete={handleProgressComplete} 
      />

      <div className="relative z-10 container mx-auto px-4 py-4 md:py-8">
        {/* Header */}
        <motion.div
          className="text-center mb-8 md:mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col sm:flex-row items-center justify-center gap-2 mb-4">
            <SparklesIcon className="h-6 w-6 sm:h-8 sm:w-8 text-neural-pink" />
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink text-center">
              AI Resume Builder
            </h1>
          </div>
          <p className="text-gray-300 text-sm sm:text-base md:text-lg max-w-2xl mx-auto px-4">
            Create a professional, ATS-friendly resume in minutes with our AI-powered builder
          </p>
        </motion.div>

        {/* Progress Steps */}
        <div className="mb-8 md:mb-12">
          <div className="flex justify-center items-center space-x-2 sm:space-x-4 overflow-x-auto pb-4 px-2">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;

              return (
                <motion.div
                  key={step.id}
                  className={`flex items-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 rounded-full border transition-all duration-300 min-w-fit ${
                    isActive
                      ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25'
                      : isCompleted
                      ? 'bg-neural-purple/20 border-neural-purple/50'
                      : 'bg-gray-800/50 border-gray-700'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Icon className={`h-4 w-4 sm:h-5 sm:w-5 ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`} />
                  <span className={`text-xs sm:text-sm font-medium hidden sm:block ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`}>
                    {step.title}
                  </span>
                  {/* Show step number on mobile */}
                  <span className={`text-xs font-medium sm:hidden ${isActive || isCompleted ? 'text-white' : 'text-gray-400'}`}>
                    {index + 1}
                  </span>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Main Content Area */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 md:gap-8 max-w-7xl mx-auto">
          {/* Form Section */}
          <div className="xl:col-span-2">
            <motion.div
              className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-white/10"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <AnimatePresence mode="wait">
                {currentStep === 0 && <PersonalInfoForm formData={formData} updateFormData={updateFormData} />}
                {currentStep === 1 && <EducationForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} />}
                {currentStep === 2 && <ExperienceForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} />}
                {currentStep === 3 && <SkillsProjectsForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} />}
                {currentStep === 4 && <ReviewForm formData={formData} />}
              </AnimatePresence>
            </motion.div>
          </div>

          {/* Preview Section */}
          <div className="xl:col-span-1 order-first xl:order-last">
            <motion.div
              className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-white/10 xl:sticky xl:top-8"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Eye className="h-5 w-5 text-neural-blue" />
                  Live Preview
                </h3>
                <button
                  onClick={() => setShowPreview(!showPreview)}
                  className="text-neural-blue hover:text-neural-pink transition-colors"
                >
                  {showPreview ? 'Hide' : 'Show'}
                </button>
              </div>
              
              {showPreview ? (
                <ResumePreview formData={formData} />
              ) : (
                <div className="text-center py-8 text-gray-400">
                  <Eye className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Click "Show" to preview your resume</p>
                </div>
              )}
            </motion.div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6 md:mt-8 max-w-7xl mx-auto px-4">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`flex items-center gap-2 px-4 sm:px-6 py-3 rounded-lg transition-all w-full sm:w-auto justify-center ${
              currentStep === 0
                ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                : 'bg-gray-800 hover:bg-gray-700 text-white'
            }`}
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm sm:text-base">Previous</span>
          </button>

          {currentStep === steps.length - 1 ? (
            <button
              onClick={generateResume}
              disabled={isGenerating}
              className="flex items-center gap-2 px-6 sm:px-8 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity disabled:opacity-50 w-full sm:w-auto justify-center"
            >
              <Sparkles className="h-4 w-4" />
              <span className="text-sm sm:text-base">{isGenerating ? 'Generating...' : 'Generate Resume'}</span>
            </button>
          ) : (
            <button
              onClick={nextStep}
              className="flex items-center gap-2 px-4 sm:px-6 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity w-full sm:w-auto justify-center"
            >
              <span className="text-sm sm:text-base">Next</span>
              <ArrowRight className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Personal Information Form Component
const PersonalInfoForm = ({ formData, updateFormData }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="flex items-center gap-3 mb-6">
      <User className="h-6 w-6 text-neural-purple" />
      <h2 className="text-2xl font-bold">Personal Information</h2>
    </div>

    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          First Name *
        </label>
        <input
          type="text"
          value={formData.personal.firstName}
          onChange={(e) => updateFormData('personal', 'firstName', e.target.value)}
          className="w-full px-3 py-2 md:px-4 md:py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 text-sm md:text-base"
          placeholder="Rahul"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Last Name *
        </label>
        <input
          type="text"
          value={formData.personal.lastName}
          onChange={(e) => updateFormData('personal', 'lastName', e.target.value)}
          className="w-full px-3 py-2 md:px-4 md:py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 text-sm md:text-base"
          placeholder="Sharma"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <Mail className="inline h-4 w-4 mr-1" />
          Email *
        </label>
        <input
          type="email"
          value={formData.personal.email}
          onChange={(e) => updateFormData('personal', 'email', e.target.value)}
          className="w-full px-3 py-2 md:px-4 md:py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 text-sm md:text-base"
          placeholder="<EMAIL>"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <Phone className="inline h-4 w-4 mr-1" />
          Phone
        </label>
        <input
          type="tel"
          value={formData.personal.phone}
          onChange={(e) => updateFormData('personal', 'phone', e.target.value)}
          className="w-full px-3 py-2 md:px-4 md:py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 text-sm md:text-base"
          placeholder="+91 98765 43210"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <MapPin className="inline h-4 w-4 mr-1" />
          Location
        </label>
        <input
          type="text"
          value={formData.personal.location}
          onChange={(e) => updateFormData('personal', 'location', e.target.value)}
          className="w-full px-3 py-2 md:px-4 md:py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 text-sm md:text-base"
          placeholder="Mumbai, Maharashtra"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <Link className="inline h-4 w-4 mr-1" />
          LinkedIn
        </label>
        <input
          type="url"
          value={formData.personal.linkedin}
          onChange={(e) => updateFormData('personal', 'linkedin', e.target.value)}
          className="w-full px-3 py-2 md:px-4 md:py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 text-sm md:text-base"
          placeholder="https://linkedin.com/in/rahulsharma"
        />
      </div>

      <div className="sm:col-span-2">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <Globe className="inline h-4 w-4 mr-1" />
          Portfolio/Website
        </label>
        <input
          type="url"
          value={formData.personal.portfolio}
          onChange={(e) => updateFormData('personal', 'portfolio', e.target.value)}
          className="w-full px-3 py-2 md:px-4 md:py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 text-sm md:text-base"
          placeholder="https://rahulsharma.dev"
        />
      </div>

      <div className="sm:col-span-2">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Professional Summary
        </label>
        <textarea
          value={formData.personal.summary}
          onChange={(e) => updateFormData('personal', 'summary', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 md:px-4 md:py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none text-sm md:text-base"
          placeholder="Experienced software engineer with expertise in full-stack development and passion for innovative solutions..."
        />
      </div>
    </div>
  </motion.div>
);

// Education Form Component
const EducationForm = ({ formData, updateFormData, addArrayItem, removeArrayItem }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
      <div className="flex items-center gap-3">
        <GraduationCap className="h-5 w-5 sm:h-6 sm:w-6 text-neural-purple" />
        <h2 className="text-xl sm:text-2xl font-bold">Education</h2>
      </div>
      <button
        onClick={() => addArrayItem('education', {
          degree: "",
          institution: "",
          location: "",
          startDate: "",
          endDate: "",
          gpa: "",
          relevant: ""
        })}
        className="flex items-center gap-2 px-3 py-2 sm:px-4 sm:py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors text-sm sm:text-base"
      >
        <Plus className="h-4 w-4" />
        <span className="hidden sm:inline">Add Education</span>
        <span className="sm:hidden">Add</span>
      </button>
    </div>

    <div className="space-y-6">
      {formData.education.map((edu, index) => (
        <div key={edu.id} className="bg-gray-800/30 p-4 md:p-6 rounded-lg border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-base md:text-lg font-semibold">Education {index + 1}</h3>
            {formData.education.length > 1 && (
              <button
                onClick={() => removeArrayItem('education', edu.id)}
                className="text-red-400 hover:text-red-300 transition-colors p-1"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Degree *
              </label>
              <input
                type="text"
                value={edu.degree}
                onChange={(e) => updateFormData('education', 'degree', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="Bachelor of Technology (B.Tech)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Institution *
              </label>
              <input
                type="text"
                value={edu.institution}
                onChange={(e) => updateFormData('education', 'institution', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="Indian Institute of Technology (IIT)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Location
              </label>
              <input
                type="text"
                value={edu.location}
                onChange={(e) => updateFormData('education', 'location', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="Delhi, India"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                GPA (Optional)
              </label>
              <input
                type="text"
                value={edu.gpa}
                onChange={(e) => updateFormData('education', 'gpa', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="8.5/10.0 CGPA"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Start Date
              </label>
              <input
                type="month"
                value={edu.startDate}
                onChange={(e) => updateFormData('education', 'startDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                End Date
              </label>
              <input
                type="month"
                value={edu.endDate}
                onChange={(e) => updateFormData('education', 'endDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Relevant Coursework/Achievements
              </label>
              <textarea
                value={edu.relevant}
                onChange={(e) => updateFormData('education', 'relevant', e.target.value, index)}
                rows={3}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
                placeholder="Data Structures, Algorithms, Machine Learning, Dean's List, Technical Society Member..."
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  </motion.div>
);

// Resume Preview Component
const ResumePreview = ({ formData }) => (
  <div className="bg-white text-black p-6 rounded-lg text-xs max-h-96 overflow-y-auto">
    {/* Header */}
    <div className="text-center border-b border-gray-300 pb-4 mb-4">
      <h1 className="text-xl font-bold text-gray-900">
        {formData.personal.firstName} {formData.personal.lastName}
      </h1>
      <div className="flex flex-wrap justify-center gap-2 mt-2 text-gray-600">
        {formData.personal.email && <span>{formData.personal.email}</span>}
        {formData.personal.phone && <span>•</span>}
        {formData.personal.phone && <span>{formData.personal.phone}</span>}
        {formData.personal.location && <span>•</span>}
        {formData.personal.location && <span>{formData.personal.location}</span>}
      </div>
      {formData.personal.linkedin && (
        <div className="mt-1 text-blue-600">{formData.personal.linkedin}</div>
      )}
    </div>

    {/* Summary */}
    {formData.personal.summary && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          PROFESSIONAL SUMMARY
        </h2>
        <p className="text-gray-700 leading-relaxed">{formData.personal.summary}</p>
      </div>
    )}

    {/* Experience */}
    {formData.experience.length > 0 && formData.experience[0].title && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          EXPERIENCE
        </h2>
        {formData.experience.map((exp) => (
          exp.title && (
            <div key={exp.id} className="mb-3">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-gray-900">{exp.title}</h3>
                  <p className="text-gray-700">{exp.company}</p>
                </div>
                <div className="text-right text-gray-600">
                  <p>{exp.startDate} - {exp.current ? 'Present' : exp.endDate}</p>
                  {exp.location && <p>{exp.location}</p>}
                </div>
              </div>
              {exp.description && (
                <div className="mt-1 text-gray-700 whitespace-pre-line">
                  {exp.description}
                </div>
              )}
            </div>
          )
        ))}
      </div>
    )}

    {/* Education */}
    {formData.education.length > 0 && formData.education[0].degree && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          EDUCATION
        </h2>
        {formData.education.map((edu) => (
          edu.degree && (
            <div key={edu.id} className="mb-2">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-gray-900">{edu.degree}</h3>
                  <p className="text-gray-700">{edu.institution}</p>
                </div>
                <div className="text-right text-gray-600">
                  <p>{edu.startDate} - {edu.endDate}</p>
                  {edu.location && <p>{edu.location}</p>}
                </div>
              </div>
              {edu.gpa && <p className="text-gray-600">GPA: {edu.gpa}</p>}
              {edu.relevant && <p className="text-gray-700 mt-1">{edu.relevant}</p>}
            </div>
          )
        ))}
      </div>
    )}

    {/* Skills */}
    {(formData.skills.technical.length > 0 || formData.skills.languages.length > 0) && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          SKILLS
        </h2>
        {formData.skills.technical.length > 0 && (
          <div className="mb-2">
            <span className="font-semibold text-gray-900">Technical: </span>
            <span className="text-gray-700">{formData.skills.technical.join(', ')}</span>
          </div>
        )}
        {formData.skills.languages.length > 0 && (
          <div className="mb-2">
            <span className="font-semibold text-gray-900">Languages: </span>
            <span className="text-gray-700">{formData.skills.languages.join(', ')}</span>
          </div>
        )}
        {formData.skills.certifications.length > 0 && (
          <div className="mb-2">
            <span className="font-semibold text-gray-900">Certifications: </span>
            <span className="text-gray-700">{formData.skills.certifications.join(', ')}</span>
          </div>
        )}
      </div>
    )}

    {/* Projects */}
    {formData.projects.length > 0 && formData.projects[0].name && (
      <div className="mb-4">
        <h2 className="text-sm font-bold text-gray-900 border-b border-gray-300 mb-2">
          PROJECTS
        </h2>
        {formData.projects.map((project) => (
          project.name && (
            <div key={project.id} className="mb-3">
              <div className="flex justify-between items-start">
                <h3 className="font-semibold text-gray-900">{project.name}</h3>
                {project.link && (
                  <a href={project.link} className="text-blue-600 hover:underline">
                    Link
                  </a>
                )}
              </div>
              {project.technologies && (
                <p className="text-gray-600 italic">{project.technologies}</p>
              )}
              {project.description && (
                <p className="text-gray-700 mt-1">{project.description}</p>
              )}
            </div>
          )
        ))}
      </div>
    )}

    {/* Empty state */}
    {!formData.personal.firstName && !formData.personal.lastName && (
      <div className="text-center py-8 text-gray-500">
        <FileText className="h-12 w-12 mx-auto mb-2" />
        <p>Start filling out the form to see your resume preview</p>
      </div>
    )}
  </div>
);

export default ResumeBuilder;
