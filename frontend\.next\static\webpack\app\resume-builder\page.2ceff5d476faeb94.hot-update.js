"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* harmony import */ var _components_SuccessScreen__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SuccessScreen */ \"(app-pages-browser)/./src/components/SuccessScreen.jsx\");\n/* harmony import */ var _components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ResumeFormComponents */ \"(app-pages-browser)/./src/components/ResumeFormComponents.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ResumeBuilder = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    // Main state management\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ]\n    });\n    // Steps configuration\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Tell us about yourself\"\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Your academic background\"\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Your work experience\"\n        },\n        {\n            id: 3,\n            title: \"Skills & Projects\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Showcase your abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Finalize your resume\"\n        }\n    ];\n    // Update form data\n    const updateFormData = function(section, field, value) {\n        let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        setFormData((prev)=>{\n            if (index !== null && Array.isArray(prev[section])) {\n                const newArray = [\n                    ...prev[section]\n                ];\n                newArray[index] = {\n                    ...newArray[index],\n                    [field]: value\n                };\n                return {\n                    ...prev,\n                    [section]: newArray\n                };\n            } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                return {\n                    ...prev,\n                    [section]: {\n                        ...prev[section],\n                        [field]: value\n                    }\n                };\n            }\n            return prev;\n        });\n    };\n    // Add new item to array sections\n    const addArrayItem = (section, template)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: [\n                    ...prev[section],\n                    {\n                        ...template,\n                        id: Math.random().toString(36).substring(2, 11)\n                    }\n                ]\n            }));\n    };\n    // Remove item from array sections\n    const removeArrayItem = (section, id)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: prev[section].filter((item)=>item.id !== id)\n            }));\n    };\n    // Navigation functions\n    const nextStep = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    // Generate resume with AI\n    const generateResume = async ()=>{\n        try {\n            setIsGenerating(true);\n            setShowProgressBar(true);\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            // Store the resume data from the API response\n            setResumeData(data);\n        } catch (error) {\n            console.error(\"Error generating resume:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || \"Failed to generate resume\");\n            setIsGenerating(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsGenerating(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Your resume has been generated successfully!\");\n    };\n    // Success screen handlers\n    const handleStartOver = ()=>{\n        setResumeGenerated(false);\n        setResumeData(null);\n        setCurrentStep(0);\n        setFormData({\n            personal: {\n                firstName: \"\",\n                lastName: \"\",\n                email: \"\",\n                phone: \"\",\n                location: \"\",\n                linkedin: \"\",\n                portfolio: \"\",\n                summary: \"\"\n            },\n            education: [\n                {\n                    id: 1,\n                    degree: \"\",\n                    institution: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    gpa: \"\",\n                    relevant: \"\"\n                }\n            ],\n            experience: [\n                {\n                    id: 1,\n                    title: \"\",\n                    company: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    current: false,\n                    description: \"\"\n                }\n            ],\n            skills: {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: [\n                {\n                    id: 1,\n                    name: \"\",\n                    description: \"\",\n                    technologies: \"\",\n                    link: \"\"\n                }\n            ]\n        });\n    };\n    const handleEditResume = ()=>{\n        setResumeGenerated(false);\n        setCurrentStep(4); // Go back to review step\n    };\n    // Enhanced floating background elements\n    const FloatingElements = ()=>{\n        _s1();\n        const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [elements, setElements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ResumeBuilder.FloatingElements.useEffect\": ()=>{\n                setMounted(true);\n                // Generate elements only on client side to avoid hydration issues\n                const newElements = Array.from({\n                    length: 8\n                }, {\n                    \"ResumeBuilder.FloatingElements.useEffect.newElements\": (_, i)=>({\n                            id: i,\n                            size: 80 + Math.random() * 120,\n                            initialX: Math.random() * 100,\n                            initialY: Math.random() * 100,\n                            duration: 15 + Math.random() * 25,\n                            delay: Math.random() * 5,\n                            color: [\n                                'neural-purple',\n                                'neural-pink',\n                                'neural-blue'\n                            ][Math.floor(Math.random() * 3)]\n                        })\n                }[\"ResumeBuilder.FloatingElements.useEffect.newElements\"]);\n                setElements(newElements);\n            }\n        }[\"ResumeBuilder.FloatingElements.useEffect\"], []);\n        if (!mounted) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: elements.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    className: \"absolute rounded-full bg-\".concat(element.color, \" opacity-10 blur-xl\"),\n                    style: {\n                        width: element.size,\n                        height: element.size,\n                        left: \"\".concat(element.initialX, \"%\"),\n                        top: \"\".concat(element.initialY, \"%\")\n                    },\n                    animate: {\n                        x: [\n                            0,\n                            100,\n                            -50,\n                            0\n                        ],\n                        y: [\n                            0,\n                            -100,\n                            50,\n                            0\n                        ],\n                        scale: [\n                            1,\n                            1.2,\n                            0.8,\n                            1\n                        ],\n                        opacity: [\n                            0.1,\n                            0.2,\n                            0.05,\n                            0.1\n                        ]\n                    },\n                    transition: {\n                        duration: element.duration,\n                        repeat: Infinity,\n                        delay: element.delay,\n                        ease: \"easeInOut\"\n                    }\n                }, element.id, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(FloatingElements, \"OR3uaTnixrLRKhvlgMvLSlkofQU=\");\n    // Show success screen if resume is generated\n    if (resumeGenerated && resumeData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SuccessScreen__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            formData: formData,\n            resumeData: resumeData,\n            onStartOver: handleStartOver,\n            onEditResume: handleEditResume\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 327,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-20 md:pt-24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-4 md:py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        className: \"text-center mb-8 md:mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row items-center justify-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-neural-pink\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink text-center\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-sm sm:text-base md:text-lg max-w-2xl mx-auto px-4\",\n                                children: \"Create a professional, ATS-friendly resume in minutes with our AI-powered builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 md:mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center space-x-2 sm:space-x-4 overflow-x-auto pb-4 px-2\",\n                            children: steps.map((step, index)=>{\n                                const Icon = step.icon;\n                                const isActive = index === currentStep;\n                                const isCompleted = index < currentStep;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"flex items-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 rounded-full border transition-all duration-300 min-w-fit \".concat(isActive ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25' : isCompleted ? 'bg-neural-purple/20 border-neural-purple/50' : 'bg-gray-800/50 border-gray-700'),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5 \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm font-medium hidden sm:block \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400'),\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-medium sm:hidden \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400'),\n                                            children: index + 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, step.id, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: [\n                                            currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonalInfoForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EducationForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.ExperienceForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.SkillsProjectsForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.ReviewForm, {\n                                                formData: formData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 39\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10 sticky top-8\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-neural-blue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Live Preview\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowPreview(!showPreview),\n                                                    className: \"text-neural-blue hover:text-neural-pink transition-colors\",\n                                                    children: showPreview ? 'Hide' : 'Show'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResumePreview, {\n                                            formData: formData\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: 'Click \"Show\" to preview your resume'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevStep,\n                                disabled: currentStep === 0,\n                                className: \"flex items-center gap-2 px-6 py-3 rounded-lg transition-all \".concat(currentStep === 0 ? 'bg-gray-800 text-gray-500 cursor-not-allowed' : 'bg-gray-800 hover:bg-gray-700 text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Previous\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, undefined),\n                            currentStep === steps.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: generateResume,\n                                disabled: isGenerating,\n                                className: \"flex items-center gap-2 px-8 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity disabled:opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isGenerating ? 'Generating...' : 'Generate Resume'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextStep,\n                                className: \"flex items-center gap-2 px-6 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity\",\n                                children: [\n                                    \"Next\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 458,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"2tVV+lPrzeyw08D2cIqSHZ2FEK8=\");\n_c = ResumeBuilder;\n// Personal Information Form Component\nconst PersonalInfoForm = (param)=>{\n    let { formData, updateFormData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-6 w-6 text-neural-purple\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 505,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"Personal Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 506,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 504,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"First Name *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 511,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.firstName,\n                                onChange: (e)=>updateFormData('personal', 'firstName', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"John\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 514,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 510,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Last Name *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 524,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.lastName,\n                                onChange: (e)=>updateFormData('personal', 'lastName', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"Doe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 527,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 523,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Email *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 537,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"email\",\n                                value: formData.personal.email,\n                                onChange: (e)=>updateFormData('personal', 'email', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 541,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 536,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Phone\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 551,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"tel\",\n                                value: formData.personal.phone,\n                                onChange: (e)=>updateFormData('personal', 'phone', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"+****************\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 555,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 550,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Location\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 565,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.location,\n                                onChange: (e)=>updateFormData('personal', 'location', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"New York, NY\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 569,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 564,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"LinkedIn\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 579,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.personal.linkedin,\n                                onChange: (e)=>updateFormData('personal', 'linkedin', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"https://linkedin.com/in/johndoe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 583,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 578,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Portfolio/Website\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 593,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.personal.portfolio,\n                                onChange: (e)=>updateFormData('personal', 'portfolio', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"https://johndoe.com\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 597,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 592,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Professional Summary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 607,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: formData.personal.summary,\n                                onChange: (e)=>updateFormData('personal', 'summary', e.target.value),\n                                rows: 4,\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                placeholder: \"Brief overview of your professional background and key achievements...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 610,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 606,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 509,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 498,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PersonalInfoForm;\n// Education Form Component\nconst EducationForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 632,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 633,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 631,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addArrayItem('education', {\n                                degree: \"\",\n                                institution: \"\",\n                                location: \"\",\n                                startDate: \"\",\n                                endDate: \"\",\n                                gpa: \"\",\n                                relevant: \"\"\n                            }),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 647,\n                                columnNumber: 9\n                            }, undefined),\n                            \"Add Education\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 635,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 630,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: formData.education.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 p-6 rounded-lg border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Education \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeArrayItem('education', edu.id),\n                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Degree *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.degree,\n                                                onChange: (e)=>updateFormData('education', 'degree', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"Bachelor of Science\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Institution *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.institution,\n                                                onChange: (e)=>updateFormData('education', 'institution', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"University of Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Location\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.location,\n                                                onChange: (e)=>updateFormData('education', 'location', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"New York, NY\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"GPA (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.gpa,\n                                                onChange: (e)=>updateFormData('education', 'gpa', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"3.8/4.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Start Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.startDate,\n                                                onChange: (e)=>updateFormData('education', 'startDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"End Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.endDate,\n                                                onChange: (e)=>updateFormData('education', 'endDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Relevant Coursework/Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: edu.relevant,\n                                                onChange: (e)=>updateFormData('education', 'relevant', e.target.value, index),\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"Relevant coursework, honors, achievements...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 667,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, edu.id, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 654,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 652,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 624,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EducationForm;\n// Resume Preview Component\nconst ResumePreview = (param)=>{\n    let { formData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white text-black p-6 rounded-lg text-xs max-h-96 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center border-b border-gray-300 pb-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: [\n                            formData.personal.firstName,\n                            \" \",\n                            formData.personal.lastName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 771,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-2 mt-2 text-gray-600\",\n                        children: [\n                            formData.personal.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 775,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 776,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.phone\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 777,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 778,\n                                columnNumber: 40\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.location\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 779,\n                                columnNumber: 40\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 774,\n                        columnNumber: 7\n                    }, undefined),\n                    formData.personal.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 text-blue-600\",\n                        children: formData.personal.linkedin\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 782,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 770,\n                columnNumber: 5\n            }, undefined),\n            formData.personal.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"PROFESSIONAL SUMMARY\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 789,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 leading-relaxed\",\n                        children: formData.personal.summary\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 792,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 788,\n                columnNumber: 7\n            }, undefined),\n            formData.experience.length > 0 && formData.experience[0].title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"EXPERIENCE\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 799,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.experience.map((exp)=>exp.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: exp.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: exp.company\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        exp.startDate,\n                                                        \" - \",\n                                                        exp.current ? 'Present' : exp.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                exp.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: exp.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 15\n                                }, undefined),\n                                exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-gray-700 whitespace-pre-line\",\n                                    children: exp.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 816,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, exp.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 804,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 798,\n                columnNumber: 7\n            }, undefined),\n            formData.education.length > 0 && formData.education[0].degree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"EDUCATION\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 829,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.education.map((edu)=>edu.degree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: edu.degree\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: edu.institution\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        edu.startDate,\n                                                        \" - \",\n                                                        edu.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 841,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                edu.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: edu.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 840,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 15\n                                }, undefined),\n                                edu.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"GPA: \",\n                                        edu.gpa\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 845,\n                                    columnNumber: 27\n                                }, undefined),\n                                edu.relevant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: edu.relevant\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 32\n                                }, undefined)\n                            ]\n                        }, edu.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 834,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 828,\n                columnNumber: 7\n            }, undefined),\n            (formData.skills.technical.length > 0 || formData.skills.languages.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"SKILLS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 856,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.skills.technical.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Technical: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 861,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.technical.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 862,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 860,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.skills.languages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Languages: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 867,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.languages.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 868,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 866,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.skills.certifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Certifications: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 873,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.certifications.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 874,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 872,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 855,\n                columnNumber: 7\n            }, undefined),\n            formData.projects.length > 0 && formData.projects[0].name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"PROJECTS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 883,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.projects.map((project)=>project.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: project.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        project.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: project.link,\n                                            className: \"text-blue-600 hover:underline\",\n                                            children: \"Link\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, undefined),\n                                project.technologies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 italic\",\n                                    children: project.technologies\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 17\n                                }, undefined),\n                                project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: project.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, project.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 888,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 882,\n                columnNumber: 7\n            }, undefined),\n            !formData.personal.firstName && !formData.personal.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 912,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Start filling out the form to see your resume preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 913,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 911,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 768,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = ResumePreview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ResumeBuilder\");\n$RefreshReg$(_c1, \"PersonalInfoForm\");\n$RefreshReg$(_c2, \"EducationForm\");\n$RefreshReg$(_c3, \"ResumePreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ })

});