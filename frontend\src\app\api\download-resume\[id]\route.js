import { NextResponse } from 'next/server';

// For now, let's create a simpler PDF generation without puppeteer
// We'll use a client-side approach instead

export async function GET(request, { params }) {
  try {
    const { id } = params;
    console.log('📄 PDF generation requested for ID:', id);

    // For now, return a simple response that triggers client-side PDF generation
    return NextResponse.json({
      message: 'PDF generation endpoint - use client-side generation',
      id: id
    });

  } catch (error) {
    console.error('PDF generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}

export async function POST(request, { params }) {
  try {
    const { id } = params;
    const { formData, resumeData } = await request.json();

    console.log('📄 PDF generation with data for ID:', id);

    // Generate HTML for the resume
    const resumeHTML = generateResumeHTML(formData, resumeData);

    // For now, return the HTML - we'll implement client-side PDF generation
    return NextResponse.json({
      success: true,
      html: resumeHTML,
      filename: `${formData.personal.firstName}_${formData.personal.lastName}_Resume.pdf`
    });

  } catch (error) {
    console.error('PDF generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}

function generateResumeHTML(formData, resumeData) {
  const enhanced = resumeData?.enhancedContent || {};
  const personal = formData?.personal || {};
  const experience = formData?.experience || [];
  const education = formData?.education || [];
  const skills = formData?.skills || { technical: [], languages: [], certifications: [] };
  const projects = formData?.projects || [];

  // Use enhanced content if available, otherwise use original form data
  const summary = enhanced.professionalSummary || personal.summary || '';
  const enhancedExperience = enhanced.experience || experience;
  const enhancedEducation = enhanced.education || education;
  const enhancedSkills = enhanced.skills || skills;
  const enhancedProjects = enhanced.projects || projects;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${personal.firstName} ${personal.lastName} - Resume</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Georgia', 'Times New Roman', serif;
          line-height: 1.6;
          color: #333;
          background: white;
          font-size: 12px;
        }

        .container {
          max-width: 8.5in;
          margin: 0 auto;
          padding: 0.75in;
        }

        .header {
          text-align: center;
          border-bottom: 3px solid #2c3e50;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }

        .name {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #2c3e50;
        }

        .contact {
          font-size: 14px;
          color: #555;
          margin-bottom: 10px;
        }

        .links {
          font-size: 13px;
          color: #3498db;
        }

        .section {
          margin-bottom: 25px;
        }

        .section-title {
          font-size: 16px;
          font-weight: bold;
          text-transform: uppercase;
          border-bottom: 2px solid #34495e;
          padding-bottom: 5px;
          margin-bottom: 15px;
          letter-spacing: 1px;
          color: #2c3e50;
        }

        .job, .education, .project {
          margin-bottom: 20px;
          page-break-inside: avoid;
        }

        .job-header, .edu-header, .project-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;
        }

        .job-title, .degree, .project-name {
          font-weight: bold;
          font-size: 15px;
          color: #2c3e50;
        }

        .company, .institution {
          font-style: italic;
          color: #555;
          font-size: 14px;
        }

        .location {
          color: #777;
          font-size: 12px;
        }

        .date {
          font-size: 13px;
          color: #666;
          font-weight: 600;
        }

        .description {
          margin-top: 8px;
          font-size: 13px;
          line-height: 1.5;
        }

        .description p {
          margin-bottom: 5px;
        }

        .skill-category {
          margin-bottom: 12px;
        }

        .skill-title {
          font-weight: bold;
          margin-bottom: 5px;
          color: #2c3e50;
        }

        .skill-list {
          color: #555;
        }

        .summary {
          text-align: justify;
          font-size: 13px;
          line-height: 1.6;
          color: #444;
        }

        @media print {
          body { font-size: 11px; }
          .container { padding: 0.5in; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="name">${personal.firstName || ''} ${personal.lastName || ''}</div>
          <div class="contact">
            ${[
              personal.email,
              personal.phone,
              personal.location
            ].filter(Boolean).join(' • ')}
          </div>
          ${personal.linkedin || personal.portfolio ? `
            <div class="links">
              ${[
                personal.linkedin ? `LinkedIn: ${personal.linkedin}` : '',
                personal.portfolio ? `Portfolio: ${personal.portfolio}` : ''
              ].filter(Boolean).join(' • ')}
            </div>
          ` : ''}
        </div>

        ${summary ? `
          <div class="section">
            <div class="section-title">Professional Summary</div>
            <div class="summary">${summary}</div>
          </div>
        ` : ''}

        ${enhancedExperience.length > 0 ? `
          <div class="section">
            <div class="section-title">Professional Experience</div>
            ${enhancedExperience.map(exp => `
              <div class="job">
                <div class="job-header">
                  <div>
                    <div class="job-title">${exp.title || ''}</div>
                    <div class="company">${exp.company || ''}</div>
                    ${exp.location ? `<div class="location">${exp.location}</div>` : ''}
                  </div>
                  <div class="date">${exp.startDate || ''} - ${exp.current ? 'Present' : (exp.endDate || '')}</div>
                </div>
                ${exp.achievements ? `
                  <div class="description">
                    ${exp.achievements.map(achievement => `<p>• ${achievement}</p>`).join('')}
                  </div>
                ` : exp.description ? `
                  <div class="description">
                    ${exp.description.split('\n').map(line => `<p>${line}</p>`).join('')}
                  </div>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        ${enhancedEducation.length > 0 ? `
          <div class="section">
            <div class="section-title">Education</div>
            ${enhancedEducation.map(edu => `
              <div class="education">
                <div class="edu-header">
                  <div>
                    <div class="degree">${edu.degree || ''}</div>
                    <div class="institution">${edu.institution || ''}</div>
                    ${edu.location ? `<div class="location">${edu.location}</div>` : ''}
                    ${edu.relevant ? `<div style="margin-top: 5px; font-size: 12px;"><strong>Relevant Coursework:</strong> ${edu.relevant}</div>` : ''}
                  </div>
                  <div>
                    <div class="date">${edu.startDate || ''} - ${edu.endDate || ''}</div>
                    ${edu.gpa ? `<div style="font-size: 12px; color: #666;">GPA: ${edu.gpa}</div>` : ''}
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        ` : ''}

        ${(enhancedSkills.technical?.length > 0 || enhancedSkills.languages?.length > 0 || enhancedSkills.certifications?.length > 0) ? `
          <div class="section">
            <div class="section-title">Skills & Certifications</div>
            ${enhancedSkills.technical?.length > 0 ? `
              <div class="skill-category">
                <div class="skill-title">Technical Skills:</div>
                <div class="skill-list">${Array.isArray(enhancedSkills.technical) ? enhancedSkills.technical.join(' • ') : enhancedSkills.technical}</div>
              </div>
            ` : ''}
            ${enhancedSkills.languages?.length > 0 ? `
              <div class="skill-category">
                <div class="skill-title">Languages:</div>
                <div class="skill-list">${Array.isArray(enhancedSkills.languages) ? enhancedSkills.languages.join(' • ') : enhancedSkills.languages}</div>
              </div>
            ` : ''}
            ${enhancedSkills.certifications?.length > 0 ? `
              <div class="skill-category">
                <div class="skill-title">Certifications:</div>
                <div class="skill-list">${Array.isArray(enhancedSkills.certifications) ? enhancedSkills.certifications.join(' • ') : enhancedSkills.certifications}</div>
              </div>
            ` : ''}
          </div>
        ` : ''}

        ${enhancedProjects.length > 0 ? `
          <div class="section">
            <div class="section-title">Projects</div>
            ${enhancedProjects.map(project => `
              <div class="project">
                <div class="project-header">
                  <div class="project-name">${project.name || ''}</div>
                  ${project.link ? `<div style="font-size: 12px; color: #3498db;">${project.link}</div>` : ''}
                </div>
                ${project.description ? `<div class="description"><p>${project.description}</p></div>` : ''}
                ${project.technologies ? `<div style="margin-top: 5px; font-size: 12px;"><strong>Technologies:</strong> ${project.technologies}</div>` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}
      </div>
    </body>
    </html>
  `;
}
