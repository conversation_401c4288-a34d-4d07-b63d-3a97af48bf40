import { NextResponse } from 'next/server';
import { generateResumeHTML } from '../../../components/ResumeTemplates.js';

// Enhanced PDF generation with professional templates

export async function GET(request, { params }) {
  try {
    const { id } = params;
    console.log('📄 PDF generation requested for ID:', id);

    // For now, return a simple response that triggers client-side PDF generation
    return NextResponse.json({
      message: 'PDF generation endpoint - use client-side generation',
      id: id
    });

  } catch (error) {
    console.error('PDF generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}

export async function POST(request, { params }) {
  try {
    const { id } = params;
    const { formData, resumeData, templateId = 'modern' } = await request.json();

    console.log('📄 PDF generation with data for ID:', id);
    console.log('🎨 Using template:', templateId);

    // Generate HTML for the resume using the selected template
    const resumeHTML = generateResumeHTML(formData, resumeData, templateId);

    // Return the HTML for client-side PDF generation
    return NextResponse.json({
      success: true,
      html: resumeHTML,
      templateId: templateId,
      filename: `${formData.personal.firstName}_${formData.personal.lastName}_Resume.pdf`
    });

  } catch (error) {
    console.error('PDF generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}

// Legacy function removed - now using template system from ResumeTemplates.js
