{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.1", "@tailwindcss/typography": "^0.5.16", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "countries-list": "^3.1.1", "docx-parser": "^0.2.1", "embla-carousel-react": "^8.5.1", "file-type": "^21.0.0", "firebase": "^11.0.2", "formik": "^2.4.6", "framer-motion": "^11.13.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.453.0", "mammoth": "^1.9.1", "multer": "^2.0.1", "next": "^15.3.3", "pdf-parse": "^1.1.1", "puppeteer": "^24.10.0", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.4.1", "react-phone-input-2": "^2.15.1", "react-slick": "^0.30.2", "react-to-print": "^3.1.0", "slick-carousel": "^1.8.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0"}, "devDependencies": {"@netlify/plugin-nextjs": "^5.8.1", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1"}}