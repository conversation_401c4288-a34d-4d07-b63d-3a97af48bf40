import fs, { Dirent } from 'fs';
export { chmodSync, mkdirSync, renameSync, rmdirSync, rmSync, statSync, lstatSync, unlinkSync, } from 'fs';
export declare const readdirSync: (path: fs.PathLike) => Dirent[];
export declare const promises: {
    chmod: (path: fs.PathLike, mode: fs.Mode) => Promise<void>;
    mkdir: (path: fs.PathLike, options?: fs.Mode | (fs.MakeDirectoryOptions & {
        recursive?: boolean | null;
    }) | undefined | null) => Promise<string | undefined>;
    readdir: (path: fs.PathLike) => Promise<Dirent[]>;
    rename: (oldPath: fs.PathLike, newPath: fs.PathLike) => Promise<void>;
    rm: (path: fs.PathLike, options: fs.RmOptions) => Promise<void>;
    rmdir: (path: fs.PathLike) => Promise<void>;
    stat: (path: fs.PathLike) => Promise<fs.Stats>;
    lstat: (path: fs.PathLike) => Promise<fs.Stats>;
    unlink: (path: fs.PathLike) => Promise<void>;
};
//# sourceMappingURL=fs.d.ts.map