"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Settings,
  HelpCircle
} from 'lucide-react';

const FormHeader = ({ 
  currentStep, 
  totalSteps, 
  steps = [],
  onBack,
  onPreview,
  onSave,
  showPreview = false,
  className = "" 
}) => {
  const currentStepData = steps[currentStep];

  return (
    <motion.div
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className={`sticky top-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-700 ${className}`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4">
          {/* Left Side - Back Button */}
          <div className="flex items-center space-x-4">
            <motion.button
              onClick={onBack}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-3 py-2 text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Back to Home</span>
            </motion.button>
          </div>

          {/* Center - Current Step Info */}
          <div className="flex-1 text-center px-4">
            <h1 className="text-xl font-bold text-white">
              Resume Builder
            </h1>
            <div className="text-sm text-gray-400 mt-1">
              {currentStepData?.title} • Step {currentStep + 1} of {totalSteps}
            </div>
          </div>

          {/* Right Side - Action Buttons */}
          <div className="flex items-center space-x-2">
            {/* Help Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-400 hover:text-neural-purple transition-colors"
              title="Help & Tips"
            >
              <HelpCircle className="h-5 w-5" />
            </motion.button>

            {/* Preview Button */}
            <motion.button
              onClick={onPreview}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`
                flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200
                ${showPreview 
                  ? 'bg-neural-purple text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
                }
              `}
            >
              <Eye className="h-4 w-4" />
              <span className="hidden sm:inline">Preview</span>
            </motion.button>

            {/* Save Draft Button */}
            <motion.button
              onClick={onSave}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-200"
            >
              <Save className="h-4 w-4" />
              <span className="hidden sm:inline">Save</span>
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default FormHeader;
