import { motion } from "framer-motion";
import {
  CheckCircle,
  Download,
  Eye,
  Share2,
  RefreshCw,
  FileText,
  Sparkles
} from "lucide-react";
import { useState, useRef } from "react";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

const SuccessScreen = ({ formData, resumeData, onStartOver, onEditResume }) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showFullPreview, setShowFullPreview] = useState(false);
  const resumeRef = useRef();

  const downloadPDF = async () => {
    try {
      setIsDownloading(true);

      // Create a temporary div with the resume content
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.width = '210mm'; // A4 width
      tempDiv.style.backgroundColor = 'white';
      tempDiv.style.color = 'black';
      tempDiv.style.padding = '20mm';
      tempDiv.style.fontFamily = 'Arial, sans-serif';
      tempDiv.style.fontSize = '12px';
      tempDiv.style.lineHeight = '1.5';

      // Format the resume content
      const resumeContent = formatResumeForPDF(formData, resumeData);
      tempDiv.innerHTML = resumeContent;

      document.body.appendChild(tempDiv);

      // Generate PDF using html2canvas and jsPDF
      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff'
      });

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');

      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Download the PDF
      const fileName = `${formData.personal.firstName}_${formData.personal.lastName}_Resume.pdf`;
      pdf.save(fileName);

      // Clean up
      document.body.removeChild(tempDiv);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  const formatResumeForPDF = (formData, resumeData) => {
    const { personal, education, experience, skills, projects } = formData;

    return `
      <div style="max-width: 100%; margin: 0 auto;">
        <!-- Header -->
        <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 15px; margin-bottom: 20px;">
          <h1 style="margin: 0; font-size: 24px; font-weight: bold; color: #333;">
            ${personal.firstName} ${personal.lastName}
          </h1>
          <div style="margin-top: 8px; color: #666; font-size: 11px;">
            ${personal.email}${personal.phone ? ` • ${personal.phone}` : ''}${personal.location ? ` • ${personal.location}` : ''}
          </div>
          ${personal.linkedin ? `<div style="margin-top: 4px; color: #0066cc; font-size: 11px;">${personal.linkedin}</div>` : ''}
          ${personal.portfolio ? `<div style="margin-top: 4px; color: #0066cc; font-size: 11px;">${personal.portfolio}</div>` : ''}
        </div>

        <!-- Professional Summary -->
        ${personal.summary ? `
          <div style="margin-bottom: 20px;">
            <h2 style="font-size: 14px; font-weight: bold; color: #333; border-bottom: 1px solid #ccc; padding-bottom: 4px; margin-bottom: 8px;">
              PROFESSIONAL SUMMARY
            </h2>
            <p style="margin: 0; color: #555; line-height: 1.4;">
              ${personal.summary}
            </p>
          </div>
        ` : ''}

        <!-- Experience -->
        ${experience && experience.length > 0 && experience[0].title ? `
          <div style="margin-bottom: 20px;">
            <h2 style="font-size: 14px; font-weight: bold; color: #333; border-bottom: 1px solid #ccc; padding-bottom: 4px; margin-bottom: 8px;">
              EXPERIENCE
            </h2>
            ${experience.map(exp => exp.title ? `
              <div style="margin-bottom: 12px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                  <div>
                    <h3 style="margin: 0; font-size: 12px; font-weight: bold; color: #333;">${exp.title}</h3>
                    <p style="margin: 2px 0; font-size: 11px; color: #666; font-weight: 600;">${exp.company}</p>
                  </div>
                  <div style="text-align: right; font-size: 10px; color: #666;">
                    <div>${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}</div>
                    ${exp.location ? `<div>${exp.location}</div>` : ''}
                  </div>
                </div>
                ${exp.description ? `
                  <div style="margin-top: 6px; font-size: 11px; color: #555; white-space: pre-line;">
                    ${exp.description}
                  </div>
                ` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        <!-- Education -->
        ${education && education.length > 0 && education[0].degree ? `
          <div style="margin-bottom: 20px;">
            <h2 style="font-size: 14px; font-weight: bold; color: #333; border-bottom: 1px solid #ccc; padding-bottom: 4px; margin-bottom: 8px;">
              EDUCATION
            </h2>
            ${education.map(edu => edu.degree ? `
              <div style="margin-bottom: 8px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                  <div>
                    <h3 style="margin: 0; font-size: 12px; font-weight: bold; color: #333;">${edu.degree}</h3>
                    <p style="margin: 2px 0; font-size: 11px; color: #666;">${edu.institution}</p>
                  </div>
                  <div style="text-align: right; font-size: 10px; color: #666;">
                    <div>${edu.startDate} - ${edu.endDate}</div>
                    ${edu.location ? `<div>${edu.location}</div>` : ''}
                  </div>
                </div>
                ${edu.gpa ? `<p style="margin: 2px 0; font-size: 10px; color: #666;">GPA: ${edu.gpa}</p>` : ''}
                ${edu.relevant ? `<p style="margin: 4px 0; font-size: 11px; color: #555;">${edu.relevant}</p>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        <!-- Skills -->
        ${(skills && (skills.technical?.length > 0 || skills.languages?.length > 0 || skills.certifications?.length > 0)) ? `
          <div style="margin-bottom: 20px;">
            <h2 style="font-size: 14px; font-weight: bold; color: #333; border-bottom: 1px solid #ccc; padding-bottom: 4px; margin-bottom: 8px;">
              SKILLS
            </h2>
            ${skills.technical?.length > 0 ? `
              <div style="margin-bottom: 6px;">
                <span style="font-weight: bold; font-size: 11px; color: #333;">Technical: </span>
                <span style="font-size: 11px; color: #555;">${skills.technical.join(', ')}</span>
              </div>
            ` : ''}
            ${skills.languages?.length > 0 ? `
              <div style="margin-bottom: 6px;">
                <span style="font-weight: bold; font-size: 11px; color: #333;">Languages: </span>
                <span style="font-size: 11px; color: #555;">${skills.languages.join(', ')}</span>
              </div>
            ` : ''}
            ${skills.certifications?.length > 0 ? `
              <div style="margin-bottom: 6px;">
                <span style="font-weight: bold; font-size: 11px; color: #333;">Certifications: </span>
                <span style="font-size: 11px; color: #555;">${skills.certifications.join(', ')}</span>
              </div>
            ` : ''}
          </div>
        ` : ''}

        <!-- Projects -->
        ${projects && projects.length > 0 && projects[0].name ? `
          <div style="margin-bottom: 20px;">
            <h2 style="font-size: 14px; font-weight: bold; color: #333; border-bottom: 1px solid #ccc; padding-bottom: 4px; margin-bottom: 8px;">
              PROJECTS
            </h2>
            ${projects.map(project => project.name ? `
              <div style="margin-bottom: 10px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                  <h3 style="margin: 0; font-size: 12px; font-weight: bold; color: #333;">${project.name}</h3>
                  ${project.link ? `<a href="${project.link}" style="font-size: 10px; color: #0066cc;">View Project</a>` : ''}
                </div>
                ${project.technologies ? `<p style="margin: 2px 0; font-size: 10px; color: #666; font-style: italic;">${project.technologies}</p>` : ''}
                ${project.description ? `<p style="margin: 4px 0; font-size: 11px; color: #555;">${project.description}</p>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}
      </div>
    `;
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white flex items-center justify-center p-4"
    >
      <div className="max-w-4xl w-full">
        {/* Success Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500 mr-4" />
            <Sparkles className="h-8 w-8 text-neural-pink" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink mb-4">
            Resume Generated Successfully!
          </h1>
          <p className="text-gray-300 text-lg">
            Your professional resume is ready for download
          </p>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="flex flex-wrap justify-center gap-4 mb-8"
        >
          <button
            onClick={downloadPDF}
            disabled={isDownloading}
            className="flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold rounded-lg hover:opacity-90 transition-opacity disabled:opacity-50"
          >
            <Download className="h-5 w-5" />
            {isDownloading ? 'Generating PDF...' : 'Download PDF'}
          </button>

          <button
            onClick={() => setShowFullPreview(!showFullPreview)}
            className="flex items-center gap-2 px-6 py-4 bg-gray-800 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors"
          >
            <Eye className="h-5 w-5" />
            {showFullPreview ? 'Hide Preview' : 'Preview Resume'}
          </button>

          <button
            onClick={onEditResume}
            className="flex items-center gap-2 px-6 py-4 bg-gray-800 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors"
          >
            <FileText className="h-5 w-5" />
            Edit Resume
          </button>

          <button
            onClick={onStartOver}
            className="flex items-center gap-2 px-6 py-4 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-lg transition-colors"
          >
            <RefreshCw className="h-5 w-5" />
            Start Over
          </button>
        </motion.div>

        {/* Resume Preview */}
        {showFullPreview && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white text-black rounded-lg p-8 max-h-96 overflow-y-auto mb-8"
          >
            <div ref={resumeRef} dangerouslySetInnerHTML={{
              __html: formatResumeForPDF(formData, resumeData)
            }} />
          </motion.div>
        )}

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8"
        >
          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10 text-center">
            <h3 className="text-2xl font-bold text-neural-purple mb-2">
              {formData.experience?.length || 0}
            </h3>
            <p className="text-gray-400">Work Experiences</p>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10 text-center">
            <h3 className="text-2xl font-bold text-neural-pink mb-2">
              {formData.education?.length || 0}
            </h3>
            <p className="text-gray-400">Education Entries</p>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10 text-center">
            <h3 className="text-2xl font-bold text-neural-blue mb-2">
              {(formData.skills?.technical?.length || 0) + (formData.skills?.languages?.length || 0)}
            </h3>
            <p className="text-gray-400">Skills Listed</p>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default SuccessScreen;