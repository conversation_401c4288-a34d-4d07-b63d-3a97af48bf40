import { motion } from "framer-motion";
import {
  CheckCircle,
  Download,
  Eye,
  Share2,
  RefreshCw,
  FileText,
  Sparkles,
  Target,
  TrendingUp,
  Award,
  BarChart3
} from "lucide-react";
import { useState, useRef } from "react";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

const SuccessScreen = ({ formData, resumeData, onStartOver, onEditResume }) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showFullPreview, setShowFullPreview] = useState(false);
  const resumeRef = useRef();

  const downloadPDF = async () => {
    try {
      setIsDownloading(true);

      // Create a temporary div with the resume content
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.width = '210mm'; // A4 width
      tempDiv.style.backgroundColor = 'white';
      tempDiv.style.color = 'black';
      tempDiv.style.padding = '20mm';
      tempDiv.style.fontFamily = 'Arial, sans-serif';
      tempDiv.style.fontSize = '12px';
      tempDiv.style.lineHeight = '1.5';

      // Format the resume content
      const resumeContent = formatResumeForPDF(formData, resumeData);
      tempDiv.innerHTML = resumeContent;

      document.body.appendChild(tempDiv);

      // Generate PDF using html2canvas and jsPDF
      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff'
      });

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');

      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Download the PDF
      const fileName = `${formData.personal.firstName}_${formData.personal.lastName}_Resume.pdf`;
      pdf.save(fileName);

      // Clean up
      document.body.removeChild(tempDiv);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  const formatResumeForPDF = (formData, resumeData) => {
    const { personal } = formData;
    const enhanced = resumeData?.enhancedContent || {};

    return `
      <div style="max-width: 100%; margin: 0 auto; font-family: 'Arial', 'Helvetica', sans-serif; line-height: 1.4; color: #333;">
        <!-- Header -->
        <div style="text-align: center; border-bottom: 3px solid #2563eb; padding-bottom: 20px; margin-bottom: 25px; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 20px; border-radius: 8px;">
          <h1 style="margin: 0; font-size: 28px; font-weight: bold; color: #1e293b; letter-spacing: 1px;">
            ${personal.firstName} ${personal.lastName}
          </h1>
          <div style="margin-top: 10px; color: #475569; font-size: 12px; font-weight: 500;">
            ${personal.email}${personal.phone ? ` • ${personal.phone}` : ''}${personal.location ? ` • ${personal.location}` : ''}
          </div>
          ${personal.linkedin ? `<div style="margin-top: 6px; color: #2563eb; font-size: 11px; font-weight: 500;">${personal.linkedin}</div>` : ''}
          ${personal.portfolio ? `<div style="margin-top: 4px; color: #2563eb; font-size: 11px; font-weight: 500;">${personal.portfolio}</div>` : ''}
        </div>

        <!-- Professional Summary -->
        ${enhanced.professionalSummary ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              PROFESSIONAL SUMMARY
            </h2>
            <p style="margin: 0; color: #374151; line-height: 1.6; font-size: 12px; text-align: justify;">
              ${enhanced.professionalSummary}
            </p>
          </div>
        ` : ''}

        <!-- Experience -->
        ${enhanced.experience && enhanced.experience.length > 0 ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              PROFESSIONAL EXPERIENCE
            </h2>
            ${enhanced.experience.map(exp => `
              <div style="margin-bottom: 18px; border-left: 3px solid #e2e8f0; padding-left: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 14px; font-weight: bold; color: #1e293b;">${exp.title}</h3>
                    <p style="margin: 2px 0; font-size: 12px; color: #2563eb; font-weight: 600;">${exp.company}</p>
                    ${exp.location ? `<p style="margin: 1px 0; font-size: 10px; color: #64748b;">${exp.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 10px; color: #64748b; font-weight: 500; background: #f1f5f9; padding: 4px 8px; border-radius: 4px;">
                    ${exp.startDate} - ${exp.endDate}
                  </div>
                </div>
                ${exp.achievements && exp.achievements.length > 0 ? `
                  <ul style="margin: 8px 0 0 0; padding-left: 0; list-style: none;">
                    ${exp.achievements.map(achievement => `
                      <li style="margin-bottom: 4px; font-size: 11px; color: #374151; line-height: 1.5; position: relative; padding-left: 12px;">
                        <span style="position: absolute; left: 0; top: 0; color: #2563eb; font-weight: bold;">•</span>
                        ${achievement}
                      </li>
                    `).join('')}
                  </ul>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        <!-- Education -->
        ${enhanced.education && enhanced.education.length > 0 ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              EDUCATION
            </h2>
            ${enhanced.education.map(edu => edu.degree ? `
              <div style="margin-bottom: 12px; border-left: 3px solid #e2e8f0; padding-left: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 13px; font-weight: bold; color: #1e293b;">${edu.degree}</h3>
                    <p style="margin: 2px 0; font-size: 12px; color: #2563eb; font-weight: 600;">${edu.institution}</p>
                    ${edu.location ? `<p style="margin: 1px 0; font-size: 10px; color: #64748b;">${edu.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 10px; color: #64748b; font-weight: 500; background: #f1f5f9; padding: 4px 8px; border-radius: 4px;">
                    ${edu.startDate} - ${edu.endDate}
                  </div>
                </div>
                ${edu.gpa ? `<p style="margin: 4px 0; font-size: 10px; color: #374151; font-weight: 500;">GPA: ${edu.gpa}</p>` : ''}
                ${edu.relevant ? `<p style="margin: 6px 0; font-size: 11px; color: #374151; line-height: 1.4;">${edu.relevant}</p>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        <!-- Skills -->
        ${enhanced.skills && (enhanced.skills.technical?.length > 0 || enhanced.skills.languages?.length > 0 || enhanced.skills.certifications?.length > 0) ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              CORE COMPETENCIES
            </h2>
            <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #2563eb;">
              ${enhanced.skills.technical?.length > 0 ? `
                <div style="margin-bottom: 10px;">
                  <span style="font-weight: bold; font-size: 12px; color: #1e293b; display: block; margin-bottom: 4px;">Technical Skills:</span>
                  <div style="display: flex; flex-wrap: wrap; gap: 6px;">
                    ${enhanced.skills.technical.map(skill => `
                      <span style="background: #2563eb; color: white; padding: 2px 8px; border-radius: 12px; font-size: 10px; font-weight: 500;">${skill}</span>
                    `).join('')}
                  </div>
                </div>
              ` : ''}
              ${enhanced.skills.languages?.length > 0 ? `
                <div style="margin-bottom: 10px;">
                  <span style="font-weight: bold; font-size: 12px; color: #1e293b; display: block; margin-bottom: 4px;">Languages:</span>
                  <span style="font-size: 11px; color: #374151;">${enhanced.skills.languages.join(' • ')}</span>
                </div>
              ` : ''}
              ${enhanced.skills.certifications?.length > 0 ? `
                <div style="margin-bottom: 0;">
                  <span style="font-weight: bold; font-size: 12px; color: #1e293b; display: block; margin-bottom: 4px;">Certifications:</span>
                  <span style="font-size: 11px; color: #374151;">${enhanced.skills.certifications.join(' • ')}</span>
                </div>
              ` : ''}
            </div>
          </div>
        ` : ''}

        <!-- Projects -->
        ${enhanced.projects && enhanced.projects.length > 0 ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              KEY PROJECTS
            </h2>
            ${enhanced.projects.map(project => project.name ? `
              <div style="margin-bottom: 15px; border-left: 3px solid #e2e8f0; padding-left: 15px; background: #f8fafc; padding: 12px; border-radius: 6px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 6px;">
                  <h3 style="margin: 0; font-size: 13px; font-weight: bold; color: #1e293b;">${project.name}</h3>
                  ${project.link ? `<a href="${project.link}" style="font-size: 10px; color: #2563eb; font-weight: 500; text-decoration: none;">View Project →</a>` : ''}
                </div>
                ${project.technologies ? `
                  <div style="margin-bottom: 6px;">
                    <span style="font-size: 10px; color: #64748b; font-weight: 500;">Technologies: </span>
                    <span style="font-size: 10px; color: #374151; font-style: italic;">${project.technologies}</span>
                  </div>
                ` : ''}
                ${project.description ? `<p style="margin: 6px 0 0 0; font-size: 11px; color: #374151; line-height: 1.4;">${project.description}</p>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        <!-- Footer -->
        <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e2e8f0; text-align: center;">
          <p style="margin: 0; font-size: 9px; color: #94a3b8; font-style: italic;">
            Generated with AI-Enhanced Resume Builder • ATS-Optimized Format
          </p>
        </div>
      </div>
    `;
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white flex items-center justify-center p-4"
    >
      <div className="max-w-4xl w-full">
        {/* Success Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500 mr-4" />
            <Sparkles className="h-8 w-8 text-neural-pink" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink mb-4">
            Resume Generated Successfully!
          </h1>
          <p className="text-gray-300 text-lg">
            Your professional, ATS-optimized resume is ready for download
          </p>
        </motion.div>

        {/* ATS Score Display */}
        {resumeData?.atsScore && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="max-w-4xl mx-auto mb-8"
          >
            <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <Target className="h-6 w-6 text-neural-blue" />
                  <h2 className="text-2xl font-bold">ATS Compatibility Score</h2>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-500" />
                  <span className="text-sm text-gray-400">AI-Enhanced</span>
                </div>
              </div>

              {/* Overall Score */}
              <div className="text-center mb-8">
                <div className="relative inline-flex items-center justify-center w-32 h-32 mb-4">
                  <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="currentColor"
                      strokeWidth="8"
                      fill="none"
                      className="text-gray-700"
                    />
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="currentColor"
                      strokeWidth="8"
                      fill="none"
                      strokeLinecap="round"
                      className={`${
                        resumeData.atsScore.overall >= 80 ? 'text-green-500' :
                        resumeData.atsScore.overall >= 60 ? 'text-yellow-500' : 'text-red-500'
                      }`}
                      style={{
                        strokeDasharray: `${2 * Math.PI * 50}`,
                        strokeDashoffset: `${2 * Math.PI * 50 * (1 - resumeData.atsScore.overall / 100)}`
                      }}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-white">{resumeData.atsScore.overall}</div>
                      <div className="text-sm text-gray-400">/ 100</div>
                    </div>
                  </div>
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  {resumeData.atsScore.overall >= 80 ? 'Excellent' :
                   resumeData.atsScore.overall >= 60 ? 'Good' : 'Needs Improvement'}
                </h3>
                <p className="text-gray-400">
                  Your resume is {resumeData.atsScore.overall >= 80 ? 'highly' : resumeData.atsScore.overall >= 60 ? 'moderately' : 'minimally'} optimized for ATS systems
                </p>
              </div>

              {/* Score Breakdown */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                {Object.entries(resumeData.atsScore.breakdown).map(([category, score]) => (
                  <div key={category} className="bg-gray-800/50 rounded-lg p-4 text-center">
                    <div className="flex items-center justify-center mb-2">
                      {category === 'keywords' && <BarChart3 className="h-5 w-5 text-neural-blue" />}
                      {category === 'formatting' && <FileText className="h-5 w-5 text-neural-purple" />}
                      {category === 'achievements' && <TrendingUp className="h-5 w-5 text-neural-pink" />}
                      {category === 'skills' && <Target className="h-5 w-5 text-green-500" />}
                    </div>
                    <div className="text-lg font-bold text-white mb-1">{score}%</div>
                    <div className="text-xs text-gray-400 capitalize">{category}</div>
                    <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
                      <div
                        className={`h-2 rounded-full ${
                          score >= 80 ? 'bg-green-500' :
                          score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${score}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>

              {/* Improvements */}
              {resumeData.atsScore.improvements && resumeData.atsScore.improvements.length > 0 && (
                <div className="bg-gray-800/30 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-gray-300 mb-3 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Suggestions for Improvement
                  </h4>
                  <ul className="space-y-2">
                    {resumeData.atsScore.improvements.map((improvement, index) => (
                      <li key={index} className="text-sm text-gray-400 flex items-start gap-2">
                        <span className="text-neural-blue mt-1">•</span>
                        {improvement}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="flex flex-wrap justify-center gap-4 mb-8"
        >
          <button
            onClick={downloadPDF}
            disabled={isDownloading}
            className="flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold rounded-lg hover:opacity-90 transition-opacity disabled:opacity-50"
          >
            <Download className="h-5 w-5" />
            {isDownloading ? 'Generating PDF...' : 'Download PDF'}
          </button>

          <button
            onClick={() => setShowFullPreview(!showFullPreview)}
            className="flex items-center gap-2 px-6 py-4 bg-gray-800 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors"
          >
            <Eye className="h-5 w-5" />
            {showFullPreview ? 'Hide Preview' : 'Preview Resume'}
          </button>

          <button
            onClick={onEditResume}
            className="flex items-center gap-2 px-6 py-4 bg-gray-800 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors"
          >
            <FileText className="h-5 w-5" />
            Edit Resume
          </button>

          <button
            onClick={onStartOver}
            className="flex items-center gap-2 px-6 py-4 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-lg transition-colors"
          >
            <RefreshCw className="h-5 w-5" />
            Start Over
          </button>
        </motion.div>

        {/* Resume Preview */}
        {showFullPreview && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white text-black rounded-lg p-8 max-h-96 overflow-y-auto mb-8"
          >
            <div ref={resumeRef} dangerouslySetInnerHTML={{
              __html: formatResumeForPDF(formData, resumeData)
            }} />
          </motion.div>
        )}

        {/* Enhanced Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8"
        >
          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 border border-white/10 text-center">
            <h3 className="text-xl font-bold text-neural-purple mb-1">
              {resumeData?.atsScore?.overall || 0}%
            </h3>
            <p className="text-gray-400 text-sm">ATS Score</p>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 border border-white/10 text-center">
            <h3 className="text-xl font-bold text-neural-pink mb-1">
              {resumeData?.enhancedContent?.experience?.length || formData.experience?.length || 0}
            </h3>
            <p className="text-gray-400 text-sm">Experiences</p>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 border border-white/10 text-center">
            <h3 className="text-xl font-bold text-neural-blue mb-1">
              {resumeData?.keywords?.length || 0}
            </h3>
            <p className="text-gray-400 text-sm">Keywords</p>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 border border-white/10 text-center">
            <h3 className="text-xl font-bold text-green-500 mb-1">
              {resumeData?.type === 'ai-enhanced' ? 'AI' : 'Template'}
            </h3>
            <p className="text-gray-400 text-sm">Enhanced</p>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default SuccessScreen;