"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Linkedin,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* harmony import */ var _components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ResumeFormComponents */ \"(app-pages-browser)/./src/components/ResumeFormComponents.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ResumeBuilder = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    // Main state management\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ]\n    });\n    // Steps configuration\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"Tell us about yourself\"\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Your academic background\"\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Your work experience\"\n        },\n        {\n            id: 3,\n            title: \"Skills & Projects\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Showcase your abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Finalize your resume\"\n        }\n    ];\n    // Update form data\n    const updateFormData = function(section, field, value) {\n        let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        setFormData((prev)=>{\n            if (index !== null && Array.isArray(prev[section])) {\n                const newArray = [\n                    ...prev[section]\n                ];\n                newArray[index] = {\n                    ...newArray[index],\n                    [field]: value\n                };\n                return {\n                    ...prev,\n                    [section]: newArray\n                };\n            } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                return {\n                    ...prev,\n                    [section]: {\n                        ...prev[section],\n                        [field]: value\n                    }\n                };\n            }\n            return prev;\n        });\n    };\n    // Add new item to array sections\n    const addArrayItem = (section, template)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: [\n                    ...prev[section],\n                    {\n                        ...template,\n                        id: Date.now()\n                    }\n                ]\n            }));\n    };\n    // Remove item from array sections\n    const removeArrayItem = (section, id)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: prev[section].filter((item)=>item.id !== id)\n            }));\n    };\n    // Navigation functions\n    const nextStep = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    // Generate resume with AI\n    const generateResume = async ()=>{\n        try {\n            setIsGenerating(true);\n            setShowProgressBar(true);\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            setResumeUrl(data.downloadUrl);\n        } catch (error) {\n            console.error(\"Error generating resume:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || \"Failed to generate resume\");\n            setIsGenerating(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsGenerating(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Your resume has been generated successfully!\");\n    };\n    // Floating background elements (matching hero section)\n    const FloatingElements = ()=>{\n        _s1();\n        const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [elements, setElements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ResumeBuilder.FloatingElements.useEffect\": ()=>{\n                setMounted(true);\n                // Generate consistent random values only on client side\n                const newElements = [\n                    ...Array(6)\n                ].map({\n                    \"ResumeBuilder.FloatingElements.useEffect.newElements\": (_, i)=>({\n                            id: i,\n                            initialX: Math.random() * 1200,\n                            initialY: Math.random() * 800,\n                            targetX: Math.random() * 1200,\n                            targetY: Math.random() * 800,\n                            width: Math.random() * 200 + 100,\n                            height: Math.random() * 200 + 100,\n                            duration: Math.random() * 20 + 20\n                        })\n                }[\"ResumeBuilder.FloatingElements.useEffect.newElements\"]);\n                setElements(newElements);\n            }\n        }[\"ResumeBuilder.FloatingElements.useEffect\"], []);\n        if (!mounted || elements.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: elements.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"absolute rounded-full bg-neural-purple opacity-10 blur-xl\",\n                    initial: {\n                        x: element.initialX,\n                        y: element.initialY,\n                        width: element.width,\n                        height: element.height\n                    },\n                    animate: {\n                        x: element.targetX,\n                        y: element.targetY,\n                        transition: {\n                            duration: element.duration,\n                            repeat: Infinity,\n                            repeatType: 'reverse'\n                        }\n                    }\n                }, element.id, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(FloatingElements, \"OR3uaTnixrLRKhvlgMvLSlkofQU=\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"text-center mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-neural-pink\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                children: \"Create a professional, ATS-friendly resume in minutes with our AI-powered builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center space-x-4 overflow-x-auto pb-4\",\n                            children: steps.map((step, index)=>{\n                                const Icon = step.icon;\n                                const isActive = index === currentStep;\n                                const isCompleted = index < currentStep;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    className: \"flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-300 \".concat(isActive ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25' : isCompleted ? 'bg-neural-purple/20 border-neural-purple/50' : 'bg-gray-800/50 border-gray-700'),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5 \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium hidden md:block \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400'),\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, step.id, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    className: \"bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: [\n                                            currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonalInfoForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EducationForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_4__.ExperienceForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_4__.SkillsProjectsForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_4__.ReviewForm, {\n                                                formData: formData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 39\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    className: \"bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10 sticky top-8\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 text-neural-blue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Live Preview\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowPreview(!showPreview),\n                                                    className: \"text-neural-blue hover:text-neural-pink transition-colors\",\n                                                    children: showPreview ? 'Hide' : 'Show'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResumePreview, {\n                                            formData: formData\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: 'Click \"Show\" to preview your resume'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevStep,\n                                disabled: currentStep === 0,\n                                className: \"flex items-center gap-2 px-6 py-3 rounded-lg transition-all \".concat(currentStep === 0 ? 'bg-gray-800 text-gray-500 cursor-not-allowed' : 'bg-gray-800 hover:bg-gray-700 text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Previous\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, undefined),\n                            currentStep === steps.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: generateResume,\n                                disabled: isGenerating,\n                                className: \"flex items-center gap-2 px-8 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity disabled:opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isGenerating ? 'Generating...' : 'Generate Resume'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextStep,\n                                className: \"flex items-center gap-2 px-6 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity\",\n                                children: [\n                                    \"Next\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"Z7z82en7Y9n3Ocn/UsN3+8Tv4v0=\");\n_c = ResumeBuilder;\n// Personal Information Form Component\nconst PersonalInfoForm = (param)=>{\n    let { formData, updateFormData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-6 w-6 text-neural-purple\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 423,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"Personal Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 424,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 422,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"First Name *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 429,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.firstName,\n                                onChange: (e)=>updateFormData('personal', 'firstName', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"John\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 432,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 428,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Last Name *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 442,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.lastName,\n                                onChange: (e)=>updateFormData('personal', 'lastName', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"Doe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 445,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 441,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Email *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 455,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"email\",\n                                value: formData.personal.email,\n                                onChange: (e)=>updateFormData('personal', 'email', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 459,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 454,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Phone\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 469,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"tel\",\n                                value: formData.personal.phone,\n                                onChange: (e)=>updateFormData('personal', 'phone', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"+****************\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 473,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 468,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Location\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 483,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.location,\n                                onChange: (e)=>updateFormData('personal', 'location', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"New York, NY\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 487,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 482,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"LinkedIn\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 497,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.personal.linkedin,\n                                onChange: (e)=>updateFormData('personal', 'linkedin', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"https://linkedin.com/in/johndoe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 501,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 496,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Portfolio/Website\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 511,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.personal.portfolio,\n                                onChange: (e)=>updateFormData('personal', 'portfolio', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"https://johndoe.com\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 515,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 510,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Professional Summary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 525,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: formData.personal.summary,\n                                onChange: (e)=>updateFormData('personal', 'summary', e.target.value),\n                                rows: 4,\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                placeholder: \"Brief overview of your professional background and key achievements...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 528,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 524,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 427,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 416,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PersonalInfoForm;\n// Education Form Component\nconst EducationForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 550,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 551,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 549,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addArrayItem('education', {\n                                degree: \"\",\n                                institution: \"\",\n                                location: \"\",\n                                startDate: \"\",\n                                endDate: \"\",\n                                gpa: \"\",\n                                relevant: \"\"\n                            }),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 565,\n                                columnNumber: 9\n                            }, undefined),\n                            \"Add Education\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 553,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 548,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: formData.education.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 p-6 rounded-lg border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Education \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeArrayItem('education', edu.id),\n                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Degree *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.degree,\n                                                onChange: (e)=>updateFormData('education', 'degree', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"Bachelor of Science\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Institution *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.institution,\n                                                onChange: (e)=>updateFormData('education', 'institution', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"University of Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Location\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.location,\n                                                onChange: (e)=>updateFormData('education', 'location', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"New York, NY\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"GPA (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.gpa,\n                                                onChange: (e)=>updateFormData('education', 'gpa', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"3.8/4.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Start Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.startDate,\n                                                onChange: (e)=>updateFormData('education', 'startDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"End Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.endDate,\n                                                onChange: (e)=>updateFormData('education', 'endDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Relevant Coursework/Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: edu.relevant,\n                                                onChange: (e)=>updateFormData('education', 'relevant', e.target.value, index),\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"Relevant coursework, honors, achievements...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, edu.id, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 572,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 570,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 542,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EducationForm;\n// Resume Preview Component\nconst ResumePreview = (param)=>{\n    let { formData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white text-black p-6 rounded-lg text-xs max-h-96 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center border-b border-gray-300 pb-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: [\n                            formData.personal.firstName,\n                            \" \",\n                            formData.personal.lastName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 689,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-2 mt-2 text-gray-600\",\n                        children: [\n                            formData.personal.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 693,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 694,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.phone\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 695,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 696,\n                                columnNumber: 40\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.location\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 697,\n                                columnNumber: 40\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 692,\n                        columnNumber: 7\n                    }, undefined),\n                    formData.personal.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 text-blue-600\",\n                        children: formData.personal.linkedin\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 700,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 688,\n                columnNumber: 5\n            }, undefined),\n            formData.personal.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"PROFESSIONAL SUMMARY\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 707,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 leading-relaxed\",\n                        children: formData.personal.summary\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 710,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 706,\n                columnNumber: 7\n            }, undefined),\n            formData.experience.length > 0 && formData.experience[0].title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"EXPERIENCE\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 717,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.experience.map((exp, index)=>exp.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: exp.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: exp.company\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        exp.startDate,\n                                                        \" - \",\n                                                        exp.current ? 'Present' : exp.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                exp.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: exp.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, undefined),\n                                exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-gray-700 whitespace-pre-line\",\n                                    children: exp.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, exp.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 722,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 716,\n                columnNumber: 7\n            }, undefined),\n            formData.education.length > 0 && formData.education[0].degree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"EDUCATION\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 747,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.education.map((edu, index)=>edu.degree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: edu.degree\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: edu.institution\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        edu.startDate,\n                                                        \" - \",\n                                                        edu.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                edu.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: edu.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, undefined),\n                                edu.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"GPA: \",\n                                        edu.gpa\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 763,\n                                    columnNumber: 27\n                                }, undefined),\n                                edu.relevant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: edu.relevant\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 32\n                                }, undefined)\n                            ]\n                        }, edu.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 752,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 746,\n                columnNumber: 7\n            }, undefined),\n            (formData.skills.technical.length > 0 || formData.skills.languages.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"SKILLS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 774,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.skills.technical.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Technical: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 779,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.technical.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 780,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 778,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.skills.languages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Languages: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 785,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.languages.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 786,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 784,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.skills.certifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Certifications: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 791,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.certifications.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 792,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 790,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 773,\n                columnNumber: 7\n            }, undefined),\n            formData.projects.length > 0 && formData.projects[0].name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"PROJECTS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 801,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.projects.map((project, index)=>project.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: project.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        project.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: project.link,\n                                            className: \"text-blue-600 hover:underline\",\n                                            children: \"Link\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 15\n                                }, undefined),\n                                project.technologies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 italic\",\n                                    children: project.technologies\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 816,\n                                    columnNumber: 17\n                                }, undefined),\n                                project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: project.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, project.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 800,\n                columnNumber: 7\n            }, undefined),\n            !formData.personal.firstName && !formData.personal.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Linkedin_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-12 w-12 mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 830,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Start filling out the form to see your resume preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 831,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 829,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 686,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = ResumePreview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ResumeBuilder\");\n$RefreshReg$(_c1, \"PersonalInfoForm\");\n$RefreshReg$(_c2, \"EducationForm\");\n$RefreshReg$(_c3, \"ResumePreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ })

});