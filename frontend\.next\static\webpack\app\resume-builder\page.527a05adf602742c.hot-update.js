"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@swc/helpers/esm/_tagged_template_literal.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: () => (/* binding */ _tagged_template_literal)\n/* harmony export */ });\nfunction _tagged_template_literal(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    return Object.freeze(Object.defineProperties(strings, { raw: { value: Object.freeze(raw) } }));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvZXNtL190YWdnZWRfdGVtcGxhdGVfbGl0ZXJhbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTs7QUFFQSw0REFBNEQsT0FBTyw2QkFBNkI7QUFDaEc7QUFDeUMiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcQmxpbmtGaW5kXFxCbGlua0ZpbmQtV2ViXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAc3djXFxoZWxwZXJzXFxlc21cXF90YWdnZWRfdGVtcGxhdGVfbGl0ZXJhbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBfdGFnZ2VkX3RlbXBsYXRlX2xpdGVyYWwoc3RyaW5ncywgcmF3KSB7XG4gICAgaWYgKCFyYXcpIHJhdyA9IHN0cmluZ3Muc2xpY2UoMCk7XG5cbiAgICByZXR1cm4gT2JqZWN0LmZyZWV6ZShPYmplY3QuZGVmaW5lUHJvcGVydGllcyhzdHJpbmdzLCB7IHJhdzogeyB2YWx1ZTogT2JqZWN0LmZyZWV6ZShyYXcpIH0gfSkpO1xufVxuZXhwb3J0IHsgX3RhZ2dlZF90ZW1wbGF0ZV9saXRlcmFsIGFzIF8gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/goober/dist/goober.modern.js":
/*!***************************************************!*\
  !*** ./node_modules/goober/dist/goober.modern.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ u),\n/* harmony export */   extractCss: () => (/* binding */ r),\n/* harmony export */   glob: () => (/* binding */ b),\n/* harmony export */   keyframes: () => (/* binding */ h),\n/* harmony export */   setup: () => (/* binding */ m),\n/* harmony export */   styled: () => (/* binding */ j)\n/* harmony export */ });\nlet e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9nb29iZXIvZGlzdC9nb29iZXIubW9kZXJuLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLE9BQU8sUUFBUSw4SkFBOEosMkJBQTJCLHlCQUF5QixvQkFBb0IsbUJBQW1CLHlDQUF5QyxLQUFLLE9BQU8sT0FBTyxJQUFJLGlEQUFpRCxtQkFBbUIsZ0JBQWdCLFdBQVcsZ0NBQWdDLDBCQUEwQix3QkFBd0IsZ1BBQWdQLEdBQUcsbUJBQW1CLE1BQU0sT0FBTyxLQUFLLE9BQU8sdUJBQXVCLFNBQVMsNEJBQTRCLFNBQVMsU0FBUyxpQkFBaUIsOEJBQThCLGFBQWEsS0FBSyxXQUFXLCtCQUErQixhQUFhLE1BQU0sVUFBVSxtQkFBbUIsYUFBYSxFQUFFLEtBQUssMEJBQTBCLGdGQUFnRix5Q0FBeUMsWUFBWSxLQUFLLFVBQVUsb0JBQW9CLGVBQWUsc0JBQXNCLGtDQUFrQyxrRkFBa0YsZ0JBQWdCLCtCQUErQixXQUFXLGNBQWMsNkRBQTZELCtEQUErRCwwQkFBMEIsS0FBSyxjQUFjLGNBQWMsbUJBQW1CLG1IQUFtSCw2QkFBNkIsb0JBQW9CLElBQUksWUFBWSxJQUFJLEVBQUUsb0JBQW9CLGtCQUFrQixnQkFBZ0IsZUFBZSxrQkFBa0IsZ0JBQWdCLGdCQUFnQixzQkFBc0IsK0JBQStCLG1CQUFtQixhQUFhLDZFQUE2RSxRQUFRLDBEQUEwRCxpQkFBa0ciLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcQmxpbmtGaW5kXFxCbGlua0ZpbmQtV2ViXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxnb29iZXJcXGRpc3RcXGdvb2Jlci5tb2Rlcm4uanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGU9e2RhdGE6XCJcIn0sdD10PT5cIm9iamVjdFwiPT10eXBlb2Ygd2luZG93PygodD90LnF1ZXJ5U2VsZWN0b3IoXCIjX2dvb2JlclwiKTp3aW5kb3cuX2dvb2Jlcil8fE9iamVjdC5hc3NpZ24oKHR8fGRvY3VtZW50LmhlYWQpLmFwcGVuZENoaWxkKGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKSkse2lubmVySFRNTDpcIiBcIixpZDpcIl9nb29iZXJcIn0pKS5maXJzdENoaWxkOnR8fGUscj1lPT57bGV0IHI9dChlKSxsPXIuZGF0YTtyZXR1cm4gci5kYXRhPVwiXCIsbH0sbD0vKD86KFtcXHUwMDgwLVxcdUZGRkZcXHctJUBdKykgKjo/ICooW157O10rPyk7fChbXjt9e10qPykgKnspfCh9XFxzKikvZyxhPS9cXC9cXCpbXl0qP1xcKlxcL3wgICsvZyxuPS9cXG4rL2csbz0oZSx0KT0+e2xldCByPVwiXCIsbD1cIlwiLGE9XCJcIjtmb3IobGV0IG4gaW4gZSl7bGV0IGM9ZVtuXTtcIkBcIj09blswXT9cImlcIj09blsxXT9yPW4rXCIgXCIrYytcIjtcIjpsKz1cImZcIj09blsxXT9vKGMsbik6bitcIntcIitvKGMsXCJrXCI9PW5bMV0/XCJcIjp0KStcIn1cIjpcIm9iamVjdFwiPT10eXBlb2YgYz9sKz1vKGMsdD90LnJlcGxhY2UoLyhbXixdKSsvZyxlPT5uLnJlcGxhY2UoLyhbXixdKjpcXFMrXFwoW14pXSpcXCkpfChbXixdKSsvZyx0PT4vJi8udGVzdCh0KT90LnJlcGxhY2UoLyYvZyxlKTplP2UrXCIgXCIrdDp0KSk6bik6bnVsbCE9YyYmKG49L14tLS8udGVzdChuKT9uOm4ucmVwbGFjZSgvW0EtWl0vZyxcIi0kJlwiKS50b0xvd2VyQ2FzZSgpLGErPW8ucD9vLnAobixjKTpuK1wiOlwiK2MrXCI7XCIpfXJldHVybiByKyh0JiZhP3QrXCJ7XCIrYStcIn1cIjphKStsfSxjPXt9LHM9ZT0+e2lmKFwib2JqZWN0XCI9PXR5cGVvZiBlKXtsZXQgdD1cIlwiO2ZvcihsZXQgciBpbiBlKXQrPXIrcyhlW3JdKTtyZXR1cm4gdH1yZXR1cm4gZX0saT0oZSx0LHIsaSxwKT0+e2xldCB1PXMoZSksZD1jW3VdfHwoY1t1XT0oZT0+e2xldCB0PTAscj0xMTtmb3IoO3Q8ZS5sZW5ndGg7KXI9MTAxKnIrZS5jaGFyQ29kZUF0KHQrKyk+Pj4wO3JldHVyblwiZ29cIityfSkodSkpO2lmKCFjW2RdKXtsZXQgdD11IT09ZT9lOihlPT57bGV0IHQscixvPVt7fV07Zm9yKDt0PWwuZXhlYyhlLnJlcGxhY2UoYSxcIlwiKSk7KXRbNF0/by5zaGlmdCgpOnRbM10/KHI9dFszXS5yZXBsYWNlKG4sXCIgXCIpLnRyaW0oKSxvLnVuc2hpZnQob1swXVtyXT1vWzBdW3JdfHx7fSkpOm9bMF1bdFsxXV09dFsyXS5yZXBsYWNlKG4sXCIgXCIpLnRyaW0oKTtyZXR1cm4gb1swXX0pKGUpO2NbZF09byhwP3tbXCJAa2V5ZnJhbWVzIFwiK2RdOnR9OnQscj9cIlwiOlwiLlwiK2QpfWxldCBmPXImJmMuZz9jLmc6bnVsbDtyZXR1cm4gciYmKGMuZz1jW2RdKSwoKGUsdCxyLGwpPT57bD90LmRhdGE9dC5kYXRhLnJlcGxhY2UobCxlKTotMT09PXQuZGF0YS5pbmRleE9mKGUpJiYodC5kYXRhPXI/ZSt0LmRhdGE6dC5kYXRhK2UpfSkoY1tkXSx0LGksZiksZH0scD0oZSx0LHIpPT5lLnJlZHVjZSgoZSxsLGEpPT57bGV0IG49dFthXTtpZihuJiZuLmNhbGwpe2xldCBlPW4ociksdD1lJiZlLnByb3BzJiZlLnByb3BzLmNsYXNzTmFtZXx8L15nby8udGVzdChlKSYmZTtuPXQ/XCIuXCIrdDplJiZcIm9iamVjdFwiPT10eXBlb2YgZT9lLnByb3BzP1wiXCI6byhlLFwiXCIpOiExPT09ZT9cIlwiOmV9cmV0dXJuIGUrbCsobnVsbD09bj9cIlwiOm4pfSxcIlwiKTtmdW5jdGlvbiB1KGUpe2xldCByPXRoaXN8fHt9LGw9ZS5jYWxsP2Uoci5wKTplO3JldHVybiBpKGwudW5zaGlmdD9sLnJhdz9wKGwsW10uc2xpY2UuY2FsbChhcmd1bWVudHMsMSksci5wKTpsLnJlZHVjZSgoZSx0KT0+T2JqZWN0LmFzc2lnbihlLHQmJnQuY2FsbD90KHIucCk6dCkse30pOmwsdChyLnRhcmdldCksci5nLHIubyxyLmspfWxldCBkLGYsZyxiPXUuYmluZCh7ZzoxfSksaD11LmJpbmQoe2s6MX0pO2Z1bmN0aW9uIG0oZSx0LHIsbCl7by5wPXQsZD1lLGY9cixnPWx9ZnVuY3Rpb24gaihlLHQpe2xldCByPXRoaXN8fHt9O3JldHVybiBmdW5jdGlvbigpe2xldCBsPWFyZ3VtZW50cztmdW5jdGlvbiBhKG4sbyl7bGV0IGM9T2JqZWN0LmFzc2lnbih7fSxuKSxzPWMuY2xhc3NOYW1lfHxhLmNsYXNzTmFtZTtyLnA9T2JqZWN0LmFzc2lnbih7dGhlbWU6ZiYmZigpfSxjKSxyLm89LyAqZ29cXGQrLy50ZXN0KHMpLGMuY2xhc3NOYW1lPXUuYXBwbHkocixsKSsocz9cIiBcIitzOlwiXCIpLHQmJihjLnJlZj1vKTtsZXQgaT1lO3JldHVybiBlWzBdJiYoaT1jLmFzfHxlLGRlbGV0ZSBjLmFzKSxnJiZpWzBdJiZnKGMpLGQoaSxjKX1yZXR1cm4gdD90KGEpOmF9fWV4cG9ydHt1IGFzIGNzcyxyIGFzIGV4dHJhY3RDc3MsYiBhcyBnbG9iLGggYXMga2V5ZnJhbWVzLG0gYXMgc2V0dXAsaiBhcyBzdHlsZWR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/goober/dist/goober.modern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/react-hot-toast/dist/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckmarkIcon: () => (/* binding */ w),\n/* harmony export */   ErrorIcon: () => (/* binding */ _),\n/* harmony export */   LoaderIcon: () => (/* binding */ V),\n/* harmony export */   ToastBar: () => (/* binding */ F),\n/* harmony export */   ToastIcon: () => (/* binding */ M),\n/* harmony export */   Toaster: () => (/* binding */ Ie),\n/* harmony export */   \"default\": () => (/* binding */ _t),\n/* harmony export */   resolveValue: () => (/* binding */ T),\n/* harmony export */   toast: () => (/* binding */ n),\n/* harmony export */   useToaster: () => (/* binding */ D),\n/* harmony export */   useToasterStore: () => (/* binding */ I)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var goober__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! goober */ \"(app-pages-browser)/./node_modules/goober/dist/goober.modern.js\");\n/* __next_internal_client_entry_do_not_use__ CheckmarkIcon,ErrorIcon,LoaderIcon,ToastBar,ToastIcon,Toaster,default,resolveValue,toast,useToaster,useToasterStore auto */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n transform: scale(1) rotate(45deg);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0);\\n  opacity: 0;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(90deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(90deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n\\n  &:after,\\n  &:before {\\n    content: '';\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 150ms;\\n    position: absolute;\\n    border-radius: 3px;\\n    opacity: 0;\\n    background: \",\n        \";\\n    bottom: 9px;\\n    left: 4px;\\n    height: 2px;\\n    width: 12px;\\n  }\\n\\n  &:before {\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 180ms;\\n    transform: rotate(90deg);\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 12px;\\n  height: 12px;\\n  box-sizing: border-box;\\n  border: 2px solid;\\n  border-radius: 100%;\\n  border-color: \",\n        \";\\n  border-right-color: \",\n        \";\\n  animation: \",\n        \" 1s linear infinite;\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(45deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n0% {\\n\theight: 0;\\n\twidth: 0;\\n\topacity: 0;\\n}\\n40% {\\n  height: 0;\\n\twidth: 6px;\\n\topacity: 1;\\n}\\n100% {\\n  opacity: 1;\\n  height: 10px;\\n}\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n  &:after {\\n    content: '';\\n    box-sizing: border-box;\\n    animation: \",\n        \" 0.2s ease-out forwards;\\n    opacity: 0;\\n    animation-delay: 200ms;\\n    position: absolute;\\n    border-right: 2px solid;\\n    border-bottom: 2px solid;\\n    border-color: \",\n        \";\\n    bottom: 6px;\\n    left: 6px;\\n    height: 10px;\\n    width: 6px;\\n  }\\n\"\n    ]);\n    _templateObject8 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject9() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: absolute;\\n\"\n    ]);\n    _templateObject9 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject10() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-width: 20px;\\n  min-height: 20px;\\n\"\n    ]);\n    _templateObject10 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject11() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject11 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject12() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n  min-width: 20px;\\n  animation: \",\n        \" 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n\"\n    ]);\n    _templateObject12 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject13() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  align-items: center;\\n  background: #fff;\\n  color: #363636;\\n  line-height: 1.3;\\n  will-change: transform;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\\n  max-width: 350px;\\n  pointer-events: auto;\\n  padding: 8px 10px;\\n  border-radius: 8px;\\n\"\n    ]);\n    _templateObject13 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject14() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  justify-content: center;\\n  margin: 4px 10px;\\n  color: inherit;\\n  flex: 1 1 auto;\\n  white-space: pre-line;\\n\"\n    ]);\n    _templateObject14 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject15() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  z-index: 9999;\\n  > * {\\n    pointer-events: auto;\\n  }\\n\"\n    ]);\n    _templateObject15 = function() {\n        return data;\n    };\n    return data;\n}\nvar _s = $RefreshSig$();\nvar W = (e)=>typeof e == \"function\", T = (e, t)=>W(e) ? e(t) : e;\nvar U = (()=>{\n    let e = 0;\n    return ()=>(++e).toString();\n})(), b = (()=>{\n    let e;\n    return ()=>{\n        if (e === void 0 && typeof window < \"u\") {\n            let t = matchMedia(\"(prefers-reduced-motion: reduce)\");\n            e = !t || t.matches;\n        }\n        return e;\n    };\n})();\n\nvar Q = 20;\nvar S = new Map, X = 1e3, $ = (e)=>{\n    if (S.has(e)) return;\n    let t = setTimeout(()=>{\n        S.delete(e), u({\n            type: 4,\n            toastId: e\n        });\n    }, X);\n    S.set(e, t);\n}, J = (e)=>{\n    let t = S.get(e);\n    t && clearTimeout(t);\n}, v = (e, t)=>{\n    switch(t.type){\n        case 0:\n            return {\n                ...e,\n                toasts: [\n                    t.toast,\n                    ...e.toasts\n                ].slice(0, Q)\n            };\n        case 1:\n            return t.toast.id && J(t.toast.id), {\n                ...e,\n                toasts: e.toasts.map((r)=>r.id === t.toast.id ? {\n                        ...r,\n                        ...t.toast\n                    } : r)\n            };\n        case 2:\n            let { toast: o } = t;\n            return e.toasts.find((r)=>r.id === o.id) ? v(e, {\n                type: 1,\n                toast: o\n            }) : v(e, {\n                type: 0,\n                toast: o\n            });\n        case 3:\n            let { toastId: s } = t;\n            return s ? $(s) : e.toasts.forEach((r)=>{\n                $(r.id);\n            }), {\n                ...e,\n                toasts: e.toasts.map((r)=>r.id === s || s === void 0 ? {\n                        ...r,\n                        visible: !1\n                    } : r)\n            };\n        case 4:\n            return t.toastId === void 0 ? {\n                ...e,\n                toasts: []\n            } : {\n                ...e,\n                toasts: e.toasts.filter((r)=>r.id !== t.toastId)\n            };\n        case 5:\n            return {\n                ...e,\n                pausedAt: t.time\n            };\n        case 6:\n            let a = t.time - (e.pausedAt || 0);\n            return {\n                ...e,\n                pausedAt: void 0,\n                toasts: e.toasts.map((r)=>({\n                        ...r,\n                        pauseDuration: r.pauseDuration + a\n                    }))\n            };\n    }\n}, A = [], P = {\n    toasts: [],\n    pausedAt: void 0\n}, u = (e)=>{\n    P = v(P, e), A.forEach((t)=>{\n        t(P);\n    });\n}, Y = {\n    blank: 4e3,\n    error: 4e3,\n    success: 2e3,\n    loading: 1 / 0,\n    custom: 4e3\n}, I = function() {\n    let e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    let [t, o] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(P);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>(A.push(o), ()=>{\n            let a = A.indexOf(o);\n            a > -1 && A.splice(a, 1);\n        }), [\n        t\n    ]);\n    let s = t.toasts.map((a)=>{\n        var r, c;\n        return {\n            ...e,\n            ...e[a.type],\n            ...a,\n            duration: a.duration || ((r = e[a.type]) == null ? void 0 : r.duration) || (e == null ? void 0 : e.duration) || Y[a.type],\n            style: {\n                ...e.style,\n                ...(c = e[a.type]) == null ? void 0 : c.style,\n                ...a.style\n            }\n        };\n    });\n    return {\n        ...t,\n        toasts: s\n    };\n};\nvar G = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"blank\", o = arguments.length > 2 ? arguments[2] : void 0;\n    return {\n        createdAt: Date.now(),\n        visible: !0,\n        type: t,\n        ariaProps: {\n            role: \"status\",\n            \"aria-live\": \"polite\"\n        },\n        message: e,\n        pauseDuration: 0,\n        ...o,\n        id: (o == null ? void 0 : o.id) || U()\n    };\n}, h = (e)=>(t, o)=>{\n        let s = G(t, e, o);\n        return u({\n            type: 2,\n            toast: s\n        }), s.id;\n    }, n = (e, t)=>h(\"blank\")(e, t);\nn.error = h(\"error\");\nn.success = h(\"success\");\nn.loading = h(\"loading\");\nn.custom = h(\"custom\");\nn.dismiss = (e)=>{\n    u({\n        type: 3,\n        toastId: e\n    });\n};\nn.remove = (e)=>u({\n        type: 4,\n        toastId: e\n    });\nn.promise = (e, t, o)=>{\n    let s = n.loading(t.loading, {\n        ...o,\n        ...o == null ? void 0 : o.loading\n    });\n    return e.then((a)=>(n.success(T(t.success, a), {\n            id: s,\n            ...o,\n            ...o == null ? void 0 : o.success\n        }), a)).catch((a)=>{\n        n.error(T(t.error, a), {\n            id: s,\n            ...o,\n            ...o == null ? void 0 : o.error\n        });\n    }), e;\n};\n\nvar Z = (e, t)=>{\n    u({\n        type: 1,\n        toast: {\n            id: e,\n            height: t\n        }\n    });\n}, ee = ()=>{\n    u({\n        type: 5,\n        time: Date.now()\n    });\n}, D = (e)=>{\n    let { toasts: t, pausedAt: o } = I(e);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (o) return;\n        let r = Date.now(), c = t.map((i)=>{\n            if (i.duration === 1 / 0) return;\n            let d = (i.duration || 0) + i.pauseDuration - (r - i.createdAt);\n            if (d < 0) {\n                i.visible && n.dismiss(i.id);\n                return;\n            }\n            return setTimeout(()=>n.dismiss(i.id), d);\n        });\n        return ()=>{\n            c.forEach((i)=>i && clearTimeout(i));\n        };\n    }, [\n        t,\n        o\n    ]);\n    let s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        o && u({\n            type: 6,\n            time: Date.now()\n        });\n    }, [\n        o\n    ]), a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((r, c)=>{\n        let { reverseOrder: i = !1, gutter: d = 8, defaultPosition: p } = c || {}, g = t.filter((m)=>(m.position || p) === (r.position || p) && m.height), E = g.findIndex((m)=>m.id === r.id), x = g.filter((m, R)=>R < E && m.visible).length;\n        return g.filter((m)=>m.visible).slice(...i ? [\n            x + 1\n        ] : [\n            0,\n            x\n        ]).reduce((m, R)=>m + (R.height || 0) + d, 0);\n    }, [\n        t\n    ]);\n    return {\n        toasts: t,\n        handlers: {\n            updateHeight: Z,\n            startPause: ee,\n            endPause: s,\n            calculateOffset: a\n        }\n    };\n};\n\n\n\n\n\nvar oe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject()), re = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject1()), se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject2()), _ = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject3(), (e)=>e.primary || \"#ff4b4b\", oe, re, (e)=>e.secondary || \"#fff\", se);\n\nvar ne = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject4()), V = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject5(), (e)=>e.secondary || \"#e0e0e0\", (e)=>e.primary || \"#616161\", ne);\n\nvar pe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject6()), de = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject7()), w = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject8(), (e)=>e.primary || \"#61d345\", pe, de, (e)=>e.secondary || \"#fff\");\nvar ue = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject9()), le = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject10()), Te = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject11()), fe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject12(), Te), M = (param)=>{\n    let { toast: e } = param;\n    let { icon: t, type: o, iconTheme: s } = e;\n    return t !== void 0 ? typeof t == \"string\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(fe, null, t) : t : o === \"blank\" ? null : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(le, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(V, {\n        ...s\n    }), o !== \"loading\" && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ue, null, o === \"error\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_, {\n        ...s\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(w, {\n        ...s\n    })));\n};\nvar ye = (e)=>\"\\n0% {transform: translate3d(0,\".concat(e * -200, \"%,0) scale(.6); opacity:.5;}\\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\\n\"), ge = (e)=>\"\\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\\n100% {transform: translate3d(0,\".concat(e * -150, \"%,-1px) scale(.6); opacity:0;}\\n\"), he = \"0%{opacity:0;} 100%{opacity:1;}\", xe = \"0%{opacity:1;} 100%{opacity:0;}\", be = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject13()), Se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject14()), Ae = (e, t)=>{\n    let s = e.includes(\"top\") ? 1 : -1, [a, r] = b() ? [\n        he,\n        xe\n    ] : [\n        ye(s),\n        ge(s)\n    ];\n    return {\n        animation: t ? \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(a), \" 0.35s cubic-bezier(.21,1.02,.73,1) forwards\") : \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(r), \" 0.4s forwards cubic-bezier(.06,.71,.55,1)\")\n    };\n}, F = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.memo((param)=>{\n    let { toast: e, position: t, style: o, children: s } = param;\n    let a = e.height ? Ae(e.position || t || \"top-center\", e.visible) : {\n        opacity: 0\n    }, r = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(M, {\n        toast: e\n    }), c = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Se, {\n        ...e.ariaProps\n    }, T(e.message, e));\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(be, {\n        className: e.className,\n        style: {\n            ...a,\n            ...o,\n            ...e.style\n        }\n    }, typeof s == \"function\" ? s({\n        icon: r,\n        message: c\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, r, c));\n});\n\n\n(0,goober__WEBPACK_IMPORTED_MODULE_2__.setup)(react__WEBPACK_IMPORTED_MODULE_1__.createElement);\nvar Ee = (param)=>{\n    let { id: e, className: t, style: o, onHeightUpdate: s, children: a } = param;\n    _s();\n    let r = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"Ee.useCallback[r]\": (c)=>{\n            if (c) {\n                let i = {\n                    \"Ee.useCallback[r].i\": ()=>{\n                        let d = c.getBoundingClientRect().height;\n                        s(e, d);\n                    }\n                }[\"Ee.useCallback[r].i\"];\n                i(), new MutationObserver(i).observe(c, {\n                    subtree: !0,\n                    childList: !0,\n                    characterData: !0\n                });\n            }\n        }\n    }[\"Ee.useCallback[r]\"], [\n        e,\n        s\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: r,\n        className: t,\n        style: o\n    }, a);\n}, Re = (e, t)=>{\n    let o = e.includes(\"top\"), s = o ? {\n        top: 0\n    } : {\n        bottom: 0\n    }, a = e.includes(\"center\") ? {\n        justifyContent: \"center\"\n    } : e.includes(\"right\") ? {\n        justifyContent: \"flex-end\"\n    } : {};\n    return {\n        left: 0,\n        right: 0,\n        display: \"flex\",\n        position: \"absolute\",\n        transition: b() ? void 0 : \"all 230ms cubic-bezier(.21,1.02,.73,1)\",\n        transform: \"translateY(\".concat(t * (o ? 1 : -1), \"px)\"),\n        ...s,\n        ...a\n    };\n}, ve = (0,goober__WEBPACK_IMPORTED_MODULE_2__.css)(_templateObject15()), O = 16, Ie = (param)=>{\n    let { reverseOrder: e, position: t = \"top-center\", toastOptions: o, gutter: s, children: a, containerStyle: r, containerClassName: c } = param;\n    let { toasts: i, handlers: d } = D(o);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        style: {\n            position: \"fixed\",\n            zIndex: 9999,\n            top: O,\n            left: O,\n            right: O,\n            bottom: O,\n            pointerEvents: \"none\",\n            ...r\n        },\n        className: c,\n        onMouseEnter: d.startPause,\n        onMouseLeave: d.endPause\n    }, i.map((p)=>{\n        let g = p.position || t, E = d.calculateOffset(p, {\n            reverseOrder: e,\n            gutter: s,\n            defaultPosition: t\n        }), x = Re(g, E);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Ee, {\n            id: p.id,\n            key: p.id,\n            onHeightUpdate: d.updateHeight,\n            className: p.visible ? ve : \"\",\n            style: x\n        }, p.type === \"custom\" ? T(p.message, p) : a ? a(p) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(F, {\n            toast: p,\n            position: g\n        }));\n    }));\n};\n_s(Ee, \"fkjAOlWVa9KjVLoOEmLo+ckqzCw=\");\nvar _t = n;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ResumeBuilder = ()=>{\n    _s();\n    // State for initial setup\n    const [setupComplete, setSetupComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [inputMode, setInputMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeFaq, setActiveFaq] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for form data\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            name: \"\",\n            email: \"\",\n            phone: \"\"\n        },\n        education: {\n            degree: \"\",\n            institution: \"\",\n            field: \"\",\n            graduationYear: \"\"\n        }\n    });\n    // UI state\n    const [currentSection, setCurrentSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"personal\");\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [transcript, setTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const recognitionRef = useRef(null);\n    const previewRef = useRef(null);\n    const synthRef = useRef(null);\n    const mediaRecorderRef = useRef(null);\n    const audioChunksRef = useRef([]);\n    // Add these new states\n    const [needsConfirmation, setNeedsConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmedTranscript, setConfirmedTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedVoice, setSelectedVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showComingSoon, setShowComingSoon] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Refs\n    // Questions in both languages\n    const questions = {\n        personal: {\n            english: [\n                {\n                    id: \"name\",\n                    text: \"What is your full name?\"\n                },\n                {\n                    id: \"email\",\n                    text: \"What is your email address?\",\n                    forceEnglish: true\n                },\n                {\n                    id: \"phone\",\n                    text: \"What is your phone number?\",\n                    forceEnglish: true\n                }\n            ],\n            hindi: [\n                {\n                    id: \"name\",\n                    text: \"आपका पूरा नाम क्या है?\"\n                },\n                {\n                    id: \"email\",\n                    text: \"आपका ईमेल पता क्या है?\",\n                    forceEnglish: true\n                },\n                {\n                    id: \"phone\",\n                    text: \"आपका फोन नंबर क्या है?\",\n                    forceEnglish: true\n                }\n            ]\n        },\n        education: {\n            english: [\n                {\n                    id: \"degree\",\n                    text: \"What degree did you earn?\"\n                },\n                {\n                    id: \"institution\",\n                    text: \"Which institution did you attend?\"\n                },\n                {\n                    id: \"field\",\n                    text: \"What was your field of study?\"\n                },\n                {\n                    id: \"graduationYear\",\n                    text: \"When did you graduate? (YYYY)\",\n                    forceEnglish: true\n                }\n            ],\n            hindi: [\n                {\n                    id: \"degree\",\n                    text: \"आपने कौन सी डिग्री प्राप्त की?\"\n                },\n                {\n                    id: \"institution\",\n                    text: \"आपने किस संस्थान में अध्ययन किया?\"\n                },\n                {\n                    id: \"field\",\n                    text: \"आपका अध्ययन क्षेत्र क्या था?\"\n                },\n                {\n                    id: \"graduationYear\",\n                    text: \"आपने कब स्नातक किया? (YYYY)\",\n                    forceEnglish: true\n                }\n            ]\n        }\n    };\n    // Initialize speech recognition and synthesis\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResumeBuilder.useEffect\": ()=>{\n            if ( true && setupComplete) {\n                // Initialize speech recognition with fallback to MediaRecorder\n                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n                if (SpeechRecognition) {\n                    recognitionRef.current = new SpeechRecognition();\n                    recognitionRef.current.continuous = true;\n                    recognitionRef.current.interimResults = true;\n                    recognitionRef.current.lang = language === \"hindi\" ? \"hi-IN\" : \"en-US\";\n                    recognitionRef.current.onresult = ({\n                        \"ResumeBuilder.useEffect\": (event)=>{\n                            const currentTranscript = Array.from(event.results).map({\n                                \"ResumeBuilder.useEffect.currentTranscript\": (result)=>result[0]\n                            }[\"ResumeBuilder.useEffect.currentTranscript\"]).map({\n                                \"ResumeBuilder.useEffect.currentTranscript\": (result)=>result.transcript\n                            }[\"ResumeBuilder.useEffect.currentTranscript\"]).join(\"\");\n                            setTranscript(currentTranscript);\n                            if (event.results[0].isFinal) {\n                                processTranscript(currentTranscript);\n                            }\n                        }\n                    })[\"ResumeBuilder.useEffect\"];\n                    recognitionRef.current.onerror = ({\n                        \"ResumeBuilder.useEffect\": (event)=>{\n                            console.error(\"Speech recognition error\", event.error);\n                            setIsRecording(false);\n                            (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast)({\n                                title: \"Recording Error\",\n                                description: \"Could not record: \".concat(event.error),\n                                variant: \"destructive\"\n                            });\n                        }\n                    })[\"ResumeBuilder.useEffect\"];\n                    recognitionRef.current.onend = ({\n                        \"ResumeBuilder.useEffect\": ()=>{\n                            if (isRecording) {\n                                // Automatically restart recording if it ended unexpectedly\n                                recognitionRef.current.start();\n                            }\n                        }\n                    })[\"ResumeBuilder.useEffect\"];\n                }\n                // Initialize speech synthesis\n                if (\"speechSynthesis\" in window) {\n                    synthRef.current = window.speechSynthesis;\n                    // Load voices and select appropriate one\n                    const loadVoices = {\n                        \"ResumeBuilder.useEffect.loadVoices\": ()=>{\n                            const voices = synthRef.current.getVoices();\n                            const preferredVoice = voices.find({\n                                \"ResumeBuilder.useEffect.loadVoices.preferredVoice\": (v)=>language === \"hindi\" ? v.lang.startsWith(\"hi\") || v.name.includes(\"Hindi\") : v.lang.startsWith(\"en\") && v.name.includes(\"Female\")\n                            }[\"ResumeBuilder.useEffect.loadVoices.preferredVoice\"]);\n                            setSelectedVoice(preferredVoice || voices[0]);\n                        }\n                    }[\"ResumeBuilder.useEffect.loadVoices\"];\n                    synthRef.current.onvoiceschanged = loadVoices;\n                    loadVoices();\n                }\n            }\n            return ({\n                \"ResumeBuilder.useEffect\": ()=>{\n                    if (recognitionRef.current) {\n                        recognitionRef.current.stop();\n                    }\n                    if (mediaRecorderRef.current) {\n                        mediaRecorderRef.current.stop();\n                    }\n                    if (synthRef.current) {\n                        synthRef.current.cancel();\n                    }\n                }\n            })[\"ResumeBuilder.useEffect\"];\n        }\n    }[\"ResumeBuilder.useEffect\"], [\n        language,\n        setupComplete\n    ]);\n    const processTranscript = (transcript)=>{\n        // Apply Hindi corrections if needed\n        if (language === \"hindi\") {\n            transcript = correctHindiTranscript(transcript);\n        }\n        const currentQuestionObj = questions[currentSection][language][currentQuestion];\n        // For critical fields, ask for confirmation\n        if (currentQuestionObj.forceEnglish || currentSection === \"personal\") {\n            setConfirmedTranscript(transcript);\n            setNeedsConfirmation(true);\n        } else {\n            updateFormField(transcript);\n        }\n    };\n    const correctHindiTranscript = (text)=>{\n        // Common corrections for Hindi speech recognition\n        const corrections = {\n            नाम: [\n                \"नम\",\n                \"नामा\",\n                \"नमः\"\n            ],\n            ईमेल: [\n                \"इमेल\",\n                \"मेल\"\n            ],\n            फोन: [\n                \"फ़ोन\",\n                \"पोन\"\n            ],\n            डिग्री: [\n                \"डिग्री\",\n                \"डिग्रि\"\n            ],\n            संस्थान: [\n                \"संस्था\",\n                \"सस्थान\"\n            ]\n        };\n        Object.entries(corrections).forEach((param)=>{\n            let [correct, incorrects] = param;\n            incorrects.forEach((incorrect)=>{\n                const regex = new RegExp(incorrect, \"g\");\n                text = text.replace(regex, correct);\n            });\n        });\n        return text;\n    };\n    const handleConfirmTranscript = (confirmed)=>{\n        if (confirmed) {\n            updateFormField(confirmedTranscript);\n        }\n        setNeedsConfirmation(false);\n        setConfirmedTranscript(\"\");\n    };\n    // Speak the current question when question changes or input mode changes to voice\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResumeBuilder.useEffect\": ()=>{\n            if (inputMode === \"voice\" && synthRef.current && setupComplete) {\n                speakQuestion();\n            }\n        }\n    }[\"ResumeBuilder.useEffect\"], [\n        currentQuestion,\n        currentSection,\n        language,\n        inputMode,\n        setupComplete\n    ]);\n    const startRecording = async ()=>{\n        try {\n            setIsRecording(true);\n            setTranscript(\"\");\n            if (recognitionRef.current) {\n                recognitionRef.current.start();\n            } else {\n                // Fallback to MediaRecorder API if SpeechRecognition not available\n                const stream = await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                mediaRecorderRef.current = new MediaRecorder(stream);\n                audioChunksRef.current = [];\n                mediaRecorderRef.current.ondataavailable = (e)=>{\n                    audioChunksRef.current.push(e.data);\n                };\n                mediaRecorderRef.current.onstop = async ()=>{\n                    const audioBlob = new Blob(audioChunksRef.current);\n                    await processAudioBlob(audioBlob);\n                    stream.getTracks().forEach((track)=>track.stop());\n                };\n                mediaRecorderRef.current.start();\n            }\n            // Speak the question\n            speakQuestion();\n        } catch (error) {\n            console.error(\"Recording error:\", error);\n            setIsRecording(false);\n            (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast)({\n                title: \"Recording Error\",\n                description: \"Could not access microphone\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const stopRecording = ()=>{\n        if (recognitionRef.current) {\n            recognitionRef.current.stop();\n        }\n        if (mediaRecorderRef.current) {\n            mediaRecorderRef.current.stop();\n        }\n        setIsRecording(false);\n    };\n    const speakQuestion = ()=>{\n        if (!synthRef.current || !selectedVoice) return;\n        synthRef.current.cancel();\n        const questionText = questions[currentSection][language][currentQuestion].text;\n        const utterance = new SpeechSynthesisUtterance(questionText);\n        utterance.voice = selectedVoice;\n        utterance.lang = language === \"hindi\" ? \"hi-IN\" : \"en-US\";\n        utterance.rate = 0.9; // Slightly slower for better comprehension\n        // Add emphasis on important words\n        if (language === \"hindi\") {\n            utterance.pitch = 1.1;\n        }\n        synthRef.current.speak(utterance);\n    };\n    const updateFormField = (value)=>{\n        const currentQuestionObj = questions[currentSection][language][currentQuestion];\n        const fieldId = currentQuestionObj.id;\n        // Additional validation for specific fields\n        if (currentQuestionObj.forceEnglish) {\n            value = value.replace(/[^a-zA-Z0-9@._+-]/g, \"\");\n            // Special handling for email\n            if (fieldId === \"email\" && !value.includes(\"@\")) {\n                (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast)({\n                    title: \"Invalid Email\",\n                    description: \"Please include @ in your email address\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Special handling for phone numbers\n            if (fieldId === \"phone\" && !/^\\d+$/.test(value)) {\n                (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast)({\n                    title: \"Invalid Phone\",\n                    description: \"Please enter numbers only\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        setFormData((prev)=>({\n                ...prev,\n                [currentSection]: {\n                    ...prev[currentSection],\n                    [fieldId]: value\n                }\n            }));\n        // Auto-advance for short fields if voice mode\n        if (inputMode === \"voice\" && value.length > 3 && (fieldId === \"email\" || fieldId === \"phone\" || fieldId === \"graduationYear\")) {\n            setTimeout(handleNext, 500);\n        }\n    };\n    const handleNext = ()=>{\n        if (currentQuestion < questions[currentSection][language].length - 1) {\n            setCurrentQuestion(currentQuestion + 1);\n        } else {\n            // Move to next section or submit\n            if (currentSection === \"personal\") {\n                setCurrentSection(\"education\");\n                setCurrentQuestion(0);\n            } else {\n                handleSubmit();\n            }\n        }\n    };\n    const handleSubmit = async ()=>{\n        setIsSubmitting(true);\n        try {\n            // Validate required fields\n            const requiredFields = [\n                ...questions.personal.english.map((q)=>q.id),\n                ...questions.education.english.map((q)=>q.id)\n            ];\n            const isValid = requiredFields.every((field)=>{\n                const section = field in formData.personal ? \"personal\" : \"education\";\n                return formData[section][field];\n            });\n            if (!isValid) {\n                (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast)({\n                    title: \"Missing Information\",\n                    description: \"Please fill all required fields\",\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Show progress bar\n            setShowProgressBar(true);\n            // Call Gemini API for resume generation\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            // Set the resume URL from the API response\n            setResumeUrl(data.downloadUrl);\n        // Progress bar will handle the completion timing\n        } catch (error) {\n            console.error(\"Error generating resume:\", error);\n            (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to generate resume\",\n                variant: \"destructive\"\n            });\n            setIsSubmitting(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsSubmitting(false);\n        (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast)({\n            title: \"Success!\",\n            description: \"Your resume has been generated successfully\"\n        });\n    };\n    const handleEdit = ()=>{\n        setResumeGenerated(false);\n        setCurrentSection(\"personal\");\n        setCurrentQuestion(0);\n    };\n    const handleDownload = ()=>{\n        if (!resumeUrl) return;\n        const link = document.createElement(\"a\");\n        link.href = resumeUrl;\n        link.download = \"\".concat(formData.personal.name || \"resume\", \".pdf\");\n        link.click();\n    };\n    const getCurrentFieldValue = ()=>{\n        var _formData_currentSection;\n        const currentQuestionObj = questions[currentSection][language][currentQuestion];\n        const fieldId = currentQuestionObj.id;\n        return ((_formData_currentSection = formData[currentSection]) === null || _formData_currentSection === void 0 ? void 0 : _formData_currentSection[fieldId]) || \"\";\n    };\n    const handleInputModeChange = (mode)=>{\n        setInputMode(mode);\n        if (mode === \"voice\" && setupComplete) {\n            // Start recording automatically when switching to voice mode\n            setTimeout(()=>startRecording(), 100);\n        } else if (isRecording) {\n            stopRecording();\n        }\n    };\n    const completeSetup = ()=>{\n        if (!language || !inputMode) {\n            (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast)({\n                title: \"Selection Required\",\n                description: \"Please select both language and input method\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setSetupComplete(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResumeBuilder.useEffect\": ()=>{\n            const hasSeenModal = localStorage.getItem('hasSeenComingSoon');\n            if (hasSeenModal) {\n                setShowComingSoon(false);\n            }\n        }\n    }[\"ResumeBuilder.useEffect\"], []);\n    const handleCloseModal = ()=>{\n        localStorage.setItem('hasSeenComingSoon', 'true');\n        setShowComingSoon(false);\n    };\n    // Then update the modal usage:\n    {\n        showComingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComingSoonModal, {\n            onClose: handleCloseModal\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 500,\n            columnNumber: 3\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen pt-20 md:pt-24 bg-gradient-to-b from-black to-[#0A0A0A] text-white p-4 md:p-8\",\n        children: [\n            showComingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComingSoonModal, {\n                onClose: ()=>setShowComingSoon(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 508,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 511,\n                columnNumber: 7\n            }, undefined),\n            needsConfirmation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConfirmationDialog, {\n                language: language,\n                confirmedTranscript: confirmedTranscript,\n                onConfirm: ()=>handleConfirmTranscript(true),\n                onRetry: ()=>setNeedsConfirmation(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 517,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto\",\n                children: [\n                    !setupComplete ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeScreen, {\n                        language: language,\n                        inputMode: inputMode,\n                        onLanguageChange: setLanguage,\n                        onInputModeChange: setInputMode,\n                        onCompleteSetup: completeSetup\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 526,\n                        columnNumber: 11\n                    }, undefined) : !resumeGenerated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500\",\n                                children: \"AI Resume Builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 535,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    inputMode === \"voice\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoiceInput, {\n                                        transcript: transcript,\n                                        currentValue: getCurrentFieldValue(),\n                                        isRecording: isRecording,\n                                        isProcessing: isProcessing,\n                                        language: language,\n                                        onSwitchToText: ()=>handleInputModeChange(\"text\"),\n                                        onToggleRecording: isRecording ? stopRecording : startRecording\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TextInput, {\n                                        value: getCurrentFieldValue(),\n                                        onChange: updateFormField,\n                                        placeholder: questions[currentSection][language][currentQuestion].forceEnglish ? language === \"hindi\" ? \"केवल अंग्रेजी में टाइप करें\" : \"Type in English only\" : language === \"hindi\" ? \"अपना उत्तर टाइप करें...\" : \"Type your answer...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreviewSection, {\n                                        formData: formData,\n                                        language: language,\n                                        isProcessing: isProcessing\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 538,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SuccessScreen, {\n                        formData: formData,\n                        language: language,\n                        resumeUrl: resumeUrl,\n                        onDownload: handleDownload,\n                        onEdit: handleEdit\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, undefined),\n                    !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FAQSection, {\n                        language: language,\n                        activeFaq: activeFaq,\n                        setActiveFaq: setActiveFaq\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 584,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 506,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"o7GZVFuQxOgXDL1Yl/340pGPhio=\");\n_c = ResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"ResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ })

});