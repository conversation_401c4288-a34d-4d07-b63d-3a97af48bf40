import { useState, useEffect, useMemo } from 'react';

// Real-time ATS analysis hook
export const useATSAnalysis = (formData) => {
  const [analysisResults, setAnalysisResults] = useState({
    overallScore: 0,
    categoryScores: {},
    recommendations: [],
    fieldAnalysis: {}
  });

  // Analyze individual fields in real-time
  const analyzeField = (fieldName, value, context = {}) => {
    const analysis = {
      score: 0,
      status: 'incomplete', // incomplete, warning, good, excellent
      suggestions: [],
      keywords: []
    };

    switch (fieldName) {
      case 'firstName':
      case 'lastName':
        if (value && value.trim().length > 0) {
          analysis.score = 100;
          analysis.status = 'excellent';
        } else {
          analysis.suggestions.push('Required field for ATS processing');
          analysis.status = 'incomplete';
        }
        break;

      case 'email':
        if (!value) {
          analysis.suggestions.push('Email is required for contact');
          analysis.status = 'incomplete';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          analysis.score = 30;
          analysis.suggestions.push('Use a valid email format');
          analysis.status = 'warning';
        } else {
          analysis.score = 100;
          analysis.status = 'excellent';
          if (value.includes('gmail') || value.includes('outlook') || value.includes('yahoo')) {
            analysis.suggestions.push('Consider using a professional email domain');
          }
        }
        break;

      case 'phone':
        if (!value) {
          analysis.suggestions.push('Phone number improves reachability');
          analysis.status = 'warning';
          analysis.score = 0;
        } else if (value.length < 10) {
          analysis.score = 50;
          analysis.suggestions.push('Include complete phone number with area code');
          analysis.status = 'warning';
        } else {
          analysis.score = 100;
          analysis.status = 'excellent';
        }
        break;

      case 'location':
        if (!value) {
          analysis.suggestions.push('Location helps with local job matching');
          analysis.status = 'warning';
          analysis.score = 0;
        } else if (value.length < 5) {
          analysis.score = 60;
          analysis.suggestions.push('Include city and state for better matching');
          analysis.status = 'warning';
        } else {
          analysis.score = 100;
          analysis.status = 'excellent';
        }
        break;

      case 'summary':
        if (!value) {
          analysis.suggestions.push('Professional summary is crucial for ATS ranking');
          analysis.status = 'incomplete';
          analysis.score = 0;
        } else if (value.length < 50) {
          analysis.score = 40;
          analysis.suggestions.push('Expand to 50+ characters for better impact');
          analysis.status = 'warning';
        } else if (value.length > 300) {
          analysis.score = 80;
          analysis.suggestions.push('Consider shortening to under 300 characters');
          analysis.status = 'good';
        } else {
          analysis.score = 100;
          analysis.status = 'excellent';
          
          // Check for keywords
          const keywords = ['experienced', 'skilled', 'proficient', 'expertise', 'results-driven'];
          const foundKeywords = keywords.filter(keyword => 
            value.toLowerCase().includes(keyword)
          );
          analysis.keywords = foundKeywords;
          
          if (foundKeywords.length === 0) {
            analysis.suggestions.push('Include professional keywords like "experienced", "skilled", "proficient"');
          }
        }
        break;

      case 'experience_description':
        if (!value) {
          analysis.suggestions.push('Detailed descriptions improve ATS ranking');
          analysis.status = 'warning';
          analysis.score = 0;
        } else if (value.length < 50) {
          analysis.score = 50;
          analysis.suggestions.push('Add more detail about your achievements');
          analysis.status = 'warning';
        } else {
          analysis.score = 80;
          analysis.status = 'good';
          
          // Check for quantified achievements
          const hasNumbers = /\d+/.test(value);
          const hasPercentage = value.includes('%');
          const hasDollar = value.includes('$');
          
          if (hasNumbers || hasPercentage || hasDollar) {
            analysis.score = 100;
            analysis.status = 'excellent';
            analysis.keywords.push('quantified achievements');
          } else {
            analysis.suggestions.push('Add numbers, percentages, or metrics to quantify achievements');
          }
          
          // Check for action verbs
          const actionVerbs = ['led', 'developed', 'implemented', 'managed', 'created', 'built', 'designed', 'optimized'];
          const hasActionVerb = actionVerbs.some(verb => 
            value.toLowerCase().includes(verb)
          );
          
          if (hasActionVerb) {
            analysis.keywords.push('action verbs');
          } else {
            analysis.suggestions.push('Start with strong action verbs like "Led", "Developed", "Implemented"');
          }
        }
        break;

      case 'technical_skills':
        const skillsArray = Array.isArray(value) ? value : [];
        if (skillsArray.length === 0) {
          analysis.suggestions.push('Technical skills are essential for ATS matching');
          analysis.status = 'incomplete';
          analysis.score = 0;
        } else if (skillsArray.length < 6) {
          analysis.score = 60;
          analysis.suggestions.push('Add more relevant technical skills (6-12 is optimal)');
          analysis.status = 'warning';
        } else if (skillsArray.length > 15) {
          analysis.score = 80;
          analysis.suggestions.push('Focus on most relevant skills (6-12 is optimal)');
          analysis.status = 'good';
        } else {
          analysis.score = 100;
          analysis.status = 'excellent';
        }
        break;

      default:
        analysis.score = value ? 100 : 0;
        analysis.status = value ? 'good' : 'incomplete';
    }

    return analysis;
  };

  // Calculate comprehensive ATS analysis
  const comprehensiveAnalysis = useMemo(() => {
    const fieldAnalysis = {};
    let totalScore = 0;
    let fieldCount = 0;

    // Analyze personal information
    ['firstName', 'lastName', 'email', 'phone', 'location', 'summary'].forEach(field => {
      const analysis = analyzeField(field, formData.personal?.[field], formData);
      fieldAnalysis[field] = analysis;
      totalScore += analysis.score;
      fieldCount++;
    });

    // Analyze experience descriptions
    formData.experience?.forEach((exp, index) => {
      if (exp.title && exp.company) {
        const analysis = analyzeField('experience_description', exp.description, exp);
        fieldAnalysis[`experience_${index}`] = analysis;
        totalScore += analysis.score;
        fieldCount++;
      }
    });

    // Analyze technical skills
    const skillsAnalysis = analyzeField('technical_skills', formData.skills?.technical, formData);
    fieldAnalysis.technical_skills = skillsAnalysis;
    totalScore += skillsAnalysis.score;
    fieldCount++;

    const overallScore = fieldCount > 0 ? Math.round(totalScore / fieldCount) : 0;

    // Generate recommendations based on analysis
    const recommendations = [];
    Object.entries(fieldAnalysis).forEach(([field, analysis]) => {
      if (analysis.suggestions.length > 0) {
        recommendations.push({
          field,
          type: analysis.status === 'incomplete' ? 'error' : 'warning',
          message: analysis.suggestions[0],
          score: analysis.score
        });
      }
    });

    // Sort recommendations by priority (incomplete first, then by score)
    recommendations.sort((a, b) => {
      if (a.type === 'error' && b.type !== 'error') return -1;
      if (b.type === 'error' && a.type !== 'error') return 1;
      return a.score - b.score;
    });

    return {
      overallScore,
      fieldAnalysis,
      recommendations: recommendations.slice(0, 8), // Top 8 recommendations
      completionPercentage: Math.round((fieldCount / 10) * 100) // Assuming 10 key fields
    };
  }, [formData]);

  useEffect(() => {
    setAnalysisResults(comprehensiveAnalysis);
  }, [comprehensiveAnalysis]);

  return {
    ...analysisResults,
    analyzeField,
    getFieldStatus: (fieldName) => analysisResults.fieldAnalysis[fieldName]?.status || 'incomplete',
    getFieldScore: (fieldName) => analysisResults.fieldAnalysis[fieldName]?.score || 0,
    getFieldSuggestions: (fieldName) => analysisResults.fieldAnalysis[fieldName]?.suggestions || []
  };
};

export default useATSAnalysis;
