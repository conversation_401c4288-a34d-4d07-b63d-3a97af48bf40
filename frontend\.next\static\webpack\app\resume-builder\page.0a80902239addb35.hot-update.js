"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* harmony import */ var _components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ATSScoreCircle */ \"(app-pages-browser)/./src/components/ATSScoreCircle.jsx\");\n/* harmony import */ var _components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/EnhancedResumePreview */ \"(app-pages-browser)/./src/components/EnhancedResumePreview.jsx\");\n/* harmony import */ var _components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/PDFDownload */ \"(app-pages-browser)/./src/components/PDFDownload.jsx\");\n/* harmony import */ var _components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ResumeBuilderModeToggle */ \"(app-pages-browser)/./src/components/ResumeBuilderModeToggle.jsx\");\n/* harmony import */ var _components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ResumeUpload */ \"(app-pages-browser)/./src/components/ResumeUpload.jsx\");\n/* harmony import */ var _components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ATSAnalysisDisplay */ \"(app-pages-browser)/./src/components/ATSAnalysisDisplay.jsx\");\n/* harmony import */ var _components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/JobDescriptionInput */ \"(app-pages-browser)/./src/components/JobDescriptionInput.jsx\");\n/* harmony import */ var _components_BeforeAfterComparison__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/BeforeAfterComparison */ \"(app-pages-browser)/./src/components/BeforeAfterComparison.jsx\");\n/* harmony import */ var _components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ResumeFormComponents */ \"(app-pages-browser)/./src/components/ResumeFormComponents.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import SuccessScreen from \"@/components/SuccessScreen\";\n\nconst ResumeBuilder = ()=>{\n    _s();\n    // Main state management\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for upload functionality\n    const [builderMode, setBuilderMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create'); // 'create', 'upload', 'analyze'\n    const [uploadAnalysis, setUploadAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAnalysis, setShowAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced upload workflow state\n    const [showJobDescriptionInput, setShowJobDescriptionInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingTargeted, setIsGeneratingTargeted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetedResumeData, setTargetedResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ]\n    });\n    // Steps configuration\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            description: \"Tell us about yourself\"\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: \"Your academic background\"\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: \"Your work experience\"\n        },\n        {\n            id: 3,\n            title: \"Skills & Projects\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Showcase your abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: \"Finalize your resume\"\n        }\n    ];\n    // Update form data\n    const updateFormData = function(section, field, value) {\n        let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        setFormData((prev)=>{\n            if (index !== null && Array.isArray(prev[section])) {\n                const newArray = [\n                    ...prev[section]\n                ];\n                newArray[index] = {\n                    ...newArray[index],\n                    [field]: value\n                };\n                return {\n                    ...prev,\n                    [section]: newArray\n                };\n            } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                return {\n                    ...prev,\n                    [section]: {\n                        ...prev[section],\n                        [field]: value\n                    }\n                };\n            }\n            return prev;\n        });\n    };\n    // Add new item to array sections\n    const addArrayItem = (section, template)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: [\n                    ...prev[section],\n                    {\n                        ...template,\n                        id: Math.random().toString(36).substring(2, 11)\n                    }\n                ]\n            }));\n    };\n    // Remove item from array sections\n    const removeArrayItem = (section, id)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: prev[section].filter((item)=>item.id !== id)\n            }));\n    };\n    // Navigation functions\n    const nextStep = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    // Generate resume with AI\n    const generateResume = async ()=>{\n        try {\n            var _data_resumeData_atsScore, _data_resumeData, _data_resumeData_atsScore1, _data_resumeData1;\n            console.log('🚀 Starting resume generation...');\n            console.log('📝 Form data:', formData);\n            setIsGenerating(true);\n            setShowProgressBar(true);\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            console.log('📡 API response status:', response.status);\n            console.log('📡 API response headers:', Object.fromEntries(response.headers.entries()));\n            // Check if response is actually JSON\n            const contentType = response.headers.get('content-type');\n            if (!contentType || !contentType.includes('application/json')) {\n                const textResponse = await response.text();\n                console.error('❌ Non-JSON response received:', textResponse);\n                throw new Error('Server returned non-JSON response');\n            }\n            const data = await response.json();\n            console.log('📊 API response data:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            // Store the enhanced resume data and ATS information\n            console.log('✅ Setting resume data...');\n            setResumeUrl(data.downloadUrl);\n            setResumeData(data.resumeData);\n            setAtsScore(data.atsScore || ((_data_resumeData = data.resumeData) === null || _data_resumeData === void 0 ? void 0 : (_data_resumeData_atsScore = _data_resumeData.atsScore) === null || _data_resumeData_atsScore === void 0 ? void 0 : _data_resumeData_atsScore.overall) || 75);\n            setSuggestions(data.suggestions || ((_data_resumeData1 = data.resumeData) === null || _data_resumeData1 === void 0 ? void 0 : (_data_resumeData_atsScore1 = _data_resumeData1.atsScore) === null || _data_resumeData_atsScore1 === void 0 ? void 0 : _data_resumeData_atsScore1.improvements) || []);\n            console.log('🎉 Resume generation completed successfully');\n        } catch (error) {\n            console.error(\"💥 Error generating resume:\", error);\n            console.error(\"Error details:\", {\n                message: error.message,\n                stack: error.stack,\n                name: error.name\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || \"Failed to generate resume\");\n            setIsGenerating(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsGenerating(false);\n        // Show success message with ATS score\n        const scoreMessage = atsScore ? \"Resume generated! ATS Score: \".concat(atsScore, \"%\") : \"Your resume has been generated successfully!\";\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(scoreMessage);\n    };\n    // Handle upload analysis completion\n    const handleUploadAnalysis = (analysisData)=>{\n        console.log('📊 Upload analysis received:', analysisData);\n        setUploadAnalysis(analysisData);\n        // For upload & enhance mode, show job description input\n        if (builderMode === 'upload') {\n            setShowJobDescriptionInput(true);\n        } else {\n            // For quick analysis, show results immediately\n            setShowAnalysis(true);\n        }\n        // If it's a full analysis, populate form data (even with minimal data)\n        if (analysisData.analysisType === 'full' && analysisData.extractedData) {\n            const extracted = analysisData.extractedData;\n            console.log('📋 Extracted data for form population:', extracted);\n            // Update form data with extracted information\n            setFormData((prevData)=>{\n                var _extracted_personal, _extracted_personal1, _extracted_personal2, _extracted_personal3, _extracted_personal4, _extracted_personal5, _extracted_personal6, _extracted_personal7, _extracted_education, _extracted_experience, _extracted_skills_technical, _extracted_skills, _extracted_skills_languages, _extracted_skills1, _extracted_skills_certifications, _extracted_skills2, _extracted_projects;\n                return {\n                    ...prevData,\n                    personal: {\n                        ...prevData.personal,\n                        firstName: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.firstName) || prevData.personal.firstName,\n                        lastName: ((_extracted_personal1 = extracted.personal) === null || _extracted_personal1 === void 0 ? void 0 : _extracted_personal1.lastName) || prevData.personal.lastName,\n                        email: ((_extracted_personal2 = extracted.personal) === null || _extracted_personal2 === void 0 ? void 0 : _extracted_personal2.email) || prevData.personal.email,\n                        phone: ((_extracted_personal3 = extracted.personal) === null || _extracted_personal3 === void 0 ? void 0 : _extracted_personal3.phone) || prevData.personal.phone,\n                        location: ((_extracted_personal4 = extracted.personal) === null || _extracted_personal4 === void 0 ? void 0 : _extracted_personal4.location) || prevData.personal.location,\n                        linkedin: ((_extracted_personal5 = extracted.personal) === null || _extracted_personal5 === void 0 ? void 0 : _extracted_personal5.linkedin) || prevData.personal.linkedin,\n                        portfolio: ((_extracted_personal6 = extracted.personal) === null || _extracted_personal6 === void 0 ? void 0 : _extracted_personal6.portfolio) || prevData.personal.portfolio,\n                        summary: ((_extracted_personal7 = extracted.personal) === null || _extracted_personal7 === void 0 ? void 0 : _extracted_personal7.summary) || prevData.personal.summary\n                    },\n                    education: ((_extracted_education = extracted.education) === null || _extracted_education === void 0 ? void 0 : _extracted_education.length) > 0 ? extracted.education.map((edu)=>({\n                            ...edu,\n                            id: edu.id || Date.now() + Math.random()\n                        })) : prevData.education,\n                    experience: ((_extracted_experience = extracted.experience) === null || _extracted_experience === void 0 ? void 0 : _extracted_experience.length) > 0 ? extracted.experience.map((exp)=>({\n                            ...exp,\n                            id: exp.id || Date.now() + Math.random()\n                        })) : prevData.experience,\n                    skills: {\n                        technical: ((_extracted_skills = extracted.skills) === null || _extracted_skills === void 0 ? void 0 : (_extracted_skills_technical = _extracted_skills.technical) === null || _extracted_skills_technical === void 0 ? void 0 : _extracted_skills_technical.length) > 0 ? extracted.skills.technical : prevData.skills.technical,\n                        languages: ((_extracted_skills1 = extracted.skills) === null || _extracted_skills1 === void 0 ? void 0 : (_extracted_skills_languages = _extracted_skills1.languages) === null || _extracted_skills_languages === void 0 ? void 0 : _extracted_skills_languages.length) > 0 ? extracted.skills.languages : prevData.skills.languages,\n                        certifications: ((_extracted_skills2 = extracted.skills) === null || _extracted_skills2 === void 0 ? void 0 : (_extracted_skills_certifications = _extracted_skills2.certifications) === null || _extracted_skills_certifications === void 0 ? void 0 : _extracted_skills_certifications.length) > 0 ? extracted.skills.certifications : prevData.skills.certifications\n                    },\n                    projects: ((_extracted_projects = extracted.projects) === null || _extracted_projects === void 0 ? void 0 : _extracted_projects.length) > 0 ? extracted.projects.map((proj)=>({\n                            ...proj,\n                            id: proj.id || Date.now() + Math.random()\n                        })) : prevData.projects\n                };\n            });\n            // Set ATS score and suggestions\n            if (analysisData.atsScore) {\n                var _analysisData_enhancements, _analysisData_analysis;\n                setAtsScore(analysisData.atsScore.overall);\n                setSuggestions(((_analysisData_enhancements = analysisData.enhancements) === null || _analysisData_enhancements === void 0 ? void 0 : _analysisData_enhancements.suggestions) || ((_analysisData_analysis = analysisData.analysis) === null || _analysisData_analysis === void 0 ? void 0 : _analysisData_analysis.recommendations) || []);\n            }\n            console.log('✅ Form data updated with extracted information');\n        } else if (analysisData.fallback) {\n            console.log('⚠️ Using fallback data - minimal extraction');\n            // Even with fallback, try to extract any available information\n            if (analysisData.extractedData) {\n                const extracted = analysisData.extractedData;\n                setFormData((prevData)=>{\n                    var _extracted_personal;\n                    return {\n                        ...prevData,\n                        personal: {\n                            ...prevData.personal,\n                            summary: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.summary) || 'Please add your professional summary here.'\n                        }\n                    };\n                });\n            }\n        }\n    };\n    // Handle job description submission for targeted resume generation\n    const handleJobDescriptionSubmit = async (jobData)=>{\n        if (!(uploadAnalysis === null || uploadAnalysis === void 0 ? void 0 : uploadAnalysis.extractedData)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('No resume data available. Please upload a resume first.');\n            return;\n        }\n        try {\n            setIsGeneratingTargeted(true);\n            console.log('🎯 Generating targeted resume with job data:', jobData);\n            const response = await fetch('/api/generate-targeted-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    extractedResumeData: uploadAnalysis.extractedData,\n                    jobDescription: jobData.description,\n                    jobTitle: jobData.jobTitle,\n                    company: jobData.company\n                })\n            });\n            const data = await response.json();\n            console.log('📊 Targeted resume response:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate targeted resume');\n            }\n            // Update form data with enhanced resume\n            if (data.enhancedResume) {\n                var _data_enhancedResume_personal, _data_enhancedResume_personal1, _data_enhancedResume_personal2, _data_enhancedResume_personal3, _data_enhancedResume_personal4, _data_enhancedResume_personal5, _data_enhancedResume_personal6, _data_enhancedResume_personal7, _data_enhancedResume_education, _data_enhancedResume_experience, _data_enhancedResume_skills, _data_enhancedResume_skills1, _data_enhancedResume_skills2, _data_enhancedResume_projects, _data_atsScore;\n                // Properly structure the enhanced resume data to match form data structure\n                const enhancedFormData = {\n                    personal: {\n                        firstName: ((_data_enhancedResume_personal = data.enhancedResume.personal) === null || _data_enhancedResume_personal === void 0 ? void 0 : _data_enhancedResume_personal.firstName) || '',\n                        lastName: ((_data_enhancedResume_personal1 = data.enhancedResume.personal) === null || _data_enhancedResume_personal1 === void 0 ? void 0 : _data_enhancedResume_personal1.lastName) || '',\n                        email: ((_data_enhancedResume_personal2 = data.enhancedResume.personal) === null || _data_enhancedResume_personal2 === void 0 ? void 0 : _data_enhancedResume_personal2.email) || '',\n                        phone: ((_data_enhancedResume_personal3 = data.enhancedResume.personal) === null || _data_enhancedResume_personal3 === void 0 ? void 0 : _data_enhancedResume_personal3.phone) || '',\n                        location: ((_data_enhancedResume_personal4 = data.enhancedResume.personal) === null || _data_enhancedResume_personal4 === void 0 ? void 0 : _data_enhancedResume_personal4.location) || '',\n                        linkedin: ((_data_enhancedResume_personal5 = data.enhancedResume.personal) === null || _data_enhancedResume_personal5 === void 0 ? void 0 : _data_enhancedResume_personal5.linkedin) || '',\n                        portfolio: ((_data_enhancedResume_personal6 = data.enhancedResume.personal) === null || _data_enhancedResume_personal6 === void 0 ? void 0 : _data_enhancedResume_personal6.portfolio) || '',\n                        summary: ((_data_enhancedResume_personal7 = data.enhancedResume.personal) === null || _data_enhancedResume_personal7 === void 0 ? void 0 : _data_enhancedResume_personal7.summary) || ''\n                    },\n                    education: ((_data_enhancedResume_education = data.enhancedResume.education) === null || _data_enhancedResume_education === void 0 ? void 0 : _data_enhancedResume_education.length) > 0 ? data.enhancedResume.education.map((edu)=>({\n                            id: edu.id || Date.now() + Math.random(),\n                            degree: edu.degree || '',\n                            institution: edu.institution || '',\n                            location: edu.location || '',\n                            startDate: edu.startDate || '',\n                            endDate: edu.endDate || '',\n                            gpa: edu.gpa || '',\n                            relevant: edu.relevant || ''\n                        })) : [\n                        {\n                            id: 1,\n                            degree: \"\",\n                            institution: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            gpa: \"\",\n                            relevant: \"\"\n                        }\n                    ],\n                    experience: ((_data_enhancedResume_experience = data.enhancedResume.experience) === null || _data_enhancedResume_experience === void 0 ? void 0 : _data_enhancedResume_experience.length) > 0 ? data.enhancedResume.experience.map((exp)=>({\n                            id: exp.id || Date.now() + Math.random(),\n                            title: exp.title || '',\n                            company: exp.company || '',\n                            location: exp.location || '',\n                            startDate: exp.startDate || '',\n                            endDate: exp.endDate || '',\n                            current: exp.current || false,\n                            description: exp.description || (exp.achievements ? exp.achievements.join('\\n') : '')\n                        })) : [\n                        {\n                            id: 1,\n                            title: \"\",\n                            company: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            current: false,\n                            description: \"\"\n                        }\n                    ],\n                    skills: {\n                        technical: ((_data_enhancedResume_skills = data.enhancedResume.skills) === null || _data_enhancedResume_skills === void 0 ? void 0 : _data_enhancedResume_skills.technical) || [],\n                        languages: ((_data_enhancedResume_skills1 = data.enhancedResume.skills) === null || _data_enhancedResume_skills1 === void 0 ? void 0 : _data_enhancedResume_skills1.languages) || [],\n                        certifications: ((_data_enhancedResume_skills2 = data.enhancedResume.skills) === null || _data_enhancedResume_skills2 === void 0 ? void 0 : _data_enhancedResume_skills2.certifications) || []\n                    },\n                    projects: ((_data_enhancedResume_projects = data.enhancedResume.projects) === null || _data_enhancedResume_projects === void 0 ? void 0 : _data_enhancedResume_projects.length) > 0 ? data.enhancedResume.projects.map((proj)=>({\n                            id: proj.id || Date.now() + Math.random(),\n                            name: proj.name || '',\n                            description: proj.description || '',\n                            technologies: proj.technologies || '',\n                            link: proj.link || ''\n                        })) : [\n                        {\n                            id: 1,\n                            name: \"\",\n                            description: \"\",\n                            technologies: \"\",\n                            link: \"\"\n                        }\n                    ]\n                };\n                setFormData(enhancedFormData);\n                setTargetedResumeData(data);\n                setAtsScore(((_data_atsScore = data.atsScore) === null || _data_atsScore === void 0 ? void 0 : _data_atsScore.overall) || 85);\n                setSuggestions(data.recommendations || []);\n                // Show success and move to form editing\n                setShowJobDescriptionInput(false);\n                setCurrentStep(0);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume optimized for the target job!');\n            }\n        } catch (error) {\n            console.error('Targeted resume generation error:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate targeted resume');\n        } finally{\n            setIsGeneratingTargeted(false);\n        }\n    };\n    // Handle mode change\n    const handleModeChange = (mode)=>{\n        setBuilderMode(mode);\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setShowJobDescriptionInput(false);\n        setIsGeneratingTargeted(false);\n        setTargetedResumeData(null);\n    };\n    // Reset to create mode\n    const resetToCreateMode = ()=>{\n        setBuilderMode('create');\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setFormData({\n            personal: {\n                firstName: \"\",\n                lastName: \"\",\n                email: \"\",\n                phone: \"\",\n                location: \"\",\n                linkedin: \"\",\n                portfolio: \"\",\n                summary: \"\"\n            },\n            education: [\n                {\n                    id: 1,\n                    degree: \"\",\n                    institution: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    gpa: \"\",\n                    relevant: \"\"\n                }\n            ],\n            experience: [\n                {\n                    id: 1,\n                    title: \"\",\n                    company: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    current: false,\n                    description: \"\"\n                }\n            ],\n            skills: {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: [\n                {\n                    id: 1,\n                    name: \"\",\n                    description: \"\",\n                    technologies: \"\",\n                    link: \"\"\n                }\n            ]\n        });\n    };\n    // Floating background elements\n    const FloatingElements = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: [\n                ...Array(8)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                    className: \"absolute rounded-full opacity-10 blur-xl\",\n                    style: {\n                        backgroundColor: '#832ED3',\n                        width: Math.random() * 200 + 100,\n                        height: Math.random() * 200 + 100,\n                        left: Math.random() * 100 + '%',\n                        top: Math.random() * 100 + '%'\n                    },\n                    animate: {\n                        x: [\n                            0,\n                            Math.random() * 100 - 50,\n                            0\n                        ],\n                        y: [\n                            0,\n                            Math.random() * 100 - 50,\n                            0\n                        ],\n                        transition: {\n                            duration: Math.random() * 20 + 20,\n                            repeat: Infinity,\n                            repeatType: 'reverse'\n                        }\n                    }\n                }, i, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 481,\n            columnNumber: 5\n        }, undefined);\n    // Show upload analysis results for quick ATS check\n    if (builderMode === 'analyze' && showAnalysis && uploadAnalysis) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 512,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 515,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                        children: \"ATS Analysis Results\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Here's your comprehensive resume analysis and recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"quick\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                        onClick: ()=>handleModeChange('upload'),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Enhance This Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                        onClick: resetToCreateMode,\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Start Fresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 537,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 518,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 517,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 510,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show success screen if resume is generated\n    if (resumeGenerated && resumeData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 570,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 573,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                        initial: {\n                                            scale: 0\n                                        },\n                                        animate: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-10 w-10 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                        children: \"Resume Generated Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300\",\n                                        children: \"Your AI-optimized resume is ready for download\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6 text-center\",\n                                                children: \"ATS Compatibility Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    score: atsScore || 75,\n                                                    size: 150\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6\",\n                                                children: \"AI Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-left\",\n                                                children: suggestions.length > 0 ? suggestions.slice(0, 4).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                                        className: \"flex items-start gap-3 p-3 bg-neural-purple/10 rounded-lg border border-neural-purple/20\",\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 10\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            delay: 0.8 + index * 0.1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-neural-purple rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: suggestion\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 23\n                                                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-center\",\n                                                    children: \"No specific recommendations at this time.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 602,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mb-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        formData: formData,\n                                        resumeData: resumeData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__.ViewResumeButton, {\n                                        formData: formData,\n                                        resumeData: resumeData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                        onClick: ()=>setShowPreview(!showPreview),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 text-neural-blue hover:text-white font-medium rounded-xl transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? 'Hide Preview' : 'View Preview'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                        onClick: ()=>{\n                                            setResumeGenerated(false);\n                                            setResumeData(null);\n                                            setAtsScore(null);\n                                            setSuggestions([]);\n                                            setCurrentStep(0);\n                                        },\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create Another Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 646,\n                                columnNumber: 13\n                            }, undefined),\n                            showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        formData: formData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 691,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 576,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 575,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 567,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 712,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 711,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 715,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 717,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                        className: \"text-center mb-8 md:mb-12 relative z-20\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-4 md:mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-8 w-8 md:h-10 md:w-10 text-neural-pink animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl lg:text-5xl font-bold text-white\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 730,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-4\",\n                                children: \"Create professional, ATS-friendly resumes in minutes with our AI-powered builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 739,\n                                columnNumber: 11\n                            }, undefined),\n                            builderMode === 'create' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            \"Step \",\n                                            currentStep + 1,\n                                            \" of \",\n                                            steps.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 745,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 724,\n                        columnNumber: 9\n                    }, undefined),\n                    (builderMode !== 'create' || currentStep === 0) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        currentMode: builderMode,\n                        onModeChange: handleModeChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 755,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'upload' || builderMode === 'analyze') && !showAnalysis && !showJobDescriptionInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            onAnalysisComplete: handleUploadAnalysis,\n                            analysisType: builderMode === 'analyze' ? 'quick' : 'full'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 768,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 763,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showJobDescriptionInput && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Uploaded Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Now provide the job description to create a targeted, ATS-optimized resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 782,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                onJobDescriptionSubmit: handleJobDescriptionSubmit,\n                                isLoading: isGeneratingTargeted\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 787,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                    onClick: ()=>{\n                                        setShowJobDescriptionInput(false);\n                                        setCurrentStep(0);\n                                    },\n                                    className: \"inline-flex items-center gap-2 px-6 py-3 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: \"Skip Job Targeting\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 792,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 777,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showAnalysis && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Analysis Complete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Review the extracted information and continue to enhance your resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 815,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 820,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                    onClick: ()=>setCurrentStep(0),\n                                    className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Continue to Form Editor\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 825,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 810,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'create' || builderMode === 'upload' && currentStep >= 0 && !showJobDescriptionInput) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            builderMode === 'create' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 md:mb-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center space-x-2 md:space-x-4 overflow-x-auto pb-4 px-4\",\n                                    children: steps.map((step, index)=>{\n                                        const Icon = step.icon;\n                                        const isActive = index === currentStep;\n                                        const isCompleted = index < currentStep;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                            className: \"flex items-center space-x-2 px-3 md:px-4 py-2 md:py-3 rounded-full border transition-all duration-300 min-w-fit \".concat(isActive ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25' : isCompleted ? 'bg-neural-purple/20 border-neural-purple/50' : 'bg-gray-800/50 border-gray-700'),\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4 md:h-5 md:w-5 \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 864,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs md:text-sm font-medium hidden sm:block \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400'),\n                                                    children: step.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium sm:hidden \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400'),\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, step.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 845,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 844,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/10 shadow-2xl\",\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 md:mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(steps[currentStep].icon, {\n                                                                    className: \"h-6 w-6 md:h-7 md:w-7 text-neural-purple\"\n                                                                }),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl md:text-2xl font-bold text-white\",\n                                                                    children: steps[currentStep].title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm md:text-base\",\n                                                            children: steps[currentStep].description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_26__.AnimatePresence, {\n                                                    mode: \"wait\",\n                                                    children: [\n                                                        currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonalInfoForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 907,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EducationForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 908,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_12__.ExperienceForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_12__.SkillsProjectsForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_12__.ReviewForm, {\n                                                            formData: formData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 39\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 906,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 884,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-4 md:p-6 border border-white/10 sticky top-4 md:top-8 shadow-2xl\",\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.4\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4 md:mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-base md:text-lg font-semibold flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 md:h-5 md:w-5 text-neural-blue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                \"Live Preview\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                                            onClick: ()=>setShowPreview(!showPreview),\n                                                            className: \"px-3 py-1.5 md:px-4 md:py-2 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 rounded-lg text-neural-blue hover:text-white transition-all duration-300 text-sm font-medium\",\n                                                            whileHover: {\n                                                                scale: 1.05\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.95\n                                                            },\n                                                            children: showPreview ? 'Hide' : 'Show'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 929,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[600px] md:max-h-[700px] overflow-y-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        formData: formData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 940,\n                                                    columnNumber: 17\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-12 md:py-16 text-gray-400\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                                        initial: {\n                                                            scale: 0.8,\n                                                            opacity: 0\n                                                        },\n                                                        animate: {\n                                                            scale: 1,\n                                                            opacity: 1\n                                                        },\n                                                        transition: {\n                                                            duration: 0.5\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-16 w-16 md:h-20 md:w-20 mx-auto mb-4 opacity-30\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm md:text-base mb-2\",\n                                                                children: \"Preview your resume\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs md:text-sm text-gray-500\",\n                                                                children: 'Click \"Show\" to see live updates'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 944,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 917,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 882,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 md:mt-12 max-w-7xl mx-auto px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                        onClick: prevStep,\n                                        disabled: currentStep === 0,\n                                        className: \"flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl transition-all duration-300 font-medium text-sm md:text-base w-full sm:w-auto \".concat(currentStep === 0 ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed border border-gray-700' : 'bg-gray-800/80 hover:bg-gray-700 text-white border border-gray-600 hover:border-gray-500 shadow-lg hover:shadow-xl'),\n                                        whileHover: currentStep !== 0 ? {\n                                            scale: 1.02\n                                        } : {},\n                                        whileTap: currentStep !== 0 ? {\n                                            scale: 0.98\n                                        } : {},\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-4 w-4 md:h-5 md:w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 973,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            \"Previous\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 sm:hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: [\n                                                \"Step \",\n                                                currentStep + 1,\n                                                \" of \",\n                                                steps.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 979,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    currentStep === steps.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                        onClick: generateResume,\n                                        disabled: isGenerating,\n                                        className: \"flex items-center gap-2 px-8 md:px-10 py-3 md:py-4 rounded-xl bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold hover:opacity-90 transition-all duration-300 disabled:opacity-50 shadow-lg hover:shadow-xl text-sm md:text-base w-full sm:w-auto\",\n                                        whileHover: !isGenerating ? {\n                                            scale: 1.02\n                                        } : {},\n                                        whileTap: !isGenerating ? {\n                                            scale: 0.98\n                                        } : {},\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 md:h-5 md:w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            isGenerating ? 'Generating...' : 'Generate Resume'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 985,\n                                        columnNumber: 13\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                        onClick: nextStep,\n                                        className: \"flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl text-sm md:text-base w-full sm:w-auto\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            \"Next\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4 md:h-5 md:w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 996,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 961,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 722,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 709,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"kqqgdHg6V16QdvB4Z2lfDHfdoqw=\");\n_c = ResumeBuilder;\n// Personal Information Form Component\nconst PersonalInfoForm = (param)=>{\n    let { formData, updateFormData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6 md:space-y-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"First Name *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1024,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.firstName,\n                            onChange: (e)=>updateFormData('personal', 'firstName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Rahul\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1027,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1023,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Last Name *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1037,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.lastName,\n                            onChange: (e)=>updateFormData('personal', 'lastName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Sharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1040,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1036,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1051,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Email *\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1050,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            value: formData.personal.email,\n                            onChange: (e)=>updateFormData('personal', 'email', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"<EMAIL>\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1054,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1049,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1065,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Phone\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1064,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"tel\",\n                            value: formData.personal.phone,\n                            onChange: (e)=>updateFormData('personal', 'phone', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"+91 98765 43210\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1068,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1063,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1079,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Location\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1078,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.location,\n                            onChange: (e)=>updateFormData('personal', 'location', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Mumbai, Maharashtra\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1082,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1077,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"LinkedIn\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1092,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.linkedin,\n                            onChange: (e)=>updateFormData('personal', 'linkedin', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://linkedin.com/in/rahulsharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1096,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1091,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Portfolio/Website\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1106,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.portfolio,\n                            onChange: (e)=>updateFormData('personal', 'portfolio', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://rahulsharma.dev\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1110,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1105,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Professional Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1120,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: formData.personal.summary,\n                            onChange: (e)=>updateFormData('personal', 'summary', e.target.value),\n                            rows: 4,\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 resize-none transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Experienced software engineer with 5+ years in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable solutions and leading cross-functional teams...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1123,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1119,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 1022,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1016,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PersonalInfoForm;\n// Education Form Component\nconst EducationForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-6 w-6 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1145,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1146,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1144,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addArrayItem('education', {\n                                degree: \"\",\n                                institution: \"\",\n                                location: \"\",\n                                startDate: \"\",\n                                endDate: \"\",\n                                gpa: \"\",\n                                relevant: \"\"\n                            }),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1160,\n                                columnNumber: 9\n                            }, undefined),\n                            \"Add Education\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1148,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1143,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: formData.education.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 p-6 rounded-lg border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Education \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1169,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeArrayItem('education', edu.id),\n                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 1175,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1171,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1168,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Degree *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1182,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.degree,\n                                                onChange: (e)=>updateFormData('education', 'degree', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"Bachelor of Science\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1181,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Institution *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1195,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.institution,\n                                                onChange: (e)=>updateFormData('education', 'institution', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"University of Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1198,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1209,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Location\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.location,\n                                                onChange: (e)=>updateFormData('education', 'location', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"New York, NY\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1212,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1207,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"GPA (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1222,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.gpa,\n                                                onChange: (e)=>updateFormData('education', 'gpa', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"3.8/4.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1225,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1221,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Start Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1235,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.startDate,\n                                                onChange: (e)=>updateFormData('education', 'startDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1239,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1234,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1249,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"End Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1248,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.endDate,\n                                                onChange: (e)=>updateFormData('education', 'endDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1252,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1247,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Relevant Coursework/Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1261,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: edu.relevant,\n                                                onChange: (e)=>updateFormData('education', 'relevant', e.target.value, index),\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"Relevant coursework, honors, achievements...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1264,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1260,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1180,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, edu.id, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1167,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1165,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1137,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EducationForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ResumeBuilder\");\n$RefreshReg$(_c1, \"PersonalInfoForm\");\n$RefreshReg$(_c2, \"EducationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ })

});