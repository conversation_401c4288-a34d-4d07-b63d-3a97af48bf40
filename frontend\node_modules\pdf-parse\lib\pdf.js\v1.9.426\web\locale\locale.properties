[ach]
@import url(ach/viewer.properties)

[af]
@import url(af/viewer.properties)

[ak]
@import url(ak/viewer.properties)

[an]
@import url(an/viewer.properties)

[ar]
@import url(ar/viewer.properties)

[as]
@import url(as/viewer.properties)

[ast]
@import url(ast/viewer.properties)

[az]
@import url(az/viewer.properties)

[be]
@import url(be/viewer.properties)

[bg]
@import url(bg/viewer.properties)

[bn-BD]
@import url(bn-BD/viewer.properties)

[bn-IN]
@import url(bn-IN/viewer.properties)

[br]
@import url(br/viewer.properties)

[bs]
@import url(bs/viewer.properties)

[ca]
@import url(ca/viewer.properties)

[cs]
@import url(cs/viewer.properties)

[csb]
@import url(csb/viewer.properties)

[cy]
@import url(cy/viewer.properties)

[da]
@import url(da/viewer.properties)

[de]
@import url(de/viewer.properties)

[el]
@import url(el/viewer.properties)

[en-GB]
@import url(en-GB/viewer.properties)

[en-US]
@import url(en-US/viewer.properties)

[en-ZA]
@import url(en-ZA/viewer.properties)

[eo]
@import url(eo/viewer.properties)

[es-AR]
@import url(es-AR/viewer.properties)

[es-CL]
@import url(es-CL/viewer.properties)

[es-ES]
@import url(es-ES/viewer.properties)

[es-MX]
@import url(es-MX/viewer.properties)

[et]
@import url(et/viewer.properties)

[eu]
@import url(eu/viewer.properties)

[fa]
@import url(fa/viewer.properties)

[ff]
@import url(ff/viewer.properties)

[fi]
@import url(fi/viewer.properties)

[fr]
@import url(fr/viewer.properties)

[fy-NL]
@import url(fy-NL/viewer.properties)

[ga-IE]
@import url(ga-IE/viewer.properties)

[gd]
@import url(gd/viewer.properties)

[gl]
@import url(gl/viewer.properties)

[gu-IN]
@import url(gu-IN/viewer.properties)

[he]
@import url(he/viewer.properties)

[hi-IN]
@import url(hi-IN/viewer.properties)

[hr]
@import url(hr/viewer.properties)

[hu]
@import url(hu/viewer.properties)

[hy-AM]
@import url(hy-AM/viewer.properties)

[id]
@import url(id/viewer.properties)

[is]
@import url(is/viewer.properties)

[it]
@import url(it/viewer.properties)

[ja]
@import url(ja/viewer.properties)

[ka]
@import url(ka/viewer.properties)

[kk]
@import url(kk/viewer.properties)

[km]
@import url(km/viewer.properties)

[kn]
@import url(kn/viewer.properties)

[ko]
@import url(ko/viewer.properties)

[ku]
@import url(ku/viewer.properties)

[lg]
@import url(lg/viewer.properties)

[lij]
@import url(lij/viewer.properties)

[lt]
@import url(lt/viewer.properties)

[lv]
@import url(lv/viewer.properties)

[mai]
@import url(mai/viewer.properties)

[mk]
@import url(mk/viewer.properties)

[ml]
@import url(ml/viewer.properties)

[mn]
@import url(mn/viewer.properties)

[mr]
@import url(mr/viewer.properties)

[ms]
@import url(ms/viewer.properties)

[my]
@import url(my/viewer.properties)

[nb-NO]
@import url(nb-NO/viewer.properties)

[nl]
@import url(nl/viewer.properties)

[nn-NO]
@import url(nn-NO/viewer.properties)

[nso]
@import url(nso/viewer.properties)

[oc]
@import url(oc/viewer.properties)

[or]
@import url(or/viewer.properties)

[pa-IN]
@import url(pa-IN/viewer.properties)

[pl]
@import url(pl/viewer.properties)

[pt-BR]
@import url(pt-BR/viewer.properties)

[pt-PT]
@import url(pt-PT/viewer.properties)

[rm]
@import url(rm/viewer.properties)

[ro]
@import url(ro/viewer.properties)

[ru]
@import url(ru/viewer.properties)

[rw]
@import url(rw/viewer.properties)

[sah]
@import url(sah/viewer.properties)

[si]
@import url(si/viewer.properties)

[sk]
@import url(sk/viewer.properties)

[sl]
@import url(sl/viewer.properties)

[son]
@import url(son/viewer.properties)

[sq]
@import url(sq/viewer.properties)

[sr]
@import url(sr/viewer.properties)

[sv-SE]
@import url(sv-SE/viewer.properties)

[sw]
@import url(sw/viewer.properties)

[ta]
@import url(ta/viewer.properties)

[ta-LK]
@import url(ta-LK/viewer.properties)

[te]
@import url(te/viewer.properties)

[th]
@import url(th/viewer.properties)

[tl]
@import url(tl/viewer.properties)

[tn]
@import url(tn/viewer.properties)

[tr]
@import url(tr/viewer.properties)

[uk]
@import url(uk/viewer.properties)

[ur]
@import url(ur/viewer.properties)

[vi]
@import url(vi/viewer.properties)

[wo]
@import url(wo/viewer.properties)

[xh]
@import url(xh/viewer.properties)

[zh-CN]
@import url(zh-CN/viewer.properties)

[zh-TW]
@import url(zh-TW/viewer.properties)

[zu]
@import url(zu/viewer.properties)

