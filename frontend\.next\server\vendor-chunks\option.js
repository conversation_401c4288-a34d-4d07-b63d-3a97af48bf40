/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/option";
exports.ids = ["vendor-chunks/option"];
exports.modules = {

/***/ "(rsc)/./node_modules/option/index.js":
/*!**************************************!*\
  !*** ./node_modules/option/index.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.none = Object.create({\n    value: function() {\n        throw new Error('Called value on none');\n    },\n    isNone: function() {\n        return true;\n    },\n    isSome: function() {\n        return false;\n    },\n    map: function() {\n        return exports.none;\n    },\n    flatMap: function() {\n        return exports.none;\n    },\n    filter: function() {\n        return exports.none;\n    },\n    toArray: function() {\n        return [];\n    },\n    orElse: callOrReturn,\n    valueOrElse: callOrReturn\n});\n\nfunction callOrReturn(value) {\n    if (typeof(value) == \"function\") {\n        return value();\n    } else {\n        return value;\n    }\n}\n\nexports.some = function(value) {\n    return new Some(value);\n};\n\nvar Some = function(value) {\n    this._value = value;\n};\n\nSome.prototype.value = function() {\n    return this._value;\n};\n\nSome.prototype.isNone = function() {\n    return false;\n};\n\nSome.prototype.isSome = function() {\n    return true;\n};\n\nSome.prototype.map = function(func) {\n    return new Some(func(this._value));\n};\n\nSome.prototype.flatMap = function(func) {\n    return func(this._value);\n};\n\nSome.prototype.filter = function(predicate) {\n    return predicate(this._value) ? this : exports.none;\n};\n\nSome.prototype.toArray = function() {\n    return [this._value];\n};\n\nSome.prototype.orElse = function(value) {\n    return this;\n};\n\nSome.prototype.valueOrElse = function(value) {\n    return this._value;\n};\n\nexports.isOption = function(value) {\n    return value === exports.none || value instanceof Some;\n};\n\nexports.fromNullable = function(value) {\n    if (value == null) {\n        return exports.none;\n    }\n    return new Some(value);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/option/index.js\n");

/***/ })

};
;