// Professional Resume Templates with Modern, ATS-Friendly Designs

export const RESUME_TEMPLATES = {
  modern: {
    id: 'modern',
    name: 'Modern Professional',
    description: 'Clean, modern design with neural theme colors',
    preview: '/templates/modern-preview.png',
    category: 'Professional'
  },
  classic: {
    id: 'classic',
    name: 'Classic Executive',
    description: 'Traditional professional layout with elegant typography',
    preview: '/templates/classic-preview.png',
    category: 'Traditional'
  },
  creative: {
    id: 'creative',
    name: 'Creative Tech',
    description: 'Contemporary design for tech and creative professionals',
    preview: '/templates/creative-preview.png',
    category: 'Creative'
  },
  minimal: {
    id: 'minimal',
    name: 'Minimal Clean',
    description: 'Minimalist design focusing on content clarity',
    preview: '/templates/minimal-preview.png',
    category: 'Minimal'
  }
};

export const generateResumeHTML = (formData, resumeData, templateId = 'modern') => {
  const template = RESUME_TEMPLATES[templateId] || RESUME_TEMPLATES.modern;
  
  switch (template.id) {
    case 'modern':
      return generateModernTemplate(formData, resumeData);
    case 'classic':
      return generateClassicTemplate(formData, resumeData);
    case 'creative':
      return generateCreativeTemplate(formData, resumeData);
    case 'minimal':
      return generateMinimalTemplate(formData, resumeData);
    default:
      return generateModernTemplate(formData, resumeData);
  }
};

// Modern Professional Template (Neural Theme)
export const generateModernTemplate = (formData, resumeData) => {
  const enhanced = resumeData?.enhancedContent || {};
  const personal = formData?.personal || {};
  const experience = formData?.experience || [];
  const education = formData?.education || [];
  const skills = formData?.skills || { technical: [], languages: [], certifications: [] };
  const projects = formData?.projects || [];

  const summary = enhanced.professionalSummary || personal.summary || '';
  const enhancedExperience = enhanced.experience || experience;
  const enhancedEducation = enhanced.education || education;
  const enhancedSkills = enhanced.skills || skills;
  const enhancedProjects = enhanced.projects || projects;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${personal.firstName} ${personal.lastName} - Resume</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
          line-height: 1.6;
          color: #1e293b;
          background: white;
          font-size: 11px;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }

        .container {
          max-width: 8.5in;
          margin: 0 auto;
          padding: 0.6in;
          min-height: 11in;
        }

        .header {
          background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
          color: white;
          padding: 30px;
          margin: -0.6in -0.6in 30px -0.6in;
          border-radius: 0 0 20px 20px;
        }

        .name {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 8px;
          letter-spacing: -0.5px;
        }

        .contact {
          font-size: 12px;
          opacity: 0.95;
          margin-bottom: 8px;
          font-weight: 400;
        }

        .contact-item {
          display: inline-block;
          margin-right: 20px;
          margin-bottom: 4px;
        }

        .links {
          font-size: 11px;
          opacity: 0.9;
          font-weight: 400;
        }

        .links a {
          color: white;
          text-decoration: none;
          margin-right: 15px;
        }

        .section {
          margin-bottom: 28px;
          page-break-inside: avoid;
        }

        .section-title {
          font-size: 14px;
          font-weight: 600;
          text-transform: uppercase;
          color: #6366f1;
          border-bottom: 2px solid #e2e8f0;
          padding-bottom: 6px;
          margin-bottom: 16px;
          letter-spacing: 0.5px;
        }

        .job, .education, .project {
          margin-bottom: 18px;
          padding-bottom: 16px;
          border-bottom: 1px solid #f1f5f9;
        }

        .job:last-child, .education:last-child, .project:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        .job-header, .edu-header, .project-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;
        }

        .job-title, .degree, .project-name {
          font-weight: 600;
          font-size: 13px;
          color: #1e293b;
          margin-bottom: 2px;
        }

        .company, .institution {
          color: #6366f1;
          font-size: 12px;
          font-weight: 500;
          margin-bottom: 2px;
        }

        .location {
          color: #64748b;
          font-size: 10px;
          font-weight: 400;
        }

        .date {
          font-size: 10px;
          color: #64748b;
          font-weight: 500;
          text-align: right;
          white-space: nowrap;
        }

        .description {
          margin-top: 8px;
          font-size: 11px;
          line-height: 1.5;
          color: #334155;
        }

        .description p {
          margin-bottom: 4px;
        }

        .description p:before {
          content: "•";
          color: #6366f1;
          font-weight: bold;
          margin-right: 8px;
        }

        .skill-category {
          margin-bottom: 12px;
        }

        .skill-title {
          font-weight: 600;
          margin-bottom: 6px;
          color: #1e293b;
          font-size: 11px;
        }

        .skill-list {
          color: #475569;
          font-size: 11px;
          line-height: 1.4;
        }

        .summary {
          font-size: 11px;
          line-height: 1.6;
          color: #334155;
          text-align: justify;
        }

        .skills-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
        }

        @media print {
          body { 
            font-size: 10px;
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
          }
          .container { 
            padding: 0.4in;
            margin: 0;
          }
          .header {
            margin: -0.4in -0.4in 20px -0.4in;
          }
          .section {
            page-break-inside: avoid;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="name">${personal.firstName || ''} ${personal.lastName || ''}</div>
          <div class="contact">
            ${[
              personal.email ? `<span class="contact-item">📧 ${personal.email}</span>` : '',
              personal.phone ? `<span class="contact-item">📱 ${personal.phone}</span>` : '',
              personal.location ? `<span class="contact-item">📍 ${personal.location}</span>` : ''
            ].filter(Boolean).join('')}
          </div>
          ${personal.linkedin || personal.portfolio ? `
            <div class="links">
              ${personal.linkedin ? `<a href="${personal.linkedin}">🔗 LinkedIn Profile</a>` : ''}
              ${personal.portfolio ? `<a href="${personal.portfolio}">🌐 Portfolio Website</a>` : ''}
            </div>
          ` : ''}
        </div>

        ${summary ? `
          <div class="section">
            <div class="section-title">Professional Summary</div>
            <div class="summary">${summary}</div>
          </div>
        ` : ''}

        ${enhancedExperience.length > 0 && enhancedExperience[0].title ? `
          <div class="section">
            <div class="section-title">Professional Experience</div>
            ${enhancedExperience.map(exp => exp.title ? `
              <div class="job">
                <div class="job-header">
                  <div>
                    <div class="job-title">${exp.title}</div>
                    <div class="company">${exp.company || ''}</div>
                    ${exp.location ? `<div class="location">${exp.location}</div>` : ''}
                  </div>
                  <div class="date">${exp.startDate || ''} - ${exp.current ? 'Present' : (exp.endDate || '')}</div>
                </div>
                ${exp.achievements ? `
                  <div class="description">
                    ${exp.achievements.map(achievement => `<p>${achievement.replace(/^•\s*/, '')}</p>`).join('')}
                  </div>
                ` : exp.description ? `
                  <div class="description">
                    ${exp.description.split('\n').filter(line => line.trim()).map(line => `<p>${line.replace(/^•\s*/, '')}</p>`).join('')}
                  </div>
                ` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        ${enhancedEducation.length > 0 && enhancedEducation[0].degree ? `
          <div class="section">
            <div class="section-title">Education</div>
            ${enhancedEducation.map(edu => edu.degree ? `
              <div class="education">
                <div class="edu-header">
                  <div>
                    <div class="degree">${edu.degree}</div>
                    <div class="institution">${edu.institution || ''}</div>
                    ${edu.location ? `<div class="location">${edu.location}</div>` : ''}
                  </div>
                  <div>
                    <div class="date">${edu.startDate || ''} - ${edu.endDate || ''}</div>
                    ${edu.gpa ? `<div style="font-size: 10px; color: #64748b; margin-top: 2px;">GPA: ${edu.gpa}</div>` : ''}
                  </div>
                </div>
                ${edu.relevant ? `<div style="margin-top: 6px; font-size: 10px; color: #475569;"><strong>Relevant Coursework:</strong> ${edu.relevant}</div>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        ${(enhancedSkills.technical?.length > 0 || enhancedSkills.languages?.length > 0 || enhancedSkills.certifications?.length > 0) ? `
          <div class="section">
            <div class="section-title">Skills & Certifications</div>
            <div class="skills-grid">
              <div>
                ${enhancedSkills.technical?.length > 0 ? `
                  <div class="skill-category">
                    <div class="skill-title">Technical Skills</div>
                    <div class="skill-list">${Array.isArray(enhancedSkills.technical) ? enhancedSkills.technical.join(' • ') : enhancedSkills.technical}</div>
                  </div>
                ` : ''}
                ${enhancedSkills.languages?.length > 0 ? `
                  <div class="skill-category">
                    <div class="skill-title">Languages</div>
                    <div class="skill-list">${Array.isArray(enhancedSkills.languages) ? enhancedSkills.languages.join(' • ') : enhancedSkills.languages}</div>
                  </div>
                ` : ''}
              </div>
              <div>
                ${enhancedSkills.certifications?.length > 0 ? `
                  <div class="skill-category">
                    <div class="skill-title">Certifications</div>
                    <div class="skill-list">${Array.isArray(enhancedSkills.certifications) ? enhancedSkills.certifications.join(' • ') : enhancedSkills.certifications}</div>
                  </div>
                ` : ''}
              </div>
            </div>
          </div>
        ` : ''}

        ${enhancedProjects.length > 0 && enhancedProjects[0].name ? `
          <div class="section">
            <div class="section-title">Projects</div>
            ${enhancedProjects.map(project => project.name ? `
              <div class="project">
                <div class="project-header">
                  <div class="project-name">${project.name}</div>
                  ${project.link ? `<div style="font-size: 10px; color: #6366f1;">${project.link}</div>` : ''}
                </div>
                ${project.description ? `<div class="description" style="margin-top: 6px;"><p style="margin: 0;">${project.description}</p></div>` : ''}
                ${project.technologies ? `<div style="margin-top: 6px; font-size: 10px; color: #475569;"><strong>Technologies:</strong> ${project.technologies}</div>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}
      </div>
    </body>
    </html>
  `;
};

// Classic Executive Template
export const generateClassicTemplate = (formData, resumeData) => {
  const enhanced = resumeData?.enhancedContent || {};
  const personal = formData?.personal || {};
  const experience = formData?.experience || [];
  const education = formData?.education || [];
  const skills = formData?.skills || { technical: [], languages: [], certifications: [] };
  const projects = formData?.projects || [];

  const summary = enhanced.professionalSummary || personal.summary || '';
  const enhancedExperience = enhanced.experience || experience;
  const enhancedEducation = enhanced.education || education;
  const enhancedSkills = enhanced.skills || skills;
  const enhancedProjects = enhanced.projects || projects;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${personal.firstName} ${personal.lastName} - Resume</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600;700&family=Source+Sans+Pro:wght@300;400;600&display=swap');

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Source Sans Pro', Arial, sans-serif;
          line-height: 1.6;
          color: #2c3e50;
          background: white;
          font-size: 11px;
        }

        .container {
          max-width: 8.5in;
          margin: 0 auto;
          padding: 0.75in;
        }

        .header {
          text-align: center;
          border-bottom: 3px solid #34495e;
          padding-bottom: 25px;
          margin-bottom: 35px;
        }

        .name {
          font-family: 'Crimson Text', serif;
          font-size: 32px;
          font-weight: 700;
          margin-bottom: 12px;
          color: #2c3e50;
          letter-spacing: 1px;
        }

        .contact {
          font-size: 12px;
          color: #555;
          margin-bottom: 10px;
          font-weight: 400;
        }

        .links {
          font-size: 11px;
          color: #3498db;
          font-weight: 400;
        }

        .section {
          margin-bottom: 30px;
        }

        .section-title {
          font-family: 'Crimson Text', serif;
          font-size: 16px;
          font-weight: 600;
          text-transform: uppercase;
          border-bottom: 2px solid #bdc3c7;
          padding-bottom: 6px;
          margin-bottom: 18px;
          letter-spacing: 1.5px;
          color: #2c3e50;
        }

        .job, .education, .project {
          margin-bottom: 22px;
          page-break-inside: avoid;
        }

        .job-header, .edu-header, .project-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 10px;
        }

        .job-title, .degree, .project-name {
          font-weight: 600;
          font-size: 13px;
          color: #2c3e50;
          margin-bottom: 3px;
        }

        .company, .institution {
          font-style: italic;
          color: #7f8c8d;
          font-size: 12px;
          font-weight: 400;
        }

        .location {
          color: #95a5a6;
          font-size: 10px;
        }

        .date {
          font-size: 11px;
          color: #7f8c8d;
          font-weight: 600;
          text-align: right;
        }

        .description {
          margin-top: 8px;
          font-size: 11px;
          line-height: 1.6;
          color: #34495e;
        }

        .description p {
          margin-bottom: 5px;
          text-indent: 15px;
        }

        .description p:before {
          content: "▪";
          margin-left: -15px;
          margin-right: 8px;
          color: #34495e;
        }

        .skill-category {
          margin-bottom: 14px;
        }

        .skill-title {
          font-weight: 600;
          margin-bottom: 6px;
          color: #2c3e50;
          font-size: 11px;
        }

        .skill-list {
          color: #555;
          font-size: 11px;
        }

        .summary {
          text-align: justify;
          font-size: 11px;
          line-height: 1.7;
          color: #34495e;
          font-style: italic;
        }

        @media print {
          body { font-size: 10px; }
          .container { padding: 0.5in; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="name">${personal.firstName || ''} ${personal.lastName || ''}</div>
          <div class="contact">
            ${[
              personal.email,
              personal.phone,
              personal.location
            ].filter(Boolean).join(' • ')}
          </div>
          ${personal.linkedin || personal.portfolio ? `
            <div class="links">
              ${[
                personal.linkedin ? `LinkedIn: ${personal.linkedin}` : '',
                personal.portfolio ? `Portfolio: ${personal.portfolio}` : ''
              ].filter(Boolean).join(' • ')}
            </div>
          ` : ''}
        </div>

        ${summary ? `
          <div class="section">
            <div class="section-title">Executive Summary</div>
            <div class="summary">${summary}</div>
          </div>
        ` : ''}

        ${enhancedExperience.length > 0 && enhancedExperience[0].title ? `
          <div class="section">
            <div class="section-title">Professional Experience</div>
            ${enhancedExperience.map(exp => exp.title ? `
              <div class="job">
                <div class="job-header">
                  <div>
                    <div class="job-title">${exp.title}</div>
                    <div class="company">${exp.company || ''}</div>
                    ${exp.location ? `<div class="location">${exp.location}</div>` : ''}
                  </div>
                  <div class="date">${exp.startDate || ''} - ${exp.current ? 'Present' : (exp.endDate || '')}</div>
                </div>
                ${exp.achievements ? `
                  <div class="description">
                    ${exp.achievements.map(achievement => `<p>${achievement.replace(/^•\s*/, '')}</p>`).join('')}
                  </div>
                ` : exp.description ? `
                  <div class="description">
                    ${exp.description.split('\n').filter(line => line.trim()).map(line => `<p>${line.replace(/^•\s*/, '')}</p>`).join('')}
                  </div>
                ` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        ${enhancedEducation.length > 0 && enhancedEducation[0].degree ? `
          <div class="section">
            <div class="section-title">Education</div>
            ${enhancedEducation.map(edu => edu.degree ? `
              <div class="education">
                <div class="edu-header">
                  <div>
                    <div class="degree">${edu.degree}</div>
                    <div class="institution">${edu.institution || ''}</div>
                    ${edu.location ? `<div class="location">${edu.location}</div>` : ''}
                    ${edu.relevant ? `<div style="margin-top: 5px; font-size: 10px; color: #7f8c8d;"><em>Relevant Coursework:</em> ${edu.relevant}</div>` : ''}
                  </div>
                  <div>
                    <div class="date">${edu.startDate || ''} - ${edu.endDate || ''}</div>
                    ${edu.gpa ? `<div style="font-size: 10px; color: #7f8c8d;">GPA: ${edu.gpa}</div>` : ''}
                  </div>
                </div>
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        ${(enhancedSkills.technical?.length > 0 || enhancedSkills.languages?.length > 0 || enhancedSkills.certifications?.length > 0) ? `
          <div class="section">
            <div class="section-title">Core Competencies</div>
            ${enhancedSkills.technical?.length > 0 ? `
              <div class="skill-category">
                <div class="skill-title">Technical Expertise:</div>
                <div class="skill-list">${Array.isArray(enhancedSkills.technical) ? enhancedSkills.technical.join(' • ') : enhancedSkills.technical}</div>
              </div>
            ` : ''}
            ${enhancedSkills.languages?.length > 0 ? `
              <div class="skill-category">
                <div class="skill-title">Languages:</div>
                <div class="skill-list">${Array.isArray(enhancedSkills.languages) ? enhancedSkills.languages.join(' • ') : enhancedSkills.languages}</div>
              </div>
            ` : ''}
            ${enhancedSkills.certifications?.length > 0 ? `
              <div class="skill-category">
                <div class="skill-title">Professional Certifications:</div>
                <div class="skill-list">${Array.isArray(enhancedSkills.certifications) ? enhancedSkills.certifications.join(' • ') : enhancedSkills.certifications}</div>
              </div>
            ` : ''}
          </div>
        ` : ''}

        ${enhancedProjects.length > 0 && enhancedProjects[0].name ? `
          <div class="section">
            <div class="section-title">Notable Projects</div>
            ${enhancedProjects.map(project => project.name ? `
              <div class="project">
                <div class="project-header">
                  <div class="project-name">${project.name}</div>
                  ${project.link ? `<div style="font-size: 10px; color: #3498db;">${project.link}</div>` : ''}
                </div>
                ${project.description ? `<div style="margin-top: 6px; font-size: 11px; color: #34495e;">${project.description}</div>` : ''}
                ${project.technologies ? `<div style="margin-top: 5px; font-size: 10px; color: #7f8c8d;"><strong>Technologies:</strong> ${project.technologies}</div>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}
      </div>
    </body>
    </html>
  `;
};

// Creative Tech Template
export const generateCreativeTemplate = (formData, resumeData) => {
  const enhanced = resumeData?.enhancedContent || {};
  const personal = formData?.personal || {};
  const experience = formData?.experience || [];
  const education = formData?.education || [];
  const skills = formData?.skills || { technical: [], languages: [], certifications: [] };
  const projects = formData?.projects || [];

  const summary = enhanced.professionalSummary || personal.summary || '';
  const enhancedExperience = enhanced.experience || experience;
  const enhancedEducation = enhanced.education || education;
  const enhancedSkills = enhanced.skills || skills;
  const enhancedProjects = enhanced.projects || projects;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${personal.firstName} ${personal.lastName} - Resume</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Poppins', sans-serif;
          line-height: 1.6;
          color: #2d3748;
          background: white;
          font-size: 11px;
        }

        .container {
          max-width: 8.5in;
          margin: 0 auto;
          padding: 0;
          display: grid;
          grid-template-columns: 280px 1fr;
          min-height: 11in;
        }

        .sidebar {
          background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 40px 30px;
        }

        .main-content {
          padding: 40px 35px;
        }

        .profile-section {
          text-align: center;
          margin-bottom: 35px;
        }

        .name {
          font-size: 22px;
          font-weight: 700;
          margin-bottom: 8px;
          letter-spacing: 0.5px;
        }

        .title {
          font-size: 12px;
          opacity: 0.9;
          font-weight: 400;
          margin-bottom: 20px;
        }

        .contact-info {
          text-align: left;
        }

        .contact-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          font-size: 10px;
        }

        .contact-icon {
          width: 16px;
          margin-right: 10px;
          opacity: 0.8;
        }

        .sidebar-section {
          margin-bottom: 30px;
        }

        .sidebar-title {
          font-size: 13px;
          font-weight: 600;
          margin-bottom: 15px;
          text-transform: uppercase;
          letter-spacing: 1px;
          border-bottom: 2px solid rgba(255,255,255,0.3);
          padding-bottom: 5px;
        }

        .skill-item {
          margin-bottom: 8px;
          font-size: 10px;
          opacity: 0.95;
        }

        .section {
          margin-bottom: 30px;
        }

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #667eea;
          margin-bottom: 20px;
          position: relative;
          padding-left: 20px;
        }

        .section-title:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 20px;
          background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
          border-radius: 2px;
        }

        .job, .education, .project {
          margin-bottom: 25px;
          position: relative;
          padding-left: 20px;
        }

        .job:before, .education:before, .project:before {
          content: '';
          position: absolute;
          left: 0;
          top: 8px;
          width: 8px;
          height: 8px;
          background: #667eea;
          border-radius: 50%;
        }

        .job-header, .edu-header, .project-header {
          margin-bottom: 8px;
        }

        .job-title, .degree, .project-name {
          font-weight: 600;
          font-size: 13px;
          color: #2d3748;
          margin-bottom: 3px;
        }

        .company, .institution {
          color: #667eea;
          font-size: 11px;
          font-weight: 500;
          margin-bottom: 2px;
        }

        .date {
          font-size: 10px;
          color: #718096;
          font-weight: 500;
        }

        .location {
          color: #a0aec0;
          font-size: 10px;
        }

        .description {
          margin-top: 8px;
          font-size: 10px;
          line-height: 1.6;
          color: #4a5568;
        }

        .description p {
          margin-bottom: 4px;
          position: relative;
          padding-left: 12px;
        }

        .description p:before {
          content: '→';
          position: absolute;
          left: 0;
          color: #667eea;
          font-weight: bold;
        }

        .summary {
          font-size: 11px;
          line-height: 1.7;
          color: #4a5568;
          text-align: justify;
          background: #f7fafc;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #667eea;
        }

        @media print {
          body { font-size: 10px; }
          .container {
            grid-template-columns: 260px 1fr;
          }
          .sidebar { padding: 30px 25px; }
          .main-content { padding: 30px 25px; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="sidebar">
          <div class="profile-section">
            <div class="name">${personal.firstName || ''} ${personal.lastName || ''}</div>
            <div class="title">Creative Professional</div>
          </div>

          <div class="contact-info">
            ${personal.email ? `
              <div class="contact-item">
                <span class="contact-icon">📧</span>
                <span>${personal.email}</span>
              </div>
            ` : ''}
            ${personal.phone ? `
              <div class="contact-item">
                <span class="contact-icon">📱</span>
                <span>${personal.phone}</span>
              </div>
            ` : ''}
            ${personal.location ? `
              <div class="contact-item">
                <span class="contact-icon">📍</span>
                <span>${personal.location}</span>
              </div>
            ` : ''}
            ${personal.linkedin ? `
              <div class="contact-item">
                <span class="contact-icon">🔗</span>
                <span>LinkedIn Profile</span>
              </div>
            ` : ''}
            ${personal.portfolio ? `
              <div class="contact-item">
                <span class="contact-icon">🌐</span>
                <span>Portfolio Website</span>
              </div>
            ` : ''}
          </div>

          ${enhancedSkills.technical?.length > 0 ? `
            <div class="sidebar-section">
              <div class="sidebar-title">Technical Skills</div>
              ${enhancedSkills.technical.map(skill => `<div class="skill-item">• ${skill}</div>`).join('')}
            </div>
          ` : ''}

          ${enhancedSkills.languages?.length > 0 ? `
            <div class="sidebar-section">
              <div class="sidebar-title">Languages</div>
              ${enhancedSkills.languages.map(lang => `<div class="skill-item">• ${lang}</div>`).join('')}
            </div>
          ` : ''}

          ${enhancedSkills.certifications?.length > 0 ? `
            <div class="sidebar-section">
              <div class="sidebar-title">Certifications</div>
              ${enhancedSkills.certifications.map(cert => `<div class="skill-item">• ${cert}</div>`).join('')}
            </div>
          ` : ''}
        </div>

        <div class="main-content">
          ${summary ? `
            <div class="section">
              <div class="section-title">About Me</div>
              <div class="summary">${summary}</div>
            </div>
          ` : ''}

          ${enhancedExperience.length > 0 && enhancedExperience[0].title ? `
            <div class="section">
              <div class="section-title">Experience</div>
              ${enhancedExperience.map(exp => exp.title ? `
                <div class="job">
                  <div class="job-header">
                    <div class="job-title">${exp.title}</div>
                    <div class="company">${exp.company || ''}</div>
                    <div class="date">${exp.startDate || ''} - ${exp.current ? 'Present' : (exp.endDate || '')}</div>
                    ${exp.location ? `<div class="location">${exp.location}</div>` : ''}
                  </div>
                  ${exp.achievements ? `
                    <div class="description">
                      ${exp.achievements.map(achievement => `<p>${achievement.replace(/^•\s*/, '')}</p>`).join('')}
                    </div>
                  ` : exp.description ? `
                    <div class="description">
                      ${exp.description.split('\n').filter(line => line.trim()).map(line => `<p>${line.replace(/^•\s*/, '')}</p>`).join('')}
                    </div>
                  ` : ''}
                </div>
              ` : '').join('')}
            </div>
          ` : ''}

          ${enhancedProjects.length > 0 && enhancedProjects[0].name ? `
            <div class="section">
              <div class="section-title">Featured Projects</div>
              ${enhancedProjects.map(project => project.name ? `
                <div class="project">
                  <div class="project-header">
                    <div class="project-name">${project.name}</div>
                    ${project.technologies ? `<div style="font-size: 10px; color: #718096; margin-top: 2px;">${project.technologies}</div>` : ''}
                  </div>
                  ${project.description ? `<div style="margin-top: 6px; font-size: 10px; color: #4a5568;">${project.description}</div>` : ''}
                  ${project.link ? `<div style="margin-top: 4px; font-size: 9px; color: #667eea;">${project.link}</div>` : ''}
                </div>
              ` : '').join('')}
            </div>
          ` : ''}

          ${enhancedEducation.length > 0 && enhancedEducation[0].degree ? `
            <div class="section">
              <div class="section-title">Education</div>
              ${enhancedEducation.map(edu => edu.degree ? `
                <div class="education">
                  <div class="edu-header">
                    <div class="degree">${edu.degree}</div>
                    <div class="institution">${edu.institution || ''}</div>
                    <div class="date">${edu.startDate || ''} - ${edu.endDate || ''}</div>
                    ${edu.location ? `<div class="location">${edu.location}</div>` : ''}
                    ${edu.gpa ? `<div style="font-size: 10px; color: #718096; margin-top: 2px;">GPA: ${edu.gpa}</div>` : ''}
                  </div>
                  ${edu.relevant ? `<div style="margin-top: 6px; font-size: 10px; color: #4a5568;"><strong>Relevant Coursework:</strong> ${edu.relevant}</div>` : ''}
                </div>
              ` : '').join('')}
            </div>
          ` : ''}
        </div>
      </div>
    </body>
    </html>
  `;
};

// Minimal Clean Template
export const generateMinimalTemplate = (formData, resumeData) => {
  const enhanced = resumeData?.enhancedContent || {};
  const personal = formData?.personal || {};
  const experience = formData?.experience || [];
  const education = formData?.education || [];
  const skills = formData?.skills || { technical: [], languages: [], certifications: [] };
  const projects = formData?.projects || [];

  const summary = enhanced.professionalSummary || personal.summary || '';
  const enhancedExperience = enhanced.experience || experience;
  const enhancedEducation = enhanced.education || education;
  const enhancedSkills = enhanced.skills || skills;
  const enhancedProjects = enhanced.projects || projects;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${personal.firstName} ${personal.lastName} - Resume</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600&display=swap');

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, sans-serif;
          line-height: 1.6;
          color: #1a202c;
          background: white;
          font-size: 11px;
          font-weight: 400;
        }

        .container {
          max-width: 8.5in;
          margin: 0 auto;
          padding: 0.8in;
        }

        .header {
          margin-bottom: 40px;
          padding-bottom: 20px;
          border-bottom: 1px solid #e2e8f0;
        }

        .name {
          font-size: 28px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #1a202c;
          letter-spacing: -0.5px;
        }

        .contact {
          font-size: 11px;
          color: #4a5568;
          margin-bottom: 8px;
          font-weight: 400;
        }

        .contact-item {
          display: inline-block;
          margin-right: 20px;
        }

        .links {
          font-size: 11px;
          color: #2d3748;
        }

        .links a {
          color: #2d3748;
          text-decoration: none;
          margin-right: 15px;
        }

        .section {
          margin-bottom: 32px;
        }

        .section-title {
          font-size: 13px;
          font-weight: 600;
          text-transform: uppercase;
          color: #1a202c;
          margin-bottom: 16px;
          letter-spacing: 1px;
        }

        .job, .education, .project {
          margin-bottom: 20px;
          padding-bottom: 16px;
          border-bottom: 1px solid #f7fafc;
        }

        .job:last-child, .education:last-child, .project:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        .job-header, .edu-header, .project-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 6px;
        }

        .job-title, .degree, .project-name {
          font-weight: 500;
          font-size: 12px;
          color: #1a202c;
          margin-bottom: 2px;
        }

        .company, .institution {
          color: #4a5568;
          font-size: 11px;
          font-weight: 400;
          margin-bottom: 2px;
        }

        .location {
          color: #718096;
          font-size: 10px;
        }

        .date {
          font-size: 10px;
          color: #718096;
          font-weight: 400;
          text-align: right;
        }

        .description {
          margin-top: 8px;
          font-size: 11px;
          line-height: 1.5;
          color: #2d3748;
        }

        .description p {
          margin-bottom: 3px;
        }

        .description p:before {
          content: "—";
          margin-right: 8px;
          color: #a0aec0;
        }

        .skill-category {
          margin-bottom: 10px;
        }

        .skill-title {
          font-weight: 500;
          margin-bottom: 4px;
          color: #1a202c;
          font-size: 11px;
        }

        .skill-list {
          color: #4a5568;
          font-size: 11px;
          line-height: 1.4;
        }

        .summary {
          font-size: 11px;
          line-height: 1.6;
          color: #2d3748;
          text-align: justify;
        }

        .skills-container {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 20px;
        }

        @media print {
          body { font-size: 10px; }
          .container { padding: 0.6in; }
          .skills-container { grid-template-columns: 1fr 1fr; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="name">${personal.firstName || ''} ${personal.lastName || ''}</div>
          <div class="contact">
            ${[
              personal.email ? `<span class="contact-item">${personal.email}</span>` : '',
              personal.phone ? `<span class="contact-item">${personal.phone}</span>` : '',
              personal.location ? `<span class="contact-item">${personal.location}</span>` : ''
            ].filter(Boolean).join('')}
          </div>
          ${personal.linkedin || personal.portfolio ? `
            <div class="links">
              ${personal.linkedin ? `<a href="${personal.linkedin}">LinkedIn</a>` : ''}
              ${personal.portfolio ? `<a href="${personal.portfolio}">Portfolio</a>` : ''}
            </div>
          ` : ''}
        </div>

        ${summary ? `
          <div class="section">
            <div class="section-title">Summary</div>
            <div class="summary">${summary}</div>
          </div>
        ` : ''}

        ${enhancedExperience.length > 0 && enhancedExperience[0].title ? `
          <div class="section">
            <div class="section-title">Experience</div>
            ${enhancedExperience.map(exp => exp.title ? `
              <div class="job">
                <div class="job-header">
                  <div>
                    <div class="job-title">${exp.title}</div>
                    <div class="company">${exp.company || ''}</div>
                    ${exp.location ? `<div class="location">${exp.location}</div>` : ''}
                  </div>
                  <div class="date">${exp.startDate || ''} - ${exp.current ? 'Present' : (exp.endDate || '')}</div>
                </div>
                ${exp.achievements ? `
                  <div class="description">
                    ${exp.achievements.map(achievement => `<p>${achievement.replace(/^•\s*/, '')}</p>`).join('')}
                  </div>
                ` : exp.description ? `
                  <div class="description">
                    ${exp.description.split('\n').filter(line => line.trim()).map(line => `<p>${line.replace(/^•\s*/, '')}</p>`).join('')}
                  </div>
                ` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        ${enhancedEducation.length > 0 && enhancedEducation[0].degree ? `
          <div class="section">
            <div class="section-title">Education</div>
            ${enhancedEducation.map(edu => edu.degree ? `
              <div class="education">
                <div class="edu-header">
                  <div>
                    <div class="degree">${edu.degree}</div>
                    <div class="institution">${edu.institution || ''}</div>
                    ${edu.location ? `<div class="location">${edu.location}</div>` : ''}
                  </div>
                  <div>
                    <div class="date">${edu.startDate || ''} - ${edu.endDate || ''}</div>
                    ${edu.gpa ? `<div style="font-size: 10px; color: #718096; margin-top: 2px;">GPA: ${edu.gpa}</div>` : ''}
                  </div>
                </div>
                ${edu.relevant ? `<div style="margin-top: 6px; font-size: 10px; color: #4a5568;">Relevant Coursework: ${edu.relevant}</div>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        ${(enhancedSkills.technical?.length > 0 || enhancedSkills.languages?.length > 0 || enhancedSkills.certifications?.length > 0) ? `
          <div class="section">
            <div class="section-title">Skills</div>
            <div class="skills-container">
              ${enhancedSkills.technical?.length > 0 ? `
                <div class="skill-category">
                  <div class="skill-title">Technical</div>
                  <div class="skill-list">${Array.isArray(enhancedSkills.technical) ? enhancedSkills.technical.join(', ') : enhancedSkills.technical}</div>
                </div>
              ` : ''}
              ${enhancedSkills.languages?.length > 0 ? `
                <div class="skill-category">
                  <div class="skill-title">Languages</div>
                  <div class="skill-list">${Array.isArray(enhancedSkills.languages) ? enhancedSkills.languages.join(', ') : enhancedSkills.languages}</div>
                </div>
              ` : ''}
              ${enhancedSkills.certifications?.length > 0 ? `
                <div class="skill-category">
                  <div class="skill-title">Certifications</div>
                  <div class="skill-list">${Array.isArray(enhancedSkills.certifications) ? enhancedSkills.certifications.join(', ') : enhancedSkills.certifications}</div>
                </div>
              ` : ''}
            </div>
          </div>
        ` : ''}

        ${enhancedProjects.length > 0 && enhancedProjects[0].name ? `
          <div class="section">
            <div class="section-title">Projects</div>
            ${enhancedProjects.map(project => project.name ? `
              <div class="project">
                <div class="project-header">
                  <div class="project-name">${project.name}</div>
                  ${project.link ? `<div style="font-size: 10px; color: #4a5568;">${project.link}</div>` : ''}
                </div>
                ${project.description ? `<div style="margin-top: 6px; font-size: 11px; color: #2d3748;">${project.description}</div>` : ''}
                ${project.technologies ? `<div style="margin-top: 4px; font-size: 10px; color: #718096;">Technologies: ${project.technologies}</div>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}
      </div>
    </body>
    </html>
  `;
};
