"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* harmony import */ var _components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ATSScoreCircle */ \"(app-pages-browser)/./src/components/ATSScoreCircle.jsx\");\n/* harmony import */ var _components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/EnhancedResumePreview */ \"(app-pages-browser)/./src/components/EnhancedResumePreview.jsx\");\n/* harmony import */ var _components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/PDFDownload */ \"(app-pages-browser)/./src/components/PDFDownload.jsx\");\n/* harmony import */ var _components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ResumeBuilderModeToggle */ \"(app-pages-browser)/./src/components/ResumeBuilderModeToggle.jsx\");\n/* harmony import */ var _components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ResumeUpload */ \"(app-pages-browser)/./src/components/ResumeUpload.jsx\");\n/* harmony import */ var _components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ATSAnalysisDisplay */ \"(app-pages-browser)/./src/components/ATSAnalysisDisplay.jsx\");\n/* harmony import */ var _components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/JobDescriptionInput */ \"(app-pages-browser)/./src/components/JobDescriptionInput.jsx\");\n/* harmony import */ var _components_BeforeAfterComparison__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/BeforeAfterComparison */ \"(app-pages-browser)/./src/components/BeforeAfterComparison.jsx\");\n/* harmony import */ var _components_AIContentSuggestions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AIContentSuggestions */ \"(app-pages-browser)/./src/components/AIContentSuggestions.jsx\");\n/* harmony import */ var _components_ATSOptimizationPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ATSOptimizationPanel */ \"(app-pages-browser)/./src/components/ATSOptimizationPanel.jsx\");\n/* harmony import */ var _components_TemplateSelector__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/TemplateSelector */ \"(app-pages-browser)/./src/components/TemplateSelector.jsx\");\n/* harmony import */ var _components_UploadEnhancementWorkflow__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/UploadEnhancementWorkflow */ \"(app-pages-browser)/./src/components/UploadEnhancementWorkflow.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* harmony import */ var _components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ATSFieldIndicator */ \"(app-pages-browser)/./src/components/ATSFieldIndicator.jsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./src/components/ClientOnly.jsx\");\n/* harmony import */ var _components_ATSTooltip__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ATSTooltip */ \"(app-pages-browser)/./src/components/ATSTooltip.jsx\");\n/* harmony import */ var _components_StepIndicator__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/StepIndicator */ \"(app-pages-browser)/./src/components/StepIndicator.jsx\");\n/* harmony import */ var _components_StickyNavigation__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/StickyNavigation */ \"(app-pages-browser)/./src/components/StickyNavigation.jsx\");\n/* harmony import */ var _components_FormHeader__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/FormHeader */ \"(app-pages-browser)/./src/components/FormHeader.jsx\");\n/* harmony import */ var _components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ResumeFormComponents */ \"(app-pages-browser)/./src/components/ResumeFormComponents.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import SuccessScreen from \"@/components/SuccessScreen\";\n\nconst ResumeBuilder = ()=>{\n    _s();\n    // Main state management\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for upload functionality\n    const [builderMode, setBuilderMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create'); // 'create', 'upload', 'analyze'\n    const [uploadAnalysis, setUploadAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAnalysis, setShowAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUploadWorkflow, setShowUploadWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced upload workflow state\n    const [showJobDescriptionInput, setShowJobDescriptionInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingTargeted, setIsGeneratingTargeted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetedResumeData, setTargetedResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Template selection state\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ]\n    });\n    // Real-time ATS analysis\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(formData);\n    // Steps configuration\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            description: \"Tell us about yourself\"\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            description: \"Your academic background\"\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            description: \"Your work experience\"\n        },\n        {\n            id: 3,\n            title: \"Skills & Projects\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            description: \"Showcase your abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            description: \"Finalize your resume\"\n        }\n    ];\n    // Update form data\n    const updateFormData = function(section, field, value) {\n        let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        setFormData((prev)=>{\n            if (index !== null && Array.isArray(prev[section])) {\n                const newArray = [\n                    ...prev[section]\n                ];\n                newArray[index] = {\n                    ...newArray[index],\n                    [field]: value\n                };\n                return {\n                    ...prev,\n                    [section]: newArray\n                };\n            } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                return {\n                    ...prev,\n                    [section]: {\n                        ...prev[section],\n                        [field]: value\n                    }\n                };\n            }\n            return prev;\n        });\n    };\n    // Add new item to array sections\n    const addArrayItem = (section, template)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: [\n                    ...prev[section],\n                    {\n                        ...template,\n                        id: Math.random().toString(36).substring(2, 11)\n                    }\n                ]\n            }));\n    };\n    // Remove item from array sections\n    const removeArrayItem = (section, id)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: prev[section].filter((item)=>item.id !== id)\n            }));\n    };\n    // Navigation functions\n    const nextStep = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    // Generate resume with AI\n    const generateResume = async ()=>{\n        try {\n            var _data_resumeData_atsScore, _data_resumeData, _data_resumeData_atsScore1, _data_resumeData1;\n            console.log('🚀 Starting resume generation...');\n            console.log('📝 Form data:', formData);\n            setIsGenerating(true);\n            setShowProgressBar(true);\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    templateId: selectedTemplate\n                })\n            });\n            console.log('📡 API response status:', response.status);\n            console.log('📡 API response headers:', Object.fromEntries(response.headers.entries()));\n            // Check if response is actually JSON\n            const contentType = response.headers.get('content-type');\n            if (!contentType || !contentType.includes('application/json')) {\n                const textResponse = await response.text();\n                console.error('❌ Non-JSON response received:', textResponse);\n                throw new Error('Server returned non-JSON response');\n            }\n            const data = await response.json();\n            console.log('📊 API response data:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            // Store the enhanced resume data and ATS information\n            console.log('✅ Setting resume data...');\n            setResumeUrl(data.downloadUrl);\n            setResumeData(data.resumeData);\n            setAtsScore(data.atsScore || ((_data_resumeData = data.resumeData) === null || _data_resumeData === void 0 ? void 0 : (_data_resumeData_atsScore = _data_resumeData.atsScore) === null || _data_resumeData_atsScore === void 0 ? void 0 : _data_resumeData_atsScore.overall) || 75);\n            setSuggestions(data.suggestions || ((_data_resumeData1 = data.resumeData) === null || _data_resumeData1 === void 0 ? void 0 : (_data_resumeData_atsScore1 = _data_resumeData1.atsScore) === null || _data_resumeData_atsScore1 === void 0 ? void 0 : _data_resumeData_atsScore1.improvements) || []);\n            console.log('🎉 Resume generation completed successfully');\n        } catch (error) {\n            console.error(\"💥 Error generating resume:\", error);\n            console.error(\"Error details:\", {\n                message: error.message,\n                stack: error.stack,\n                name: error.name\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || \"Failed to generate resume\");\n            setIsGenerating(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsGenerating(false);\n        // Show success message with ATS score\n        const scoreMessage = atsScore ? \"Resume generated! ATS Score: \".concat(atsScore, \"%\") : \"Your resume has been generated successfully!\";\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(scoreMessage);\n    };\n    // Handle upload analysis completion\n    const handleUploadAnalysis = (analysisData)=>{\n        console.log('📊 Upload analysis received:', analysisData);\n        setUploadAnalysis(analysisData);\n        // For upload & enhance mode, show job description input\n        if (builderMode === 'upload') {\n            setShowJobDescriptionInput(true);\n        } else {\n            // For quick analysis, show results immediately\n            setShowAnalysis(true);\n        }\n        // If it's a full analysis, populate form data (even with minimal data)\n        if (analysisData.analysisType === 'full' && analysisData.extractedData) {\n            const extracted = analysisData.extractedData;\n            console.log('📋 Extracted data for form population:', extracted);\n            // Update form data with extracted information\n            setFormData((prevData)=>{\n                var _extracted_personal, _extracted_personal1, _extracted_personal2, _extracted_personal3, _extracted_personal4, _extracted_personal5, _extracted_personal6, _extracted_personal7, _extracted_education, _extracted_experience, _extracted_skills_technical, _extracted_skills, _extracted_skills_languages, _extracted_skills1, _extracted_skills_certifications, _extracted_skills2, _extracted_projects;\n                return {\n                    ...prevData,\n                    personal: {\n                        ...prevData.personal,\n                        firstName: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.firstName) || prevData.personal.firstName,\n                        lastName: ((_extracted_personal1 = extracted.personal) === null || _extracted_personal1 === void 0 ? void 0 : _extracted_personal1.lastName) || prevData.personal.lastName,\n                        email: ((_extracted_personal2 = extracted.personal) === null || _extracted_personal2 === void 0 ? void 0 : _extracted_personal2.email) || prevData.personal.email,\n                        phone: ((_extracted_personal3 = extracted.personal) === null || _extracted_personal3 === void 0 ? void 0 : _extracted_personal3.phone) || prevData.personal.phone,\n                        location: ((_extracted_personal4 = extracted.personal) === null || _extracted_personal4 === void 0 ? void 0 : _extracted_personal4.location) || prevData.personal.location,\n                        linkedin: ((_extracted_personal5 = extracted.personal) === null || _extracted_personal5 === void 0 ? void 0 : _extracted_personal5.linkedin) || prevData.personal.linkedin,\n                        portfolio: ((_extracted_personal6 = extracted.personal) === null || _extracted_personal6 === void 0 ? void 0 : _extracted_personal6.portfolio) || prevData.personal.portfolio,\n                        summary: ((_extracted_personal7 = extracted.personal) === null || _extracted_personal7 === void 0 ? void 0 : _extracted_personal7.summary) || prevData.personal.summary\n                    },\n                    education: ((_extracted_education = extracted.education) === null || _extracted_education === void 0 ? void 0 : _extracted_education.length) > 0 ? extracted.education.map((edu)=>({\n                            ...edu,\n                            id: edu.id || Date.now() + Math.random()\n                        })) : prevData.education,\n                    experience: ((_extracted_experience = extracted.experience) === null || _extracted_experience === void 0 ? void 0 : _extracted_experience.length) > 0 ? extracted.experience.map((exp)=>({\n                            ...exp,\n                            id: exp.id || Date.now() + Math.random()\n                        })) : prevData.experience,\n                    skills: {\n                        technical: ((_extracted_skills = extracted.skills) === null || _extracted_skills === void 0 ? void 0 : (_extracted_skills_technical = _extracted_skills.technical) === null || _extracted_skills_technical === void 0 ? void 0 : _extracted_skills_technical.length) > 0 ? extracted.skills.technical : prevData.skills.technical,\n                        languages: ((_extracted_skills1 = extracted.skills) === null || _extracted_skills1 === void 0 ? void 0 : (_extracted_skills_languages = _extracted_skills1.languages) === null || _extracted_skills_languages === void 0 ? void 0 : _extracted_skills_languages.length) > 0 ? extracted.skills.languages : prevData.skills.languages,\n                        certifications: ((_extracted_skills2 = extracted.skills) === null || _extracted_skills2 === void 0 ? void 0 : (_extracted_skills_certifications = _extracted_skills2.certifications) === null || _extracted_skills_certifications === void 0 ? void 0 : _extracted_skills_certifications.length) > 0 ? extracted.skills.certifications : prevData.skills.certifications\n                    },\n                    projects: ((_extracted_projects = extracted.projects) === null || _extracted_projects === void 0 ? void 0 : _extracted_projects.length) > 0 ? extracted.projects.map((proj)=>({\n                            ...proj,\n                            id: proj.id || Date.now() + Math.random()\n                        })) : prevData.projects\n                };\n            });\n            // Set ATS score and suggestions\n            if (analysisData.atsScore) {\n                var _analysisData_enhancements, _analysisData_analysis;\n                setAtsScore(analysisData.atsScore.overall);\n                setSuggestions(((_analysisData_enhancements = analysisData.enhancements) === null || _analysisData_enhancements === void 0 ? void 0 : _analysisData_enhancements.suggestions) || ((_analysisData_analysis = analysisData.analysis) === null || _analysisData_analysis === void 0 ? void 0 : _analysisData_analysis.recommendations) || []);\n            }\n            console.log('✅ Form data updated with extracted information');\n        } else if (analysisData.fallback) {\n            console.log('⚠️ Using fallback data - minimal extraction');\n            // Even with fallback, try to extract any available information\n            if (analysisData.extractedData) {\n                const extracted = analysisData.extractedData;\n                setFormData((prevData)=>{\n                    var _extracted_personal;\n                    return {\n                        ...prevData,\n                        personal: {\n                            ...prevData.personal,\n                            summary: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.summary) || 'Please add your professional summary here.'\n                        }\n                    };\n                });\n            }\n        }\n    };\n    // Handle job description submission for targeted resume generation\n    const handleJobDescriptionSubmit = async (jobData)=>{\n        if (!(uploadAnalysis === null || uploadAnalysis === void 0 ? void 0 : uploadAnalysis.extractedData)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('No resume data available. Please upload a resume first.');\n            return;\n        }\n        try {\n            setIsGeneratingTargeted(true);\n            console.log('🎯 Generating targeted resume with job data:', jobData);\n            const response = await fetch('/api/generate-targeted-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    extractedResumeData: uploadAnalysis.extractedData,\n                    jobDescription: jobData.description,\n                    jobTitle: jobData.jobTitle,\n                    company: jobData.company\n                })\n            });\n            const data = await response.json();\n            console.log('📊 Targeted resume response:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate targeted resume');\n            }\n            // Update form data with enhanced resume\n            if (data.enhancedResume) {\n                var _data_enhancedResume_personal, _data_enhancedResume_personal1, _data_enhancedResume_personal2, _data_enhancedResume_personal3, _data_enhancedResume_personal4, _data_enhancedResume_personal5, _data_enhancedResume_personal6, _data_enhancedResume_personal7, _data_enhancedResume_education, _data_enhancedResume_experience, _data_enhancedResume_skills, _data_enhancedResume_skills1, _data_enhancedResume_skills2, _data_enhancedResume_projects, _data_atsScore;\n                // Properly structure the enhanced resume data to match form data structure\n                const enhancedFormData = {\n                    personal: {\n                        firstName: ((_data_enhancedResume_personal = data.enhancedResume.personal) === null || _data_enhancedResume_personal === void 0 ? void 0 : _data_enhancedResume_personal.firstName) || '',\n                        lastName: ((_data_enhancedResume_personal1 = data.enhancedResume.personal) === null || _data_enhancedResume_personal1 === void 0 ? void 0 : _data_enhancedResume_personal1.lastName) || '',\n                        email: ((_data_enhancedResume_personal2 = data.enhancedResume.personal) === null || _data_enhancedResume_personal2 === void 0 ? void 0 : _data_enhancedResume_personal2.email) || '',\n                        phone: ((_data_enhancedResume_personal3 = data.enhancedResume.personal) === null || _data_enhancedResume_personal3 === void 0 ? void 0 : _data_enhancedResume_personal3.phone) || '',\n                        location: ((_data_enhancedResume_personal4 = data.enhancedResume.personal) === null || _data_enhancedResume_personal4 === void 0 ? void 0 : _data_enhancedResume_personal4.location) || '',\n                        linkedin: ((_data_enhancedResume_personal5 = data.enhancedResume.personal) === null || _data_enhancedResume_personal5 === void 0 ? void 0 : _data_enhancedResume_personal5.linkedin) || '',\n                        portfolio: ((_data_enhancedResume_personal6 = data.enhancedResume.personal) === null || _data_enhancedResume_personal6 === void 0 ? void 0 : _data_enhancedResume_personal6.portfolio) || '',\n                        summary: ((_data_enhancedResume_personal7 = data.enhancedResume.personal) === null || _data_enhancedResume_personal7 === void 0 ? void 0 : _data_enhancedResume_personal7.summary) || ''\n                    },\n                    education: ((_data_enhancedResume_education = data.enhancedResume.education) === null || _data_enhancedResume_education === void 0 ? void 0 : _data_enhancedResume_education.length) > 0 ? data.enhancedResume.education.map((edu)=>({\n                            id: edu.id || Date.now() + Math.random(),\n                            degree: edu.degree || '',\n                            institution: edu.institution || '',\n                            location: edu.location || '',\n                            startDate: edu.startDate || '',\n                            endDate: edu.endDate || '',\n                            gpa: edu.gpa || '',\n                            relevant: edu.relevant || ''\n                        })) : [\n                        {\n                            id: 1,\n                            degree: \"\",\n                            institution: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            gpa: \"\",\n                            relevant: \"\"\n                        }\n                    ],\n                    experience: ((_data_enhancedResume_experience = data.enhancedResume.experience) === null || _data_enhancedResume_experience === void 0 ? void 0 : _data_enhancedResume_experience.length) > 0 ? data.enhancedResume.experience.map((exp)=>({\n                            id: exp.id || Date.now() + Math.random(),\n                            title: exp.title || '',\n                            company: exp.company || '',\n                            location: exp.location || '',\n                            startDate: exp.startDate || '',\n                            endDate: exp.endDate || '',\n                            current: exp.current || false,\n                            description: exp.description || (exp.achievements ? exp.achievements.join('\\n') : '')\n                        })) : [\n                        {\n                            id: 1,\n                            title: \"\",\n                            company: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            current: false,\n                            description: \"\"\n                        }\n                    ],\n                    skills: {\n                        technical: ((_data_enhancedResume_skills = data.enhancedResume.skills) === null || _data_enhancedResume_skills === void 0 ? void 0 : _data_enhancedResume_skills.technical) || [],\n                        languages: ((_data_enhancedResume_skills1 = data.enhancedResume.skills) === null || _data_enhancedResume_skills1 === void 0 ? void 0 : _data_enhancedResume_skills1.languages) || [],\n                        certifications: ((_data_enhancedResume_skills2 = data.enhancedResume.skills) === null || _data_enhancedResume_skills2 === void 0 ? void 0 : _data_enhancedResume_skills2.certifications) || []\n                    },\n                    projects: ((_data_enhancedResume_projects = data.enhancedResume.projects) === null || _data_enhancedResume_projects === void 0 ? void 0 : _data_enhancedResume_projects.length) > 0 ? data.enhancedResume.projects.map((proj)=>({\n                            id: proj.id || Date.now() + Math.random(),\n                            name: proj.name || '',\n                            description: proj.description || '',\n                            technologies: proj.technologies || '',\n                            link: proj.link || ''\n                        })) : [\n                        {\n                            id: 1,\n                            name: \"\",\n                            description: \"\",\n                            technologies: \"\",\n                            link: \"\"\n                        }\n                    ]\n                };\n                setFormData(enhancedFormData);\n                setTargetedResumeData(data);\n                setAtsScore(((_data_atsScore = data.atsScore) === null || _data_atsScore === void 0 ? void 0 : _data_atsScore.overall) || 85);\n                setSuggestions(data.recommendations || []);\n                // Show success and move to form editing\n                setShowJobDescriptionInput(false);\n                setCurrentStep(0);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume optimized for the target job!');\n            }\n        } catch (error) {\n            console.error('Targeted resume generation error:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate targeted resume');\n        } finally{\n            setIsGeneratingTargeted(false);\n        }\n    };\n    // Handle mode change\n    const handleModeChange = (mode)=>{\n        setBuilderMode(mode);\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setShowJobDescriptionInput(false);\n        setIsGeneratingTargeted(false);\n        setTargetedResumeData(null);\n    };\n    // Reset to create mode\n    const resetToCreateMode = ()=>{\n        setBuilderMode('create');\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setFormData({\n            personal: {\n                firstName: \"\",\n                lastName: \"\",\n                email: \"\",\n                phone: \"\",\n                location: \"\",\n                linkedin: \"\",\n                portfolio: \"\",\n                summary: \"\"\n            },\n            education: [\n                {\n                    id: 1,\n                    degree: \"\",\n                    institution: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    gpa: \"\",\n                    relevant: \"\"\n                }\n            ],\n            experience: [\n                {\n                    id: 1,\n                    title: \"\",\n                    company: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    current: false,\n                    description: \"\"\n                }\n            ],\n            skills: {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: [\n                {\n                    id: 1,\n                    name: \"\",\n                    description: \"\",\n                    technologies: \"\",\n                    link: \"\"\n                }\n            ]\n        });\n    };\n    // Floating background elements with deterministic values to avoid hydration mismatch\n    const FloatingElements = ()=>{\n        // Use deterministic values to avoid hydration mismatch\n        const elements = [\n            {\n                width: 180,\n                height: 160,\n                left: 10,\n                top: 20,\n                duration: 15,\n                x: 30,\n                y: 40\n            },\n            {\n                width: 220,\n                height: 190,\n                left: 80,\n                top: 60,\n                duration: 18,\n                x: -25,\n                y: -30\n            },\n            {\n                width: 150,\n                height: 140,\n                left: 60,\n                top: 80,\n                duration: 12,\n                x: 35,\n                y: 25\n            },\n            {\n                width: 200,\n                height: 170,\n                left: 30,\n                top: 40,\n                duration: 20,\n                x: -40,\n                y: 35\n            },\n            {\n                width: 170,\n                height: 200,\n                left: 90,\n                top: 10,\n                duration: 16,\n                x: 20,\n                y: -45\n            },\n            {\n                width: 190,\n                height: 150,\n                left: 20,\n                top: 70,\n                duration: 14,\n                x: -30,\n                y: 20\n            },\n            {\n                width: 160,\n                height: 180,\n                left: 70,\n                top: 30,\n                duration: 22,\n                x: 45,\n                y: -25\n            },\n            {\n                width: 210,\n                height: 160,\n                left: 50,\n                top: 90,\n                duration: 17,\n                x: -20,\n                y: 30\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: elements.map((element, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                    className: \"absolute rounded-full opacity-10 blur-xl\",\n                    style: {\n                        backgroundColor: '#832ED3',\n                        width: element.width,\n                        height: element.height,\n                        left: element.left + '%',\n                        top: element.top + '%'\n                    },\n                    animate: {\n                        x: [\n                            0,\n                            element.x,\n                            0\n                        ],\n                        y: [\n                            0,\n                            element.y,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: element.duration,\n                        repeat: Infinity,\n                        repeatType: 'reverse',\n                        ease: \"easeInOut\"\n                    }\n                }, i, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 519,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 517,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Show upload analysis results for quick ATS check\n    if (builderMode === 'analyze' && showAnalysis && uploadAnalysis) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 550,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 549,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 553,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                        children: \"ATS Analysis Results\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Here's your comprehensive resume analysis and recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 561,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"quick\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 570,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: ()=>handleModeChange('upload'),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Enhance This Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: resetToCreateMode,\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Start Fresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 575,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 556,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 555,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 548,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show success screen if resume is generated\n    if (resumeGenerated && resumeData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 608,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 607,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 611,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                        initial: {\n                                            scale: 0\n                                        },\n                                        animate: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                            className: \"h-10 w-10 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                        children: \"Resume Generated Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300\",\n                                        children: \"Your AI-optimized resume is ready for download\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6 text-center\",\n                                                children: \"ATS Compatibility Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    score: atsScore || 75,\n                                                    size: 150\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6\",\n                                                children: \"AI Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-left\",\n                                                children: suggestions.length > 0 ? suggestions.slice(0, 4).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                        className: \"flex items-start gap-3 p-3 bg-neural-purple/10 rounded-lg border border-neural-purple/20\",\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 10\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            delay: 0.8 + index * 0.1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-neural-purple rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: typeof suggestion === 'string' ? suggestion : suggestion.message || suggestion\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 23\n                                                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-center\",\n                                                    children: \"No specific recommendations at this time.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mb-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        formData: formData,\n                                        resumeData: resumeData,\n                                        templateId: selectedTemplate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__.ViewResumeButton, {\n                                        formData: formData,\n                                        resumeData: resumeData,\n                                        templateId: selectedTemplate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: ()=>setShowPreview(!showPreview),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 text-neural-blue hover:text-white font-medium rounded-xl transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? 'Hide Preview' : 'View Preview'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: ()=>{\n                                            setResumeGenerated(false);\n                                            setResumeData(null);\n                                            setAtsScore(null);\n                                            setSuggestions([]);\n                                            setCurrentStep(0);\n                                        },\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create Another Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 686,\n                                columnNumber: 13\n                            }, undefined),\n                            showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        formData: formData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 733,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 614,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 613,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 605,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormHeader__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                steps: steps,\n                onBack: ()=>window.history.back(),\n                onPreview: ()=>setShowPreview(!showPreview),\n                onSave: ()=>console.log('Save draft'),\n                showPreview: showPreview\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 753,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 765,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 764,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 768,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 770,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        className: \"text-center mb-8 md:mb-12 relative z-20\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-4 md:mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                className: \"h-8 w-8 md:h-10 md:w-10 text-neural-pink animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 785,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl lg:text-5xl font-bold text-white\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 788,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 783,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-4\",\n                                children: \"Create professional, ATS-friendly resumes in minutes with our AI-powered builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, undefined),\n                            builderMode === 'create' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            \"Step \",\n                                            currentStep + 1,\n                                            \" of \",\n                                            steps.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 800,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 798,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 777,\n                        columnNumber: 9\n                    }, undefined),\n                    (builderMode !== 'create' || currentStep === 0) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        currentMode: builderMode,\n                        onModeChange: handleModeChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 808,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'upload' || builderMode === 'analyze') && !showAnalysis && !showJobDescriptionInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            onAnalysisComplete: handleUploadAnalysis,\n                            analysisType: builderMode === 'analyze' ? 'quick' : 'full'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 821,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 816,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showJobDescriptionInput && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Uploaded Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Now provide the job description to create a targeted, ATS-optimized resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 837,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 835,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                onJobDescriptionSubmit: handleJobDescriptionSubmit,\n                                isLoading: isGeneratingTargeted\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 840,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                    onClick: ()=>{\n                                        setShowJobDescriptionInput(false);\n                                        setCurrentStep(0);\n                                    },\n                                    className: \"inline-flex items-center gap-2 px-6 py-3 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: \"Skip Job Targeting\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 845,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 830,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showAnalysis && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Analysis Complete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Review the extracted information and continue to enhance your resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 870,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 868,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 873,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                    onClick: ()=>setCurrentStep(0),\n                                    className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Continue to Form Editor\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 879,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 878,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 863,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'create' || builderMode === 'upload' && currentStep >= 0 && !showJobDescriptionInput) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                className: \"mb-8 md:mb-12\",\n                                initial: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StepIndicator__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    currentStep: currentStep,\n                                    totalSteps: steps.length,\n                                    steps: steps,\n                                    onStepClick: (stepIndex)=>{\n                                        if (stepIndex <= currentStep) {\n                                            setCurrentStep(stepIndex);\n                                        }\n                                    },\n                                    allowClickNavigation: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 902,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 896,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/10 shadow-2xl\",\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 md:mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(steps[currentStep].icon, {\n                                                                    className: \"h-6 w-6 md:h-7 md:w-7 text-neural-purple\"\n                                                                }),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl md:text-2xl font-bold text-white\",\n                                                                    children: steps[currentStep].title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 933,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 929,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm md:text-base\",\n                                                            children: steps[currentStep].description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 928,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_37__.AnimatePresence, {\n                                                    mode: \"wait\",\n                                                    children: [\n                                                        currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonalInfoForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 943,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EducationForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__.ExperienceForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 945,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__.SkillsProjectsForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__.ReviewForm, {\n                                                            formData: formData,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 39\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 942,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-4 md:p-6 border border-white/10 sticky top-4 md:top-8 shadow-2xl\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 0.4\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4 md:mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-base md:text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                        className: \"h-4 w-4 md:h-5 md:w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 962,\n                                                                        columnNumber: 19\n                                                                    }, undefined),\n                                                                    \"Live Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 961,\n                                                                columnNumber: 17\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                                                onClick: ()=>setShowPreview(!showPreview),\n                                                                className: \"px-3 py-1.5 md:px-4 md:py-2 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 rounded-lg text-neural-blue hover:text-white transition-all duration-300 text-sm font-medium\",\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                children: showPreview ? 'Hide' : 'Show'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 965,\n                                                                columnNumber: 17\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-[600px] md:max-h-[700px] overflow-y-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            formData: formData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 17\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12 md:py-16 text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                            initial: {\n                                                                scale: 0.8,\n                                                                opacity: 0\n                                                            },\n                                                            animate: {\n                                                                scale: 1,\n                                                                opacity: 1\n                                                            },\n                                                            transition: {\n                                                                duration: 0.5\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                    className: \"h-16 w-16 md:h-20 md:w-20 mx-auto mb-4 opacity-30\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm md:text-base mb-2\",\n                                                                    children: \"Preview your resume\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs md:text-sm text-gray-500\",\n                                                                    children: 'Click \"Show\" to see live updates'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 988,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                className: \"mt-6\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 0.6\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSOptimizationPanel__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        formData: formData,\n                                                        atsScore: atsAnalysis.overallScore,\n                                                        suggestions: atsAnalysis.recommendations,\n                                                        realTimeAnalysis: atsAnalysis\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1002,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 995,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 918,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pb-24\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1014,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TemplateSelector__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        selectedTemplate: selectedTemplate,\n                        onTemplateSelect: setSelectedTemplate,\n                        onClose: ()=>setShowTemplateSelector(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1020,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StickyNavigation__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        currentStep: currentStep,\n                        totalSteps: steps.length,\n                        onPrevious: ()=>{\n                            if (currentStep > 0) {\n                                setCurrentStep(currentStep - 1);\n                            }\n                        },\n                        onNext: ()=>{\n                            if (currentStep < steps.length - 1) {\n                                setCurrentStep(currentStep + 1);\n                            }\n                        },\n                        onGenerate: generateResume,\n                        isGenerating: isGenerating,\n                        canProceed: true,\n                        steps: steps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1028,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 775,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 751,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"ov9cNX+p3PbbnqwGct0srf01HjQ=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    ];\n});\n_c = ResumeBuilder;\n// Personal Information Form Component\nconst PersonalInfoForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis } = param;\n    var _atsAnalysis_fieldAnalysis, _atsAnalysis_fieldAnalysis1, _atsAnalysis_fieldAnalysis2, _atsAnalysis_fieldAnalysis3;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6 md:space-y-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                \"First Name *\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSTooltip__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    fieldType: \"firstName\",\n                                    className: \"ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1061,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.firstName,\n                            onChange: (e)=>updateFormData('personal', 'firstName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Rahul\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1065,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"firstName\",\n                                value: formData.personal.firstName,\n                                analysis: (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis.firstName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1073,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1072,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1060,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Last Name *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1082,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.lastName,\n                            onChange: (e)=>updateFormData('personal', 'lastName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Sharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1085,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"lastName\",\n                                value: formData.personal.lastName,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis1 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis1 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis1.lastName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1093,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1092,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1081,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1103,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Email *\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1102,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            value: formData.personal.email,\n                            onChange: (e)=>updateFormData('personal', 'email', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"<EMAIL>\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1106,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"email\",\n                                value: formData.personal.email,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis2 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis2 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis2.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1114,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1113,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1101,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1124,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Phone\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1123,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"tel\",\n                            value: formData.personal.phone,\n                            onChange: (e)=>updateFormData('personal', 'phone', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"+91 98765 43210\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1127,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1122,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1138,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Location\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1137,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.location,\n                            onChange: (e)=>updateFormData('personal', 'location', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Mumbai, Maharashtra\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1141,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1136,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"LinkedIn\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1151,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.linkedin,\n                            onChange: (e)=>updateFormData('personal', 'linkedin', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://linkedin.com/in/rahulsharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1155,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1150,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1166,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Portfolio/Website\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1165,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.portfolio,\n                            onChange: (e)=>updateFormData('personal', 'portfolio', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://rahulsharma.dev\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1169,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1164,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Professional Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1179,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: formData.personal.summary,\n                            onChange: (e)=>updateFormData('personal', 'summary', e.target.value),\n                            rows: 4,\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 resize-none transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Experienced software engineer with 5+ years in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable solutions and leading cross-functional teams...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1182,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"summary\",\n                                value: formData.personal.summary,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis3 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis3 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis3.summary,\n                                showDetails: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1190,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1189,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIContentSuggestions__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                fieldType: \"summary\",\n                                currentValue: formData.personal.summary,\n                                onSuggestionApply: (suggestion)=>updateFormData('personal', 'summary', suggestion),\n                                context: {\n                                    firstName: formData.personal.firstName,\n                                    lastName: formData.personal.lastName,\n                                    experience: formData.experience,\n                                    skills: formData.skills\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1200,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1199,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1178,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 1059,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1053,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PersonalInfoForm;\n// Education Form Component\nconst EducationForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem, atsAnalysis } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"h-6 w-6 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1227,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1228,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1226,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addArrayItem('education', {\n                                degree: \"\",\n                                institution: \"\",\n                                location: \"\",\n                                startDate: \"\",\n                                endDate: \"\",\n                                gpa: \"\",\n                                relevant: \"\"\n                            }),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1242,\n                                columnNumber: 9\n                            }, undefined),\n                            \"Add Education\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1230,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1225,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: formData.education.map((edu, index)=>{\n                    var _atsAnalysis_fieldAnalysis, _atsAnalysis_fieldAnalysis1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 p-6 rounded-lg border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Education \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeArrayItem('education', edu.id),\n                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 1257,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1253,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1250,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Degree *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1264,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.degree,\n                                                onChange: (e)=>updateFormData('education', 'degree', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"Bachelor of Science\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1267,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    fieldName: \"degree\",\n                                                    value: edu.degree,\n                                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis[\"education_degree_\".concat(index)]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1275,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1263,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Institution *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1284,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.institution,\n                                                onChange: (e)=>updateFormData('education', 'institution', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"University of Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    fieldName: \"institution\",\n                                                    value: edu.institution,\n                                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis1 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis1 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis1[\"education_institution_\".concat(index)]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1295,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1294,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1283,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Location\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.location,\n                                                onChange: (e)=>updateFormData('education', 'location', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"New York, NY\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1303,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"GPA (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1318,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.gpa,\n                                                onChange: (e)=>updateFormData('education', 'gpa', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"3.8/4.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1321,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1317,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1332,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Start Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1331,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.startDate,\n                                                onChange: (e)=>updateFormData('education', 'startDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1335,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1330,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1345,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"End Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1344,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.endDate,\n                                                onChange: (e)=>updateFormData('education', 'endDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1348,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1343,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Relevant Coursework/Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1357,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: edu.relevant,\n                                                onChange: (e)=>updateFormData('education', 'relevant', e.target.value, index),\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"Relevant coursework, honors, achievements...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1360,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1356,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1262,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, edu.id, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1249,\n                        columnNumber: 9\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1247,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1219,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EducationForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ResumeBuilder\");\n$RefreshReg$(_c1, \"PersonalInfoForm\");\n$RefreshReg$(_c2, \"EducationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ })

});