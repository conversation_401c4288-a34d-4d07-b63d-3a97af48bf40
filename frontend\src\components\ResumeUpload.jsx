"use client";
import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  X,
  Download,
  Eye
} from 'lucide-react';
import { toast } from 'react-hot-toast';

const ResumeUpload = ({ onAnalysisComplete, analysisType = 'full' }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, []);

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file) => {
    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload PDF, DOC, or DOCX files only');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      return;
    }

    setUploadedFile(file);
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('analysisType', analysisType);

      const response = await fetch('/api/upload-resume', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Upload failed');
      }

      // Delay to show 100% progress
      setTimeout(() => {
        setIsUploading(false);
        onAnalysisComplete(data);

        // Show appropriate success message
        if (data.fallback) {
          toast.success('Resume uploaded! Please review and edit the form manually.');
        } else {
          toast.success('Resume analyzed successfully!');
        }
      }, 500);

    } catch (error) {
      console.error('Upload error:', error);
      setIsUploading(false);
      setUploadedFile(null);
      setUploadProgress(0);

      // Show more specific error messages
      if (error.message.includes('Failed to fetch')) {
        toast.error('Network error. Please check your connection and try again.');
      } else if (error.message.includes('413')) {
        toast.error('File too large. Please upload a smaller file.');
      } else {
        toast.error(error.message || 'Failed to upload resume. Please try again.');
      }
    }
  };

  const removeFile = () => {
    setUploadedFile(null);
    setUploadProgress(0);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType) => {
    if (fileType === 'application/pdf') {
      return <FileText className="h-8 w-8 text-red-500" />;
    }
    return <FileText className="h-8 w-8 text-blue-500" />;
  };

  return (
    <div className="w-full">
      <AnimatePresence mode="wait">
        {!uploadedFile && !isUploading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
              isDragOver
                ? 'border-neural-purple bg-neural-purple/10'
                : 'border-gray-600 hover:border-gray-500'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="flex flex-col items-center space-y-4">
              <motion.div
                animate={{ 
                  scale: isDragOver ? 1.1 : 1,
                  rotate: isDragOver ? 5 : 0
                }}
                className="p-4 bg-neural-purple/20 rounded-full"
              >
                <Upload className="h-12 w-12 text-neural-purple" />
              </motion.div>
              
              <div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  Upload Your Resume
                </h3>
                <p className="text-gray-400 mb-4">
                  Drag and drop your resume here, or click to browse
                </p>
                <p className="text-sm text-gray-500">
                  Supports PDF, DOC, and DOCX files (max 10MB)
                </p>
              </div>

              <motion.label
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
              >
                <Upload className="h-4 w-4" />
                Choose File
                <input
                  type="file"
                  className="hidden"
                  accept=".pdf,.doc,.docx"
                  onChange={handleFileSelect}
                />
              </motion.label>
            </div>
          </motion.div>
        )}

        {(uploadedFile || isUploading) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                {getFileIcon(uploadedFile?.type)}
                <div>
                  <h4 className="font-semibold text-white">{uploadedFile?.name}</h4>
                  <p className="text-sm text-gray-400">
                    {formatFileSize(uploadedFile?.size || 0)}
                  </p>
                </div>
              </div>
              
              {!isUploading && (
                <button
                  onClick={removeFile}
                  className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <X className="h-4 w-4 text-gray-400" />
                </button>
              )}
            </div>

            {isUploading && (
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">
                    {uploadProgress < 90 ? 'Uploading...' : 'Analyzing with AI...'}
                  </span>
                  <span className="text-neural-purple font-semibold">
                    {uploadProgress}%
                  </span>
                </div>
                
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${uploadProgress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>

                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {uploadProgress < 30 && "Extracting text from resume..."}
                  {uploadProgress >= 30 && uploadProgress < 70 && "Processing content..."}
                  {uploadProgress >= 70 && uploadProgress < 90 && "Running AI analysis..."}
                  {uploadProgress >= 90 && "Finalizing results..."}
                </div>
              </div>
            )}

            {!isUploading && uploadProgress === 100 && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex items-center gap-2 text-green-400"
              >
                <CheckCircle className="h-5 w-5" />
                <span className="font-semibold">Analysis Complete!</span>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ResumeUpload;
