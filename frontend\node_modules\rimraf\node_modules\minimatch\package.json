{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "name": "minimatch", "description": "a glob matcher in javascript", "version": "10.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "engines": {"node": "20 || >=22"}, "dependencies": {"brace-expansion": "^2.0.1"}, "devDependencies": {"@types/brace-expansion": "^1.1.2", "@types/node": "^20.14.10", "mkdirp": "^3.0.1", "prettier": "^3.3.2", "tap": "^20.0.3", "tshy": "^2.0.1", "typedoc": "^0.26.3", "typescript": "^5.5.3"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "license": "ISC", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "module": "./dist/esm/index.js"}