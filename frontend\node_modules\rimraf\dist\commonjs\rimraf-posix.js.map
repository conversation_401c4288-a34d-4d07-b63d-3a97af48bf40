{"version": 3, "file": "rimraf-posix.js", "sourceRoot": "", "sources": ["../../src/rimraf-posix.ts"], "names": [], "mappings": ";AAAA,kEAAkE;AAClE,oDAAoD;AACpD,iEAAiE;AACjE,2DAA2D;AAC3D,4CAA4C;;;AAE5C,mCAAoE;AACpE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,gBAAQ,CAAA;AAEzC,+BAAqC;AAErC,+DAA0E;AAI1E,yDAAmE;AAE5D,MAAM,WAAW,GAAG,KAAK,EAAE,IAAY,EAAE,GAAuB,EAAE,EAAE;IACzE,IAAI,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACzB,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAA;IACzB,CAAC;IACD,IAAI,CAAC;QACH,OAAO,MAAM,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC3D,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ;YAAE,OAAO,IAAI,CAAA;QACjE,MAAM,EAAE,CAAA;IACV,CAAC;AACH,CAAC,CAAA;AAVY,QAAA,WAAW,eAUvB;AAEM,MAAM,eAAe,GAAG,CAAC,IAAY,EAAE,GAAsB,EAAE,EAAE;IACtE,IAAI,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACzB,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAA;IACzB,CAAC;IACD,IAAI,CAAC;QACH,OAAO,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,IAAA,iBAAS,EAAC,IAAI,CAAC,CAAC,CAAA;IACvD,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ;YAAE,OAAO,IAAI,CAAA;QACjE,MAAM,EAAE,CAAA;IACV,CAAC;AACH,CAAC,CAAA;AAVY,QAAA,eAAe,mBAU3B;AAED,MAAM,cAAc,GAAG,KAAK,EAC1B,IAAY,EACZ,GAAuB,EACvB,GAAmB,EACD,EAAE;IACpB,IAAI,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACzB,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAA;IACzB,CAAC;IACD,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,MAAM,IAAA,oCAAc,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACrE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5B,gEAAgE;QAChE,oDAAoD;QACpD,qBAAqB;QACrB,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAA;YACb,CAAC;YACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC/B,MAAM,OAAO,CAAA;YACf,CAAC;QACH,CAAC;QACD,oBAAoB;QACpB,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;YACjD,OAAO,KAAK,CAAA;QACd,CAAC;QACD,MAAM,IAAA,+BAAY,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;QAChC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,UAAU,GAAG,CACjB,MAAM,OAAO,CAAC,GAAG,CACf,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,cAAc,CAAC,IAAA,cAAO,EAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CACtE,CACF,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;IAEhC,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,sEAAsE;IACtE,+DAA+D;IAC/D,iDAAiD;IACjD,IAAI,GAAG,CAAC,YAAY,KAAK,KAAK,IAAI,IAAI,KAAK,IAAA,YAAK,EAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,OAAO,KAAK,CAAA;IACd,CAAC;IAED,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;QACjD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,IAAA,+BAAY,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC/B,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,MAAM,kBAAkB,GAAG,CACzB,IAAY,EACZ,GAAsB,EACtB,GAAmB,EACV,EAAE;IACX,IAAI,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACzB,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAA;IACzB,CAAC;IACD,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAA,wCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACnE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5B,gEAAgE;QAChE,oDAAoD;QACpD,qBAAqB;QACrB,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAA;YACb,CAAC;YACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC/B,MAAM,OAAO,CAAA;YACf,CAAC;QACH,CAAC;QACD,oBAAoB;QACpB,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACzC,OAAO,KAAK,CAAA;QACd,CAAC;QACD,IAAA,mCAAgB,EAAC,GAAG,EAAE,CAAC,IAAA,kBAAU,EAAC,IAAI,CAAC,CAAC,CAAA;QACxC,OAAO,IAAI,CAAA;IACb,CAAC;IACD,IAAI,UAAU,GAAY,IAAI,CAAA;IAC9B,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,MAAM,CAAC,GAAG,IAAA,cAAO,EAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;QACjC,UAAU,GAAG,kBAAkB,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,UAAU,CAAA;IAC5D,CAAC;IACD,IAAI,GAAG,CAAC,YAAY,KAAK,KAAK,IAAI,IAAI,KAAK,IAAA,YAAK,EAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,OAAO,KAAK,CAAA;IACd,CAAC;IAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;QACzC,OAAO,KAAK,CAAA;IACd,CAAC;IAED,IAAA,mCAAgB,EAAC,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,IAAI,CAAC,CAAC,CAAA;IACvC,OAAO,IAAI,CAAA;AACb,CAAC,CAAA", "sourcesContent": ["// the simple recursive removal, where unlink and rmdir are atomic\n// Note that this approach does NOT work on Windows!\n// We stat first and only unlink if the Dirent isn't a directory,\n// because sun<PERSON> will let root unlink a directory, and some\n// SUPER weird breakage happens as a result.\n\nimport { lstatSync, promises, rmdirSync, unlinkSync } from './fs.js'\nconst { lstat, rmdir, unlink } = promises\n\nimport { parse, resolve } from 'path'\n\nimport { readdirOrError, readdirOrErrorSync } from './readdir-or-error.js'\n\nimport { Dirent, Stats } from 'fs'\nimport { RimrafAsyncOptions, RimrafSyncOptions } from './index.js'\nimport { ignoreENOENT, ignoreENOENTSync } from './ignore-enoent.js'\n\nexport const rimrafPosix = async (path: string, opt: RimrafAsyncOptions) => {\n  if (opt?.signal?.aborted) {\n    throw opt.signal.reason\n  }\n  try {\n    return await rimrafPosixDir(path, opt, await lstat(path))\n  } catch (er) {\n    if ((er as NodeJS.ErrnoException)?.code === 'ENOENT') return true\n    throw er\n  }\n}\n\nexport const rimrafPosixSync = (path: string, opt: RimrafSyncOptions) => {\n  if (opt?.signal?.aborted) {\n    throw opt.signal.reason\n  }\n  try {\n    return rimrafPosixDirSync(path, opt, lstatSync(path))\n  } catch (er) {\n    if ((er as NodeJS.ErrnoException)?.code === 'ENOENT') return true\n    throw er\n  }\n}\n\nconst rimrafPosixDir = async (\n  path: string,\n  opt: RimrafAsyncOptions,\n  ent: Dirent | Stats,\n): Promise<boolean> => {\n  if (opt?.signal?.aborted) {\n    throw opt.signal.reason\n  }\n  const entries = ent.isDirectory() ? await readdirOrError(path) : null\n  if (!Array.isArray(entries)) {\n    // this can only happen if lstat/readdir lied, or if the dir was\n    // swapped out with a file at just the right moment.\n    /* c8 ignore start */\n    if (entries) {\n      if (entries.code === 'ENOENT') {\n        return true\n      }\n      if (entries.code !== 'ENOTDIR') {\n        throw entries\n      }\n    }\n    /* c8 ignore stop */\n    if (opt.filter && !(await opt.filter(path, ent))) {\n      return false\n    }\n    await ignoreENOENT(unlink(path))\n    return true\n  }\n\n  const removedAll = (\n    await Promise.all(\n      entries.map(ent => rimrafPosixDir(resolve(path, ent.name), opt, ent)),\n    )\n  ).reduce((a, b) => a && b, true)\n\n  if (!removedAll) {\n    return false\n  }\n\n  // we don't ever ACTUALLY try to unlink /, because that can never work\n  // but when preserveRoot is false, we could be operating on it.\n  // No need to check if preserveRoot is not false.\n  if (opt.preserveRoot === false && path === parse(path).root) {\n    return false\n  }\n\n  if (opt.filter && !(await opt.filter(path, ent))) {\n    return false\n  }\n\n  await ignoreENOENT(rmdir(path))\n  return true\n}\n\nconst rimrafPosixDirSync = (\n  path: string,\n  opt: RimrafSyncOptions,\n  ent: Dirent | Stats,\n): boolean => {\n  if (opt?.signal?.aborted) {\n    throw opt.signal.reason\n  }\n  const entries = ent.isDirectory() ? readdirOrErrorSync(path) : null\n  if (!Array.isArray(entries)) {\n    // this can only happen if lstat/readdir lied, or if the dir was\n    // swapped out with a file at just the right moment.\n    /* c8 ignore start */\n    if (entries) {\n      if (entries.code === 'ENOENT') {\n        return true\n      }\n      if (entries.code !== 'ENOTDIR') {\n        throw entries\n      }\n    }\n    /* c8 ignore stop */\n    if (opt.filter && !opt.filter(path, ent)) {\n      return false\n    }\n    ignoreENOENTSync(() => unlinkSync(path))\n    return true\n  }\n  let removedAll: boolean = true\n  for (const ent of entries) {\n    const p = resolve(path, ent.name)\n    removedAll = rimrafPosixDirSync(p, opt, ent) && removedAll\n  }\n  if (opt.preserveRoot === false && path === parse(path).root) {\n    return false\n  }\n\n  if (!removedAll) {\n    return false\n  }\n\n  if (opt.filter && !opt.filter(path, ent)) {\n    return false\n  }\n\n  ignoreENOENTSync(() => rmdirSync(path))\n  return true\n}\n"]}