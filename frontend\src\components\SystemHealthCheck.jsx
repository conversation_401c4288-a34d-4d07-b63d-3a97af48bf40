"use client";
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Loader2,
  Settings,
  Zap,
  Upload,
  Target
} from 'lucide-react';

const SystemHealthCheck = () => {
  const [checks, setChecks] = useState({
    apiKey: { status: 'pending', message: 'Checking API key...' },
    generateResume: { status: 'pending', message: 'Testing resume generation...' },
    uploadAnalysis: { status: 'pending', message: 'Testing upload analysis...' },
    targetedGeneration: { status: 'pending', message: 'Testing targeted generation...' }
  });
  const [isRunning, setIsRunning] = useState(false);

  const runHealthCheck = async () => {
    setIsRunning(true);
    
    // Test 1: API Key Configuration
    try {
      const response = await fetch('/api/generate-resume', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          personal: { firstName: 'Test', lastName: 'User', email: '<EMAIL>' },
          education: [],
          experience: [],
          skills: { technical: ['JavaScript'], languages: [], certifications: [] },
          projects: []
        })
      });
      
      if (response.ok) {
        setChecks(prev => ({
          ...prev,
          apiKey: { status: 'success', message: 'API key configured correctly' },
          generateResume: { status: 'success', message: 'Resume generation working' }
        }));
      } else {
        setChecks(prev => ({
          ...prev,
          apiKey: { status: 'error', message: 'API key issue or quota exceeded' },
          generateResume: { status: 'error', message: 'Resume generation failed' }
        }));
      }
    } catch (error) {
      setChecks(prev => ({
        ...prev,
        apiKey: { status: 'error', message: 'Network error or API unavailable' },
        generateResume: { status: 'error', message: 'Resume generation failed' }
      }));
    }

    // Test 2: Upload Analysis (simulate)
    try {
      const formData = new FormData();
      const testFile = new Blob(['Test resume content'], { type: 'text/plain' });
      formData.append('file', testFile, 'test-resume.txt');
      formData.append('analysisType', 'quick');

      const response = await fetch('/api/upload-resume', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        setChecks(prev => ({
          ...prev,
          uploadAnalysis: { status: 'success', message: 'Upload analysis working' }
        }));
      } else {
        setChecks(prev => ({
          ...prev,
          uploadAnalysis: { status: 'warning', message: 'Upload works but may use fallback' }
        }));
      }
    } catch (error) {
      setChecks(prev => ({
        ...prev,
        uploadAnalysis: { status: 'error', message: 'Upload analysis failed' }
      }));
    }

    // Test 3: Targeted Generation
    try {
      const response = await fetch('/api/generate-targeted-resume', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          extractedResumeData: {
            personal: { firstName: 'Test', lastName: 'User' },
            experience: [],
            education: [],
            skills: { technical: [], languages: [], certifications: [] },
            projects: []
          },
          jobDescription: 'Test job description for software developer position',
          jobTitle: 'Software Developer',
          company: 'Test Company'
        })
      });

      if (response.ok) {
        setChecks(prev => ({
          ...prev,
          targetedGeneration: { status: 'success', message: 'Targeted generation working' }
        }));
      } else {
        setChecks(prev => ({
          ...prev,
          targetedGeneration: { status: 'warning', message: 'Targeted generation works but may use fallback' }
        }));
      }
    } catch (error) {
      setChecks(prev => ({
        ...prev,
        targetedGeneration: { status: 'error', message: 'Targeted generation failed' }
      }));
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-400" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-400" />;
      default:
        return <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'border-green-500/30 bg-green-900/20';
      case 'warning':
        return 'border-yellow-500/30 bg-yellow-900/20';
      case 'error':
        return 'border-red-500/30 bg-red-900/20';
      default:
        return 'border-gray-500/30 bg-gray-900/20';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700"
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Settings className="h-6 w-6 text-neural-blue" />
          <h3 className="text-xl font-bold text-white">System Health Check</h3>
        </div>
        <motion.button
          onClick={runHealthCheck}
          disabled={isRunning}
          className="px-4 py-2 bg-neural-blue hover:bg-neural-blue/80 text-white rounded-lg font-medium transition-colors disabled:opacity-50"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {isRunning ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
              Running...
            </>
          ) : (
            'Run Check'
          )}
        </motion.button>
      </div>

      <div className="space-y-4">
        {/* API Key Check */}
        <div className={`p-4 rounded-lg border ${getStatusColor(checks.apiKey.status)}`}>
          <div className="flex items-center gap-3">
            {getStatusIcon(checks.apiKey.status)}
            <div className="flex-1">
              <h4 className="font-semibold text-white flex items-center gap-2">
                <Zap className="h-4 w-4" />
                API Configuration
              </h4>
              <p className="text-sm text-gray-300">{checks.apiKey.message}</p>
            </div>
          </div>
        </div>

        {/* Resume Generation Check */}
        <div className={`p-4 rounded-lg border ${getStatusColor(checks.generateResume.status)}`}>
          <div className="flex items-center gap-3">
            {getStatusIcon(checks.generateResume.status)}
            <div className="flex-1">
              <h4 className="font-semibold text-white flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Resume Generation
              </h4>
              <p className="text-sm text-gray-300">{checks.generateResume.message}</p>
            </div>
          </div>
        </div>

        {/* Upload Analysis Check */}
        <div className={`p-4 rounded-lg border ${getStatusColor(checks.uploadAnalysis.status)}`}>
          <div className="flex items-center gap-3">
            {getStatusIcon(checks.uploadAnalysis.status)}
            <div className="flex-1">
              <h4 className="font-semibold text-white flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Upload & Analysis
              </h4>
              <p className="text-sm text-gray-300">{checks.uploadAnalysis.message}</p>
            </div>
          </div>
        </div>

        {/* Targeted Generation Check */}
        <div className={`p-4 rounded-lg border ${getStatusColor(checks.targetedGeneration.status)}`}>
          <div className="flex items-center gap-3">
            {getStatusIcon(checks.targetedGeneration.status)}
            <div className="flex-1">
              <h4 className="font-semibold text-white flex items-center gap-2">
                <Target className="h-4 w-4" />
                Job Targeting
              </h4>
              <p className="text-sm text-gray-300">{checks.targetedGeneration.message}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-neural-purple/10 rounded-lg border border-neural-purple/20">
        <h4 className="font-semibold text-neural-purple mb-2">System Status Summary</h4>
        <p className="text-sm text-gray-300">
          This health check verifies that all core features are working properly. 
          Green indicates full functionality, yellow indicates working with fallbacks, 
          and red indicates issues that need attention.
        </p>
      </div>
    </motion.div>
  );
};

export default SystemHealthCheck;
