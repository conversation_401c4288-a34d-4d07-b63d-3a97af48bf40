"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* harmony import */ var _components_SuccessScreen__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SuccessScreen */ \"(app-pages-browser)/./src/components/SuccessScreen.jsx\");\n/* harmony import */ var _components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ResumeFormComponents */ \"(app-pages-browser)/./src/components/ResumeFormComponents.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ResumeBuilder = ()=>{\n    _s();\n    // Main state management\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ]\n    });\n    // Steps configuration\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Tell us about yourself\"\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Your academic background\"\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Your work experience\"\n        },\n        {\n            id: 3,\n            title: \"Skills & Projects\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Showcase your abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Finalize your resume\"\n        }\n    ];\n    // Update form data\n    const updateFormData = function(section, field, value) {\n        let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        setFormData((prev)=>{\n            if (index !== null && Array.isArray(prev[section])) {\n                const newArray = [\n                    ...prev[section]\n                ];\n                newArray[index] = {\n                    ...newArray[index],\n                    [field]: value\n                };\n                return {\n                    ...prev,\n                    [section]: newArray\n                };\n            } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                return {\n                    ...prev,\n                    [section]: {\n                        ...prev[section],\n                        [field]: value\n                    }\n                };\n            }\n            return prev;\n        });\n    };\n    // Add new item to array sections\n    const addArrayItem = (section, template)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: [\n                    ...prev[section],\n                    {\n                        ...template,\n                        id: Math.random().toString(36).substring(2, 11)\n                    }\n                ]\n            }));\n    };\n    // Remove item from array sections\n    const removeArrayItem = (section, id)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: prev[section].filter((item)=>item.id !== id)\n            }));\n    };\n    // Navigation functions\n    const nextStep = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    // Generate resume with AI\n    const generateResume = async ()=>{\n        try {\n            setIsGenerating(true);\n            setShowProgressBar(true);\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            setResumeUrl(data.downloadUrl);\n        } catch (error) {\n            console.error(\"Error generating resume:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || \"Failed to generate resume\");\n            setIsGenerating(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsGenerating(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Your resume has been generated successfully!\");\n    };\n    // Floating background elements\n    const FloatingElements = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: [\n                ...Array(8)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    className: \"absolute rounded-full opacity-10 blur-xl\",\n                    style: {\n                        backgroundColor: '#832ED3',\n                        width: Math.random() * 200 + 100,\n                        height: Math.random() * 200 + 100,\n                        left: Math.random() * 100 + '%',\n                        top: Math.random() * 100 + '%'\n                    },\n                    animate: {\n                        x: [\n                            0,\n                            Math.random() * 100 - 50,\n                            0\n                        ],\n                        y: [\n                            0,\n                            Math.random() * 100 - 50,\n                            0\n                        ],\n                        transition: {\n                            duration: Math.random() * 20 + 20,\n                            repeat: Infinity,\n                            repeatType: 'reverse'\n                        }\n                    }\n                }, i, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 212,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        className: \"text-center mb-12 relative z-20\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-neural-pink\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                children: \"Create professional, ATS-friendly resumes in minutes with our AI-powered builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center space-x-4 overflow-x-auto pb-4\",\n                            children: steps.map((step, index)=>{\n                                const Icon = step.icon;\n                                const isActive = index === currentStep;\n                                const isCompleted = index < currentStep;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-300 \".concat(isActive ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25' : isCompleted ? 'bg-neural-purple/20 border-neural-purple/50' : 'bg-gray-800/50 border-gray-700'),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5 \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium hidden md:block \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400'),\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, step.id, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: [\n                                            currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonalInfoForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EducationForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.ExperienceForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.SkillsProjectsForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.ReviewForm, {\n                                                formData: formData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 39\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10 sticky top-8\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-neural-blue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Live Preview\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowPreview(!showPreview),\n                                                    className: \"text-neural-blue hover:text-neural-pink transition-colors\",\n                                                    children: showPreview ? 'Hide' : 'Show'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResumePreview, {\n                                            formData: formData\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: 'Click \"Show\" to preview your resume'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevStep,\n                                disabled: currentStep === 0,\n                                className: \"flex items-center gap-2 px-6 py-3 rounded-lg transition-all \".concat(currentStep === 0 ? 'bg-gray-800 text-gray-500 cursor-not-allowed' : 'bg-gray-800 hover:bg-gray-700 text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Previous\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, undefined),\n                            currentStep === steps.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: generateResume,\n                                disabled: isGenerating,\n                                className: \"flex items-center gap-2 px-8 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity disabled:opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isGenerating ? 'Generating...' : 'Generate Resume'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextStep,\n                                className: \"flex items-center gap-2 px-6 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity\",\n                                children: [\n                                    \"Next\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"VGPAcYlXC0IsI/lEDTa3PxHUi44=\");\n_c = ResumeBuilder;\n// Personal Information Form Component\nconst PersonalInfoForm = (param)=>{\n    let { formData, updateFormData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-6 w-6 text-neural-purple\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 403,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"Personal Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 404,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 402,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"First Name *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 409,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.firstName,\n                                onChange: (e)=>updateFormData('personal', 'firstName', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"John\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 412,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 408,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Last Name *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 422,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.lastName,\n                                onChange: (e)=>updateFormData('personal', 'lastName', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"Doe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 425,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 421,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Email *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 435,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"email\",\n                                value: formData.personal.email,\n                                onChange: (e)=>updateFormData('personal', 'email', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 439,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 434,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Phone\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 449,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"tel\",\n                                value: formData.personal.phone,\n                                onChange: (e)=>updateFormData('personal', 'phone', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"+****************\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 453,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 448,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Location\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 463,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.location,\n                                onChange: (e)=>updateFormData('personal', 'location', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"New York, NY\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 467,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 462,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"LinkedIn\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 477,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.personal.linkedin,\n                                onChange: (e)=>updateFormData('personal', 'linkedin', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"https://linkedin.com/in/johndoe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 481,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 476,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Portfolio/Website\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 491,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.personal.portfolio,\n                                onChange: (e)=>updateFormData('personal', 'portfolio', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"https://johndoe.com\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 495,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 490,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Professional Summary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 505,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: formData.personal.summary,\n                                onChange: (e)=>updateFormData('personal', 'summary', e.target.value),\n                                rows: 4,\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                placeholder: \"Brief overview of your professional background and key achievements...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 508,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 504,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 407,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 396,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PersonalInfoForm;\n// Education Form Component\nconst EducationForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 530,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 531,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 529,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addArrayItem('education', {\n                                degree: \"\",\n                                institution: \"\",\n                                location: \"\",\n                                startDate: \"\",\n                                endDate: \"\",\n                                gpa: \"\",\n                                relevant: \"\"\n                            }),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 545,\n                                columnNumber: 9\n                            }, undefined),\n                            \"Add Education\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 533,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 528,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: formData.education.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 p-6 rounded-lg border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Education \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeArrayItem('education', edu.id),\n                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 553,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Degree *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.degree,\n                                                onChange: (e)=>updateFormData('education', 'degree', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"Bachelor of Science\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Institution *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.institution,\n                                                onChange: (e)=>updateFormData('education', 'institution', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"University of Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Location\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.location,\n                                                onChange: (e)=>updateFormData('education', 'location', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"New York, NY\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"GPA (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.gpa,\n                                                onChange: (e)=>updateFormData('education', 'gpa', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"3.8/4.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Start Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.startDate,\n                                                onChange: (e)=>updateFormData('education', 'startDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"End Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.endDate,\n                                                onChange: (e)=>updateFormData('education', 'endDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Relevant Coursework/Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: edu.relevant,\n                                                onChange: (e)=>updateFormData('education', 'relevant', e.target.value, index),\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"Relevant coursework, honors, achievements...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 565,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, edu.id, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 552,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 550,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 522,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EducationForm;\n// Resume Preview Component\nconst ResumePreview = (param)=>{\n    let { formData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white text-black p-6 rounded-lg text-xs max-h-96 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center border-b border-gray-300 pb-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: [\n                            formData.personal.firstName,\n                            \" \",\n                            formData.personal.lastName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 669,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-2 mt-2 text-gray-600\",\n                        children: [\n                            formData.personal.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 673,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 674,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.phone\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 675,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 676,\n                                columnNumber: 40\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.location\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 677,\n                                columnNumber: 40\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 672,\n                        columnNumber: 7\n                    }, undefined),\n                    formData.personal.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 text-blue-600\",\n                        children: formData.personal.linkedin\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 668,\n                columnNumber: 5\n            }, undefined),\n            formData.personal.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"PROFESSIONAL SUMMARY\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 leading-relaxed\",\n                        children: formData.personal.summary\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 690,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 686,\n                columnNumber: 7\n            }, undefined),\n            formData.experience.length > 0 && formData.experience[0].title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"EXPERIENCE\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 697,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.experience.map((exp)=>exp.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: exp.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: exp.company\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        exp.startDate,\n                                                        \" - \",\n                                                        exp.current ? 'Present' : exp.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                exp.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: exp.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 15\n                                }, undefined),\n                                exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-gray-700 whitespace-pre-line\",\n                                    children: exp.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, exp.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 702,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 696,\n                columnNumber: 7\n            }, undefined),\n            formData.education.length > 0 && formData.education[0].degree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"EDUCATION\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 727,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.education.map((edu)=>edu.degree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: edu.degree\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: edu.institution\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        edu.startDate,\n                                                        \" - \",\n                                                        edu.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                edu.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: edu.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 15\n                                }, undefined),\n                                edu.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"GPA: \",\n                                        edu.gpa\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 27\n                                }, undefined),\n                                edu.relevant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: edu.relevant\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 32\n                                }, undefined)\n                            ]\n                        }, edu.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 732,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 726,\n                columnNumber: 7\n            }, undefined),\n            (formData.skills.technical.length > 0 || formData.skills.languages.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"SKILLS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 754,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.skills.technical.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Technical: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 759,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.technical.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 760,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 758,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.skills.languages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Languages: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 765,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.languages.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 766,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 764,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.skills.certifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Certifications: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 771,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.certifications.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 772,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 770,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 753,\n                columnNumber: 7\n            }, undefined),\n            formData.projects.length > 0 && formData.projects[0].name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"PROJECTS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 781,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.projects.map((project)=>project.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: project.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        project.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: project.link,\n                                            className: \"text-blue-600 hover:underline\",\n                                            children: \"Link\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 15\n                                }, undefined),\n                                project.technologies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 italic\",\n                                    children: project.technologies\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 796,\n                                    columnNumber: 17\n                                }, undefined),\n                                project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: project.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, project.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 786,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 780,\n                columnNumber: 7\n            }, undefined),\n            !formData.personal.firstName && !formData.personal.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 810,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Start filling out the form to see your resume preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 811,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 809,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 666,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = ResumePreview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ResumeBuilder\");\n$RefreshReg$(_c1, \"PersonalInfoForm\");\n$RefreshReg$(_c2, \"EducationForm\");\n$RefreshReg$(_c3, \"ResumePreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ })

});