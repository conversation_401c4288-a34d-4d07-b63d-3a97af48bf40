/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-targeted-resume/route";
exports.ids = ["app/api/generate-targeted-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_targeted_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-targeted-resume/route.js */ \"(rsc)/./src/app/api/generate-targeted-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-targeted-resume/route\",\n        pathname: \"/api/generate-targeted-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-targeted-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\generate-targeted-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_targeted_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-targeted-resume/route.js":
/*!*******************************************************!*\
  !*** ./src/app/api/generate-targeted-resume/route.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        console.log('🎯 Targeted resume generation API called');\n        const { extractedResumeData, jobDescription, jobTitle, company } = await request.json();\n        if (!extractedResumeData || !jobDescription) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required data: resume data and job description are required'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📊 Processing targeted resume generation...');\n        console.log('🎯 Job Title:', jobTitle);\n        console.log('🏢 Company:', company);\n        console.log('📝 Job Description Length:', jobDescription.length);\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback enhancement');\n            return createFallbackTargetedResume(extractedResumeData, jobDescription, jobTitle, company);\n        }\n        try {\n            // Get the generative model\n            const model = genAI.getGenerativeModel({\n                model: 'gemini-1.5-flash-latest',\n                generationConfig: {\n                    temperature: 0.7,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 4000\n                }\n            });\n            const prompt = createTargetedResumePrompt(extractedResumeData, jobDescription, jobTitle, company);\n            console.log('🤖 Calling Gemini AI for targeted resume generation...');\n            const result = await model.generateContent(prompt);\n            const response = await result.response;\n            const generatedContent = response.text();\n            console.log('✅ Gemini targeted analysis completed, length:', generatedContent?.length || 0);\n            // Parse the AI response\n            const enhancedResume = parseTargetedResumeResponse(generatedContent);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                enhancedResume,\n                jobTargeting: {\n                    jobTitle,\n                    company,\n                    description: jobDescription.substring(0, 200) + '...'\n                },\n                processedAt: new Date().toISOString()\n            });\n        } catch (aiError) {\n            console.error('AI enhancement error:', aiError);\n            console.log('⚠️ AI enhancement failed, using fallback enhancement');\n            return createFallbackTargetedResume(extractedResumeData, jobDescription, jobTitle, company);\n        }\n    } catch (error) {\n        console.error('💥 Targeted resume generation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate targeted resume',\n            details:  true ? error.message : 0\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createTargetedResumePrompt(resumeData, jobDescription, jobTitle, company) {\n    return `\nYou are an expert resume writer and ATS optimization specialist. Create a highly targeted, ATS-optimized resume based on the provided resume data and job description.\n\nORIGINAL RESUME DATA:\n${JSON.stringify(resumeData, null, 2)}\n\nTARGET JOB INFORMATION:\nJob Title: ${jobTitle || 'Not specified'}\nCompany: ${company || 'Not specified'}\nJob Description: ${jobDescription}\n\nTASK: Create an enhanced, job-targeted resume that:\n1. Optimizes keywords from the job description naturally throughout the resume\n2. Highlights the most relevant experience and skills for this specific role\n3. Quantifies achievements with metrics where possible\n4. Creates a compelling professional summary targeted to this job\n5. Prioritizes and emphasizes experiences most relevant to the job requirements\n6. Uses action verbs and industry terminology from the job description\n7. Ensures ATS compatibility with proper formatting and keyword density\n\nPlease respond in the following JSON format:\n{\n  \"enhancedResume\": {\n    \"personal\": {\n      \"firstName\": \"Enhanced first name\",\n      \"lastName\": \"Enhanced last name\", \n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"location\": \"City, State\",\n      \"linkedin\": \"linkedin.com/in/profile\",\n      \"portfolio\": \"portfolio.com\",\n      \"summary\": \"Compelling 3-4 sentence professional summary specifically targeted to this job, incorporating key requirements and showcasing relevant value proposition\"\n    },\n    \"experience\": [\n      {\n        \"id\": 1,\n        \"title\": \"Job Title\",\n        \"company\": \"Company Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"YYYY-MM\",\n        \"endDate\": \"YYYY-MM\",\n        \"current\": false,\n        \"description\": \"Enhanced description with quantified achievements\",\n        \"achievements\": [\n          \"• Quantified achievement that aligns with job requirements\",\n          \"• Another achievement using keywords from job description\",\n          \"• Third achievement showing relevant impact and skills\"\n        ]\n      }\n    ],\n    \"education\": [\n      {\n        \"id\": 1,\n        \"degree\": \"Degree Name\",\n        \"institution\": \"Institution Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"YYYY-MM\",\n        \"endDate\": \"YYYY-MM\",\n        \"gpa\": \"GPA if relevant\",\n        \"relevant\": \"Relevant coursework that aligns with job requirements\"\n      }\n    ],\n    \"skills\": {\n      \"technical\": [\"Prioritized technical skills matching job requirements\"],\n      \"languages\": [\"Languages if relevant to the job\"],\n      \"certifications\": [\"Relevant certifications for this role\"]\n    },\n    \"projects\": [\n      {\n        \"id\": 1,\n        \"name\": \"Project Name\",\n        \"description\": \"Project description highlighting skills relevant to the job\",\n        \"technologies\": \"Technologies that match job requirements\",\n        \"link\": \"project-link.com\"\n      }\n    ]\n  },\n  \"atsScore\": {\n    \"overall\": 95,\n    \"breakdown\": {\n      \"keywords\": 95,\n      \"formatting\": 90,\n      \"structure\": 95,\n      \"achievements\": 90,\n      \"skills\": 95\n    }\n  },\n  \"targetingAnalysis\": {\n    \"keywordMatches\": [\"List of keywords from job description incorporated\"],\n    \"skillsAlignment\": [\"Skills that directly match job requirements\"],\n    \"experienceRelevance\": \"Analysis of how experience aligns with job\",\n    \"improvementsMade\": [\n      \"Specific improvement made for job targeting\",\n      \"Another enhancement for ATS optimization\",\n      \"Additional targeting improvement\"\n    ]\n  },\n  \"recommendations\": [\n    \"Specific recommendation for this job application\",\n    \"Another targeted suggestion\",\n    \"Additional advice for this role\"\n  ]\n}\n\nEnsure the enhanced resume is specifically optimized for this job while maintaining authenticity and accuracy of the original information.\n`;\n}\nfunction parseTargetedResumeResponse(generatedContent) {\n    try {\n        // Try to extract JSON from the response\n        const jsonMatch = generatedContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            const parsedJson = JSON.parse(jsonMatch[0]);\n            return parsedJson;\n        }\n    } catch (error) {\n        console.error('Error parsing targeted resume response:', error);\n    }\n    // Fallback response\n    return {\n        enhancedResume: {\n            personal: {\n                firstName: '',\n                lastName: '',\n                email: '',\n                phone: '',\n                location: '',\n                summary: 'Enhanced professional summary targeting the specific job requirements.'\n            },\n            experience: [],\n            education: [],\n            skills: {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: []\n        },\n        atsScore: {\n            overall: 85,\n            breakdown: {\n                keywords: 85,\n                formatting: 85,\n                structure: 85,\n                achievements: 85,\n                skills: 85\n            }\n        },\n        targetingAnalysis: {\n            keywordMatches: [],\n            skillsAlignment: [],\n            experienceRelevance: 'Resume has been optimized for the target job.',\n            improvementsMade: [\n                'Job-specific optimization applied',\n                'ATS compatibility enhanced'\n            ]\n        },\n        recommendations: [\n            'Review the enhanced resume for accuracy',\n            'Customize further based on specific job requirements'\n        ]\n    };\n}\nfunction createFallbackTargetedResume(resumeData, jobDescription, jobTitle, company) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        enhancedResume: {\n            ...resumeData,\n            personal: {\n                ...resumeData.personal,\n                summary: resumeData.personal.summary || `Experienced professional seeking ${jobTitle || 'the target position'} role${company ? ` at ${company}` : ''}. Ready to contribute relevant skills and experience to drive organizational success.`\n            }\n        },\n        atsScore: {\n            overall: 80,\n            breakdown: {\n                keywords: 75,\n                formatting: 85,\n                structure: 80,\n                achievements: 75,\n                skills: 85\n            }\n        },\n        targetingAnalysis: {\n            keywordMatches: [\n                'Basic optimization applied'\n            ],\n            skillsAlignment: [\n                'Manual review recommended'\n            ],\n            experienceRelevance: 'Resume structure optimized for ATS compatibility.',\n            improvementsMade: [\n                'Basic job targeting applied',\n                'ATS formatting enhanced'\n            ]\n        },\n        recommendations: [\n            'Review and manually optimize keywords from the job description',\n            'Quantify achievements with specific metrics',\n            'Highlight experience most relevant to the target role',\n            'Consider adding skills mentioned in the job description'\n        ],\n        fallback: true\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-targeted-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();