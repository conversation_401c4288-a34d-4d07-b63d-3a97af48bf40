/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-targeted-resume/route";
exports.ids = ["app/api/generate-targeted-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_targeted_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-targeted-resume/route.js */ \"(rsc)/./src/app/api/generate-targeted-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-targeted-resume/route\",\n        pathname: \"/api/generate-targeted-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-targeted-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\generate-targeted-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_targeted_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-targeted-resume/route.js":
/*!*******************************************************!*\
  !*** ./src/app/api/generate-targeted-resume/route.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        console.log('🎯 Targeted resume generation API called');\n        const { extractedResumeData, jobDescription, jobTitle, company } = await request.json();\n        if (!extractedResumeData || !jobDescription) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required data: resume data and job description are required'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📊 Processing targeted resume generation...');\n        console.log('🎯 Job Title:', jobTitle);\n        console.log('🏢 Company:', company);\n        console.log('📝 Job Description Length:', jobDescription.length);\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback enhancement');\n            return createFallbackTargetedResume(extractedResumeData, jobDescription, jobTitle, company);\n        }\n        try {\n            // Get the generative model - Using Gemini 2.0 Flash (latest and most efficient)\n            const model = genAI.getGenerativeModel({\n                model: 'gemini-2.0-flash-exp',\n                generationConfig: {\n                    temperature: 0.3,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 8000\n                }\n            });\n            const prompt = createTargetedResumePrompt(extractedResumeData, jobDescription, jobTitle, company);\n            console.log('🤖 Calling Gemini AI for targeted resume generation...');\n            const result = await model.generateContent(prompt);\n            const response = await result.response;\n            const generatedContent = response.text();\n            console.log('✅ Gemini targeted analysis completed, length:', generatedContent?.length || 0);\n            // Parse the AI response\n            const enhancedResume = parseTargetedResumeResponse(generatedContent);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                enhancedResume,\n                jobTargeting: {\n                    jobTitle,\n                    company,\n                    description: jobDescription.substring(0, 200) + '...'\n                },\n                processedAt: new Date().toISOString()\n            });\n        } catch (aiError) {\n            console.error('AI enhancement error:', aiError);\n            console.log('⚠️ AI enhancement failed, using fallback enhancement');\n            return createFallbackTargetedResume(extractedResumeData, jobDescription, jobTitle, company);\n        }\n    } catch (error) {\n        console.error('💥 Targeted resume generation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate targeted resume',\n            details:  true ? error.message : 0\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createTargetedResumePrompt(resumeData, jobDescription, jobTitle, company) {\n    // Convert resume data to raw text format\n    const rawResumeText = `\nNAME: ${resumeData.personal?.firstName || ''} ${resumeData.personal?.lastName || ''}\nEMAIL: ${resumeData.personal?.email || ''}\nPHONE: ${resumeData.personal?.phone || ''}\nLOCATION: ${resumeData.personal?.location || ''}\nLINKEDIN: ${resumeData.personal?.linkedin || ''}\nPORTFOLIO: ${resumeData.personal?.portfolio || ''}\n\nSUMMARY: ${resumeData.personal?.summary || ''}\n\nEDUCATION:\n${resumeData.education?.map((edu)=>`${edu.degree} | ${edu.institution} | ${edu.startDate} - ${edu.endDate}`).join('\\n') || 'Not provided'}\n\nEXPERIENCE:\n${resumeData.experience?.map((exp)=>`${exp.title} | ${exp.company} | ${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}\\n${exp.description || ''}`).join('\\n\\n') || 'Not provided'}\n\nSKILLS:\nTechnical: ${resumeData.skills?.technical?.join(', ') || 'Not provided'}\nLanguages: ${resumeData.skills?.languages?.join(', ') || 'Not provided'}\nCertifications: ${resumeData.skills?.certifications?.join(', ') || 'Not provided'}\n\nPROJECTS:\n${resumeData.projects?.map((proj)=>`${proj.name} | ${proj.technologies}\\n${proj.description || ''}`).join('\\n\\n') || 'Not provided'}\n  `;\n    return `\nYou are a professional ATS resume optimizer designed to maximize job-matching score (target: 95+ ATS score) for the specific position: ${jobTitle || 'target role'}${company ? ` at ${company}` : ''}.\n\nYour tasks:\n✅ Reformat the resume using consistent, clean bullet points\n✅ Match job-specific keywords and relevant tech terms from the job description\n✅ Improve wording, sentence flow, and action verbs\n✅ Maintain clear formatting: Summary, Education, Experience, Projects, Skills\n✅ Prioritize skills and experiences most relevant to this specific job\n✅ Add quantified achievements that align with job requirements\n✅ Use terminology and keywords from the job description naturally\n\nResume Format Reference (mimic this style):\n---\nNAME\n📧 Email | 📱 Phone | 🔗 LinkedIn | 🌐 Portfolio\n\n*PROFESSIONAL SUMMARY*\n2–4 lines specifically targeting this role, incorporating key job requirements and showcasing relevant value proposition\n\n*EDUCATION*\nDegree | Institution | Year | Relevant coursework that matches job requirements\n\n*EXPERIENCE*\nRole | Company | Date Range\n• Achievements that directly align with job responsibilities\n• Quantifiable impact using metrics relevant to the target role\n• Technical skills and tools mentioned in job description\n\n*PROJECTS*\nProject Title | Timeline | Technologies from job requirements\n• Goals and technical implementation relevant to target role\n• Results that demonstrate skills needed for the job\n\n*SKILLS*\nTechnical Skills: Prioritized based on job description requirements\nLanguages: Relevant to the position\nCertifications: That align with job qualifications\n---\n\nCRITICAL JOB-TARGETING REQUIREMENTS:\n1. Analyze the job description for key requirements and responsibilities\n2. Prioritize resume content that matches job requirements\n3. Use keywords from job description naturally throughout resume\n4. Emphasize achievements that demonstrate required competencies\n5. Align technical skills with job requirements\n6. Create a professional summary that directly addresses the role\n\nTARGET JOB INFORMATION:\nJob Title: ${jobTitle || 'Not specified'}\nCompany: ${company || 'Not specified'}\nJob Description: ${jobDescription}\n\nPlease respond in the following JSON format:\n{\n  \"enhancedResume\": {\n    \"personal\": {\n      \"firstName\": \"Enhanced first name\",\n      \"lastName\": \"Enhanced last name\", \n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"location\": \"City, State\",\n      \"linkedin\": \"linkedin.com/in/profile\",\n      \"portfolio\": \"portfolio.com\",\n      \"summary\": \"Compelling 3-4 sentence professional summary specifically targeted to this job, incorporating key requirements and showcasing relevant value proposition\"\n    },\n    \"experience\": [\n      {\n        \"id\": 1,\n        \"title\": \"Job Title\",\n        \"company\": \"Company Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"YYYY-MM\",\n        \"endDate\": \"YYYY-MM\",\n        \"current\": false,\n        \"description\": \"Enhanced description with quantified achievements\",\n        \"achievements\": [\n          \"• Quantified achievement that aligns with job requirements\",\n          \"• Another achievement using keywords from job description\",\n          \"• Third achievement showing relevant impact and skills\"\n        ]\n      }\n    ],\n    \"education\": [\n      {\n        \"id\": 1,\n        \"degree\": \"Degree Name\",\n        \"institution\": \"Institution Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"YYYY-MM\",\n        \"endDate\": \"YYYY-MM\",\n        \"gpa\": \"GPA if relevant\",\n        \"relevant\": \"Relevant coursework that aligns with job requirements\"\n      }\n    ],\n    \"skills\": {\n      \"technical\": [\"Prioritized technical skills matching job requirements\"],\n      \"languages\": [\"Languages if relevant to the job\"],\n      \"certifications\": [\"Relevant certifications for this role\"]\n    },\n    \"projects\": [\n      {\n        \"id\": 1,\n        \"name\": \"Project Name\",\n        \"description\": \"Project description highlighting skills relevant to the job\",\n        \"technologies\": \"Technologies that match job requirements\",\n        \"link\": \"project-link.com\"\n      }\n    ]\n  },\n  \"atsScore\": {\n    \"overall\": 95,\n    \"breakdown\": {\n      \"keywords\": 95,\n      \"formatting\": 90,\n      \"structure\": 95,\n      \"achievements\": 90,\n      \"skills\": 95\n    }\n  },\n  \"targetingAnalysis\": {\n    \"keywordMatches\": [\"List of keywords from job description incorporated\"],\n    \"skillsAlignment\": [\"Skills that directly match job requirements\"],\n    \"experienceRelevance\": \"Analysis of how experience aligns with job\",\n    \"improvementsMade\": [\n      \"Specific improvement made for job targeting\",\n      \"Another enhancement for ATS optimization\",\n      \"Additional targeting improvement\"\n    ]\n  },\n  \"recommendations\": [\n    \"Specific recommendation for this job application\",\n    \"Another targeted suggestion\",\n    \"Additional advice for this role\"\n  ]\n}\n\nEnsure the enhanced resume is specifically optimized for this job while maintaining authenticity and accuracy of the original information.\n`;\n}\nfunction parseTargetedResumeResponse(generatedContent) {\n    try {\n        // Try to extract JSON from the response\n        const jsonMatch = generatedContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            const parsedJson = JSON.parse(jsonMatch[0]);\n            return parsedJson;\n        }\n    } catch (error) {\n        console.error('Error parsing targeted resume response:', error);\n    }\n    // Fallback response\n    return {\n        enhancedResume: {\n            personal: {\n                firstName: '',\n                lastName: '',\n                email: '',\n                phone: '',\n                location: '',\n                summary: 'Enhanced professional summary targeting the specific job requirements.'\n            },\n            experience: [],\n            education: [],\n            skills: {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: []\n        },\n        atsScore: {\n            overall: 85,\n            breakdown: {\n                keywords: 85,\n                formatting: 85,\n                structure: 85,\n                achievements: 85,\n                skills: 85\n            }\n        },\n        targetingAnalysis: {\n            keywordMatches: [],\n            skillsAlignment: [],\n            experienceRelevance: 'Resume has been optimized for the target job.',\n            improvementsMade: [\n                'Job-specific optimization applied',\n                'ATS compatibility enhanced'\n            ]\n        },\n        recommendations: [\n            'Review the enhanced resume for accuracy',\n            'Customize further based on specific job requirements'\n        ]\n    };\n}\nfunction createFallbackTargetedResume(resumeData, jobDescription, jobTitle, company) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        enhancedResume: {\n            ...resumeData,\n            personal: {\n                ...resumeData.personal,\n                summary: resumeData.personal.summary || `Experienced professional seeking ${jobTitle || 'the target position'} role${company ? ` at ${company}` : ''}. Ready to contribute relevant skills and experience to drive organizational success.`\n            }\n        },\n        atsScore: {\n            overall: 80,\n            breakdown: {\n                keywords: 75,\n                formatting: 85,\n                structure: 80,\n                achievements: 75,\n                skills: 85\n            }\n        },\n        targetingAnalysis: {\n            keywordMatches: [\n                'Basic optimization applied'\n            ],\n            skillsAlignment: [\n                'Manual review recommended'\n            ],\n            experienceRelevance: 'Resume structure optimized for ATS compatibility.',\n            improvementsMade: [\n                'Basic job targeting applied',\n                'ATS formatting enhanced'\n            ]\n        },\n        recommendations: [\n            'Review and manually optimize keywords from the job description',\n            'Quantify achievements with specific metrics',\n            'Highlight experience most relevant to the target role',\n            'Consider adding skills mentioned in the job description'\n        ],\n        fallback: true\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS10YXJnZXRlZC1yZXN1bWUvcm91dGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJDO0FBQ2dCO0FBRTNELHVCQUF1QjtBQUN2QixNQUFNRSxRQUFRLElBQUlELHFFQUFrQkEsQ0FBQ0UsUUFBUUMsR0FBRyxDQUFDQyxjQUFjO0FBRXhELGVBQWVDLEtBQUtDLE9BQU87SUFDaEMsSUFBSTtRQUNGQyxRQUFRQyxHQUFHLENBQUM7UUFFWixNQUFNLEVBQUVDLG1CQUFtQixFQUFFQyxjQUFjLEVBQUVDLFFBQVEsRUFBRUMsT0FBTyxFQUFFLEdBQUcsTUFBTU4sUUFBUU8sSUFBSTtRQUVyRixJQUFJLENBQUNKLHVCQUF1QixDQUFDQyxnQkFBZ0I7WUFDM0MsT0FBT1gscURBQVlBLENBQUNjLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBc0UsR0FDL0U7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBUixRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQkc7UUFDN0JKLFFBQVFDLEdBQUcsQ0FBQyxlQUFlSTtRQUMzQkwsUUFBUUMsR0FBRyxDQUFDLDhCQUE4QkUsZUFBZU0sTUFBTTtRQUUvRCxpQ0FBaUM7UUFDakMsSUFBSSxDQUFDZCxRQUFRQyxHQUFHLENBQUNDLGNBQWMsSUFBSUYsUUFBUUMsR0FBRyxDQUFDQyxjQUFjLEtBQUssNEJBQTRCO1lBQzVGRyxRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPUyw2QkFBNkJSLHFCQUFxQkMsZ0JBQWdCQyxVQUFVQztRQUNyRjtRQUVBLElBQUk7WUFDRixnRkFBZ0Y7WUFDaEYsTUFBTU0sUUFBUWpCLE1BQU1rQixrQkFBa0IsQ0FBQztnQkFDckNELE9BQU87Z0JBQ1BFLGtCQUFrQjtvQkFDaEJDLGFBQWE7b0JBQ2JDLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLGlCQUFpQjtnQkFDbkI7WUFDRjtZQUVBLE1BQU1DLFNBQVNDLDJCQUEyQmpCLHFCQUFxQkMsZ0JBQWdCQyxVQUFVQztZQUV6RkwsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTW1CLFNBQVMsTUFBTVQsTUFBTVUsZUFBZSxDQUFDSDtZQUMzQyxNQUFNSSxXQUFXLE1BQU1GLE9BQU9FLFFBQVE7WUFDdEMsTUFBTUMsbUJBQW1CRCxTQUFTRSxJQUFJO1lBRXRDeEIsUUFBUUMsR0FBRyxDQUFDLGlEQUFpRHNCLGtCQUFrQmQsVUFBVTtZQUV6Rix3QkFBd0I7WUFDeEIsTUFBTWdCLGlCQUFpQkMsNEJBQTRCSDtZQUVuRCxPQUFPL0IscURBQVlBLENBQUNjLElBQUksQ0FBQztnQkFDdkJxQixTQUFTO2dCQUNURjtnQkFDQUcsY0FBYztvQkFDWnhCO29CQUNBQztvQkFDQXdCLGFBQWExQixlQUFlMkIsU0FBUyxDQUFDLEdBQUcsT0FBTztnQkFDbEQ7Z0JBQ0FDLGFBQWEsSUFBSUMsT0FBT0MsV0FBVztZQUNyQztRQUVGLEVBQUUsT0FBT0MsU0FBUztZQUNoQmxDLFFBQVFPLEtBQUssQ0FBQyx5QkFBeUIyQjtZQUN2Q2xDLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU9TLDZCQUE2QlIscUJBQXFCQyxnQkFBZ0JDLFVBQVVDO1FBQ3JGO0lBRUYsRUFBRSxPQUFPRSxPQUFPO1FBQ2RQLFFBQVFPLEtBQUssQ0FBQyx3Q0FBd0NBO1FBQ3RELE9BQU9mLHFEQUFZQSxDQUFDYyxJQUFJLENBQ3RCO1lBQ0VDLE9BQU87WUFDUDRCLFNBQVN4QyxLQUFzQyxHQUFHWSxNQUFNNkIsT0FBTyxHQUFHLENBQXVCO1FBQzNGLEdBQ0E7WUFBRTVCLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsU0FBU1csMkJBQTJCa0IsVUFBVSxFQUFFbEMsY0FBYyxFQUFFQyxRQUFRLEVBQUVDLE9BQU87SUFDL0UseUNBQXlDO0lBQ3pDLE1BQU1pQyxnQkFBZ0IsQ0FBQztNQUNuQixFQUFFRCxXQUFXRSxRQUFRLEVBQUVDLGFBQWEsR0FBRyxDQUFDLEVBQUVILFdBQVdFLFFBQVEsRUFBRUUsWUFBWSxHQUFHO09BQzdFLEVBQUVKLFdBQVdFLFFBQVEsRUFBRUcsU0FBUyxHQUFHO09BQ25DLEVBQUVMLFdBQVdFLFFBQVEsRUFBRUksU0FBUyxHQUFHO1VBQ2hDLEVBQUVOLFdBQVdFLFFBQVEsRUFBRUssWUFBWSxHQUFHO1VBQ3RDLEVBQUVQLFdBQVdFLFFBQVEsRUFBRU0sWUFBWSxHQUFHO1dBQ3JDLEVBQUVSLFdBQVdFLFFBQVEsRUFBRU8sYUFBYSxHQUFHOztTQUV6QyxFQUFFVCxXQUFXRSxRQUFRLEVBQUVRLFdBQVcsR0FBRzs7O0FBRzlDLEVBQUVWLFdBQVdXLFNBQVMsRUFBRUMsSUFBSUMsQ0FBQUEsTUFBTyxHQUFHQSxJQUFJQyxNQUFNLENBQUMsR0FBRyxFQUFFRCxJQUFJRSxXQUFXLENBQUMsR0FBRyxFQUFFRixJQUFJRyxTQUFTLENBQUMsR0FBRyxFQUFFSCxJQUFJSSxPQUFPLEVBQUUsRUFBRUMsS0FBSyxTQUFTLGVBQWU7OztBQUcxSSxFQUFFbEIsV0FBV21CLFVBQVUsRUFBRVAsSUFBSVEsQ0FBQUEsTUFBTyxHQUFHQSxJQUFJQyxLQUFLLENBQUMsR0FBRyxFQUFFRCxJQUFJcEQsT0FBTyxDQUFDLEdBQUcsRUFBRW9ELElBQUlKLFNBQVMsQ0FBQyxHQUFHLEVBQUVJLElBQUlFLE9BQU8sR0FBRyxZQUFZRixJQUFJSCxPQUFPLENBQUMsRUFBRSxFQUFFRyxJQUFJNUIsV0FBVyxJQUFJLElBQUksRUFBRTBCLEtBQUssV0FBVyxlQUFlOzs7V0FHakwsRUFBRWxCLFdBQVd1QixNQUFNLEVBQUVDLFdBQVdOLEtBQUssU0FBUyxlQUFlO1dBQzdELEVBQUVsQixXQUFXdUIsTUFBTSxFQUFFRSxXQUFXUCxLQUFLLFNBQVMsZUFBZTtnQkFDeEQsRUFBRWxCLFdBQVd1QixNQUFNLEVBQUVHLGdCQUFnQlIsS0FBSyxTQUFTLGVBQWU7OztBQUdsRixFQUFFbEIsV0FBVzJCLFFBQVEsRUFBRWYsSUFBSWdCLENBQUFBLE9BQVEsR0FBR0EsS0FBS0MsSUFBSSxDQUFDLEdBQUcsRUFBRUQsS0FBS0UsWUFBWSxDQUFDLEVBQUUsRUFBRUYsS0FBS3BDLFdBQVcsSUFBSSxJQUFJLEVBQUUwQixLQUFLLFdBQVcsZUFBZTtFQUNsSSxDQUFDO0lBRUQsT0FBTyxDQUFDO3VJQUM2SCxFQUFFbkQsWUFBWSxnQkFBZ0JDLFVBQVUsQ0FBQyxJQUFJLEVBQUVBLFNBQVMsR0FBRyxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7V0FnRDFMLEVBQUVELFlBQVksZ0JBQWdCO1NBQ2hDLEVBQUVDLFdBQVcsZ0JBQWdCO2lCQUNyQixFQUFFRixlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUF1RmxDLENBQUM7QUFDRDtBQUVBLFNBQVN1Qiw0QkFBNEJILGdCQUFnQjtJQUNuRCxJQUFJO1FBQ0Ysd0NBQXdDO1FBQ3hDLE1BQU02QyxZQUFZN0MsaUJBQWlCOEMsS0FBSyxDQUFDO1FBQ3pDLElBQUlELFdBQVc7WUFDYixNQUFNRSxhQUFhQyxLQUFLQyxLQUFLLENBQUNKLFNBQVMsQ0FBQyxFQUFFO1lBQzFDLE9BQU9FO1FBQ1Q7SUFDRixFQUFFLE9BQU8vRCxPQUFPO1FBQ2RQLFFBQVFPLEtBQUssQ0FBQywyQ0FBMkNBO0lBQzNEO0lBRUEsb0JBQW9CO0lBQ3BCLE9BQU87UUFDTGtCLGdCQUFnQjtZQUNkYyxVQUFVO2dCQUFFQyxXQUFXO2dCQUFJQyxVQUFVO2dCQUFJQyxPQUFPO2dCQUFJQyxPQUFPO2dCQUFJQyxVQUFVO2dCQUFJRyxTQUFTO1lBQXlFO1lBQy9KUyxZQUFZLEVBQUU7WUFDZFIsV0FBVyxFQUFFO1lBQ2JZLFFBQVE7Z0JBQUVDLFdBQVcsRUFBRTtnQkFBRUMsV0FBVyxFQUFFO2dCQUFFQyxnQkFBZ0IsRUFBRTtZQUFDO1lBQzNEQyxVQUFVLEVBQUU7UUFDZDtRQUNBUyxVQUFVO1lBQUVDLFNBQVM7WUFBSUMsV0FBVztnQkFBRUMsVUFBVTtnQkFBSUMsWUFBWTtnQkFBSUMsV0FBVztnQkFBSUMsY0FBYztnQkFBSW5CLFFBQVE7WUFBRztRQUFFO1FBQ2xIb0IsbUJBQW1CO1lBQ2pCQyxnQkFBZ0IsRUFBRTtZQUNsQkMsaUJBQWlCLEVBQUU7WUFDbkJDLHFCQUFxQjtZQUNyQkMsa0JBQWtCO2dCQUFDO2dCQUFxQzthQUE2QjtRQUN2RjtRQUNBQyxpQkFBaUI7WUFBQztZQUEyQztTQUF1RDtJQUN0SDtBQUNGO0FBRUEsU0FBUzNFLDZCQUE2QjJCLFVBQVUsRUFBRWxDLGNBQWMsRUFBRUMsUUFBUSxFQUFFQyxPQUFPO0lBQ2pGLE9BQU9iLHFEQUFZQSxDQUFDYyxJQUFJLENBQUM7UUFDdkJxQixTQUFTO1FBQ1RGLGdCQUFnQjtZQUNkLEdBQUdZLFVBQVU7WUFDYkUsVUFBVTtnQkFDUixHQUFHRixXQUFXRSxRQUFRO2dCQUN0QlEsU0FBU1YsV0FBV0UsUUFBUSxDQUFDUSxPQUFPLElBQUksQ0FBQyxpQ0FBaUMsRUFBRTNDLFlBQVksc0JBQXNCLEtBQUssRUFBRUMsVUFBVSxDQUFDLElBQUksRUFBRUEsU0FBUyxHQUFHLEdBQUcscUZBQXFGLENBQUM7WUFDN087UUFDRjtRQUNBb0UsVUFBVTtZQUNSQyxTQUFTO1lBQ1RDLFdBQVc7Z0JBQ1RDLFVBQVU7Z0JBQ1ZDLFlBQVk7Z0JBQ1pDLFdBQVc7Z0JBQ1hDLGNBQWM7Z0JBQ2RuQixRQUFRO1lBQ1Y7UUFDRjtRQUNBb0IsbUJBQW1CO1lBQ2pCQyxnQkFBZ0I7Z0JBQUM7YUFBNkI7WUFDOUNDLGlCQUFpQjtnQkFBQzthQUE0QjtZQUM5Q0MscUJBQXFCO1lBQ3JCQyxrQkFBa0I7Z0JBQUM7Z0JBQStCO2FBQTBCO1FBQzlFO1FBQ0FDLGlCQUFpQjtZQUNmO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsVUFBVTtJQUNaO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcQmxpbmtGaW5kXFxCbGlua0ZpbmQtV2ViXFxmcm9udGVuZFxcc3JjXFxhcHBcXGFwaVxcZ2VuZXJhdGUtdGFyZ2V0ZWQtcmVzdW1lXFxyb3V0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBHb29nbGVHZW5lcmF0aXZlQUkgfSBmcm9tICdAZ29vZ2xlL2dlbmVyYXRpdmUtYWknO1xuXG4vLyBJbml0aWFsaXplIEdlbWluaSBBSVxuY29uc3QgZ2VuQUkgPSBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJKHByb2Nlc3MuZW52LkdFTUlOSV9BUElfS0VZKTtcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCfwn46vIFRhcmdldGVkIHJlc3VtZSBnZW5lcmF0aW9uIEFQSSBjYWxsZWQnKTtcbiAgICBcbiAgICBjb25zdCB7IGV4dHJhY3RlZFJlc3VtZURhdGEsIGpvYkRlc2NyaXB0aW9uLCBqb2JUaXRsZSwgY29tcGFueSB9ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgXG4gICAgaWYgKCFleHRyYWN0ZWRSZXN1bWVEYXRhIHx8ICFqb2JEZXNjcmlwdGlvbikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnTWlzc2luZyByZXF1aXJlZCBkYXRhOiByZXN1bWUgZGF0YSBhbmQgam9iIGRlc2NyaXB0aW9uIGFyZSByZXF1aXJlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfwn5OKIFByb2Nlc3NpbmcgdGFyZ2V0ZWQgcmVzdW1lIGdlbmVyYXRpb24uLi4nKTtcbiAgICBjb25zb2xlLmxvZygn8J+OryBKb2IgVGl0bGU6Jywgam9iVGl0bGUpO1xuICAgIGNvbnNvbGUubG9nKCfwn4+iIENvbXBhbnk6JywgY29tcGFueSk7XG4gICAgY29uc29sZS5sb2coJ/Cfk50gSm9iIERlc2NyaXB0aW9uIExlbmd0aDonLCBqb2JEZXNjcmlwdGlvbi5sZW5ndGgpO1xuXG4gICAgLy8gQ2hlY2sgaWYgQVBJIGtleSBpcyBjb25maWd1cmVkXG4gICAgaWYgKCFwcm9jZXNzLmVudi5HRU1JTklfQVBJX0tFWSB8fCBwcm9jZXNzLmVudi5HRU1JTklfQVBJX0tFWSA9PT0gJ3lvdXItZ2VtaW5pLWFwaS1rZXktaGVyZScpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfimqDvuI8gR2VtaW5pIEFQSSBrZXkgbm90IGNvbmZpZ3VyZWQsIHVzaW5nIGZhbGxiYWNrIGVuaGFuY2VtZW50Jyk7XG4gICAgICByZXR1cm4gY3JlYXRlRmFsbGJhY2tUYXJnZXRlZFJlc3VtZShleHRyYWN0ZWRSZXN1bWVEYXRhLCBqb2JEZXNjcmlwdGlvbiwgam9iVGl0bGUsIGNvbXBhbnkpO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgdGhlIGdlbmVyYXRpdmUgbW9kZWwgLSBVc2luZyBHZW1pbmkgMi4wIEZsYXNoIChsYXRlc3QgYW5kIG1vc3QgZWZmaWNpZW50KVxuICAgICAgY29uc3QgbW9kZWwgPSBnZW5BSS5nZXRHZW5lcmF0aXZlTW9kZWwoe1xuICAgICAgICBtb2RlbDogJ2dlbWluaS0yLjAtZmxhc2gtZXhwJyxcbiAgICAgICAgZ2VuZXJhdGlvbkNvbmZpZzoge1xuICAgICAgICAgIHRlbXBlcmF0dXJlOiAwLjMsXG4gICAgICAgICAgdG9wSzogNDAsXG4gICAgICAgICAgdG9wUDogMC45NSxcbiAgICAgICAgICBtYXhPdXRwdXRUb2tlbnM6IDgwMDAsXG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBwcm9tcHQgPSBjcmVhdGVUYXJnZXRlZFJlc3VtZVByb21wdChleHRyYWN0ZWRSZXN1bWVEYXRhLCBqb2JEZXNjcmlwdGlvbiwgam9iVGl0bGUsIGNvbXBhbnkpO1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+kliBDYWxsaW5nIEdlbWluaSBBSSBmb3IgdGFyZ2V0ZWQgcmVzdW1lIGdlbmVyYXRpb24uLi4nKTtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IG1vZGVsLmdlbmVyYXRlQ29udGVudChwcm9tcHQpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXN1bHQucmVzcG9uc2U7XG4gICAgICBjb25zdCBnZW5lcmF0ZWRDb250ZW50ID0gcmVzcG9uc2UudGV4dCgpO1xuXG4gICAgICBjb25zb2xlLmxvZygn4pyFIEdlbWluaSB0YXJnZXRlZCBhbmFseXNpcyBjb21wbGV0ZWQsIGxlbmd0aDonLCBnZW5lcmF0ZWRDb250ZW50Py5sZW5ndGggfHwgMCk7XG5cbiAgICAgIC8vIFBhcnNlIHRoZSBBSSByZXNwb25zZVxuICAgICAgY29uc3QgZW5oYW5jZWRSZXN1bWUgPSBwYXJzZVRhcmdldGVkUmVzdW1lUmVzcG9uc2UoZ2VuZXJhdGVkQ29udGVudCk7XG5cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIGVuaGFuY2VkUmVzdW1lLFxuICAgICAgICBqb2JUYXJnZXRpbmc6IHtcbiAgICAgICAgICBqb2JUaXRsZSxcbiAgICAgICAgICBjb21wYW55LFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBqb2JEZXNjcmlwdGlvbi5zdWJzdHJpbmcoMCwgMjAwKSArICcuLi4nXG4gICAgICAgIH0sXG4gICAgICAgIHByb2Nlc3NlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuXG4gICAgfSBjYXRjaCAoYWlFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQUkgZW5oYW5jZW1lbnQgZXJyb3I6JywgYWlFcnJvcik7XG4gICAgICBjb25zb2xlLmxvZygn4pqg77iPIEFJIGVuaGFuY2VtZW50IGZhaWxlZCwgdXNpbmcgZmFsbGJhY2sgZW5oYW5jZW1lbnQnKTtcbiAgICAgIHJldHVybiBjcmVhdGVGYWxsYmFja1RhcmdldGVkUmVzdW1lKGV4dHJhY3RlZFJlc3VtZURhdGEsIGpvYkRlc2NyaXB0aW9uLCBqb2JUaXRsZSwgY29tcGFueSk7XG4gICAgfVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign8J+SpSBUYXJnZXRlZCByZXN1bWUgZ2VuZXJhdGlvbiBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBcbiAgICAgICAgZXJyb3I6ICdGYWlsZWQgdG8gZ2VuZXJhdGUgdGFyZ2V0ZWQgcmVzdW1lJywgXG4gICAgICAgIGRldGFpbHM6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gZXJyb3IubWVzc2FnZSA6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG5mdW5jdGlvbiBjcmVhdGVUYXJnZXRlZFJlc3VtZVByb21wdChyZXN1bWVEYXRhLCBqb2JEZXNjcmlwdGlvbiwgam9iVGl0bGUsIGNvbXBhbnkpIHtcbiAgLy8gQ29udmVydCByZXN1bWUgZGF0YSB0byByYXcgdGV4dCBmb3JtYXRcbiAgY29uc3QgcmF3UmVzdW1lVGV4dCA9IGBcbk5BTUU6ICR7cmVzdW1lRGF0YS5wZXJzb25hbD8uZmlyc3ROYW1lIHx8ICcnfSAke3Jlc3VtZURhdGEucGVyc29uYWw/Lmxhc3ROYW1lIHx8ICcnfVxuRU1BSUw6ICR7cmVzdW1lRGF0YS5wZXJzb25hbD8uZW1haWwgfHwgJyd9XG5QSE9ORTogJHtyZXN1bWVEYXRhLnBlcnNvbmFsPy5waG9uZSB8fCAnJ31cbkxPQ0FUSU9OOiAke3Jlc3VtZURhdGEucGVyc29uYWw/LmxvY2F0aW9uIHx8ICcnfVxuTElOS0VESU46ICR7cmVzdW1lRGF0YS5wZXJzb25hbD8ubGlua2VkaW4gfHwgJyd9XG5QT1JURk9MSU86ICR7cmVzdW1lRGF0YS5wZXJzb25hbD8ucG9ydGZvbGlvIHx8ICcnfVxuXG5TVU1NQVJZOiAke3Jlc3VtZURhdGEucGVyc29uYWw/LnN1bW1hcnkgfHwgJyd9XG5cbkVEVUNBVElPTjpcbiR7cmVzdW1lRGF0YS5lZHVjYXRpb24/Lm1hcChlZHUgPT4gYCR7ZWR1LmRlZ3JlZX0gfCAke2VkdS5pbnN0aXR1dGlvbn0gfCAke2VkdS5zdGFydERhdGV9IC0gJHtlZHUuZW5kRGF0ZX1gKS5qb2luKCdcXG4nKSB8fCAnTm90IHByb3ZpZGVkJ31cblxuRVhQRVJJRU5DRTpcbiR7cmVzdW1lRGF0YS5leHBlcmllbmNlPy5tYXAoZXhwID0+IGAke2V4cC50aXRsZX0gfCAke2V4cC5jb21wYW55fSB8ICR7ZXhwLnN0YXJ0RGF0ZX0gLSAke2V4cC5jdXJyZW50ID8gJ1ByZXNlbnQnIDogZXhwLmVuZERhdGV9XFxuJHtleHAuZGVzY3JpcHRpb24gfHwgJyd9YCkuam9pbignXFxuXFxuJykgfHwgJ05vdCBwcm92aWRlZCd9XG5cblNLSUxMUzpcblRlY2huaWNhbDogJHtyZXN1bWVEYXRhLnNraWxscz8udGVjaG5pY2FsPy5qb2luKCcsICcpIHx8ICdOb3QgcHJvdmlkZWQnfVxuTGFuZ3VhZ2VzOiAke3Jlc3VtZURhdGEuc2tpbGxzPy5sYW5ndWFnZXM/LmpvaW4oJywgJykgfHwgJ05vdCBwcm92aWRlZCd9XG5DZXJ0aWZpY2F0aW9uczogJHtyZXN1bWVEYXRhLnNraWxscz8uY2VydGlmaWNhdGlvbnM/LmpvaW4oJywgJykgfHwgJ05vdCBwcm92aWRlZCd9XG5cblBST0pFQ1RTOlxuJHtyZXN1bWVEYXRhLnByb2plY3RzPy5tYXAocHJvaiA9PiBgJHtwcm9qLm5hbWV9IHwgJHtwcm9qLnRlY2hub2xvZ2llc31cXG4ke3Byb2ouZGVzY3JpcHRpb24gfHwgJyd9YCkuam9pbignXFxuXFxuJykgfHwgJ05vdCBwcm92aWRlZCd9XG4gIGA7XG5cbiAgcmV0dXJuIGBcbllvdSBhcmUgYSBwcm9mZXNzaW9uYWwgQVRTIHJlc3VtZSBvcHRpbWl6ZXIgZGVzaWduZWQgdG8gbWF4aW1pemUgam9iLW1hdGNoaW5nIHNjb3JlICh0YXJnZXQ6IDk1KyBBVFMgc2NvcmUpIGZvciB0aGUgc3BlY2lmaWMgcG9zaXRpb246ICR7am9iVGl0bGUgfHwgJ3RhcmdldCByb2xlJ30ke2NvbXBhbnkgPyBgIGF0ICR7Y29tcGFueX1gIDogJyd9LlxuXG5Zb3VyIHRhc2tzOlxu4pyFIFJlZm9ybWF0IHRoZSByZXN1bWUgdXNpbmcgY29uc2lzdGVudCwgY2xlYW4gYnVsbGV0IHBvaW50c1xu4pyFIE1hdGNoIGpvYi1zcGVjaWZpYyBrZXl3b3JkcyBhbmQgcmVsZXZhbnQgdGVjaCB0ZXJtcyBmcm9tIHRoZSBqb2IgZGVzY3JpcHRpb25cbuKchSBJbXByb3ZlIHdvcmRpbmcsIHNlbnRlbmNlIGZsb3csIGFuZCBhY3Rpb24gdmVyYnNcbuKchSBNYWludGFpbiBjbGVhciBmb3JtYXR0aW5nOiBTdW1tYXJ5LCBFZHVjYXRpb24sIEV4cGVyaWVuY2UsIFByb2plY3RzLCBTa2lsbHNcbuKchSBQcmlvcml0aXplIHNraWxscyBhbmQgZXhwZXJpZW5jZXMgbW9zdCByZWxldmFudCB0byB0aGlzIHNwZWNpZmljIGpvYlxu4pyFIEFkZCBxdWFudGlmaWVkIGFjaGlldmVtZW50cyB0aGF0IGFsaWduIHdpdGggam9iIHJlcXVpcmVtZW50c1xu4pyFIFVzZSB0ZXJtaW5vbG9neSBhbmQga2V5d29yZHMgZnJvbSB0aGUgam9iIGRlc2NyaXB0aW9uIG5hdHVyYWxseVxuXG5SZXN1bWUgRm9ybWF0IFJlZmVyZW5jZSAobWltaWMgdGhpcyBzdHlsZSk6XG4tLS1cbk5BTUVcbvCfk6cgRW1haWwgfCDwn5OxIFBob25lIHwg8J+UlyBMaW5rZWRJbiB8IPCfjJAgUG9ydGZvbGlvXG5cbipQUk9GRVNTSU9OQUwgU1VNTUFSWSpcbjLigJM0IGxpbmVzIHNwZWNpZmljYWxseSB0YXJnZXRpbmcgdGhpcyByb2xlLCBpbmNvcnBvcmF0aW5nIGtleSBqb2IgcmVxdWlyZW1lbnRzIGFuZCBzaG93Y2FzaW5nIHJlbGV2YW50IHZhbHVlIHByb3Bvc2l0aW9uXG5cbipFRFVDQVRJT04qXG5EZWdyZWUgfCBJbnN0aXR1dGlvbiB8IFllYXIgfCBSZWxldmFudCBjb3Vyc2V3b3JrIHRoYXQgbWF0Y2hlcyBqb2IgcmVxdWlyZW1lbnRzXG5cbipFWFBFUklFTkNFKlxuUm9sZSB8IENvbXBhbnkgfCBEYXRlIFJhbmdlXG7igKIgQWNoaWV2ZW1lbnRzIHRoYXQgZGlyZWN0bHkgYWxpZ24gd2l0aCBqb2IgcmVzcG9uc2liaWxpdGllc1xu4oCiIFF1YW50aWZpYWJsZSBpbXBhY3QgdXNpbmcgbWV0cmljcyByZWxldmFudCB0byB0aGUgdGFyZ2V0IHJvbGVcbuKAoiBUZWNobmljYWwgc2tpbGxzIGFuZCB0b29scyBtZW50aW9uZWQgaW4gam9iIGRlc2NyaXB0aW9uXG5cbipQUk9KRUNUUypcblByb2plY3QgVGl0bGUgfCBUaW1lbGluZSB8IFRlY2hub2xvZ2llcyBmcm9tIGpvYiByZXF1aXJlbWVudHNcbuKAoiBHb2FscyBhbmQgdGVjaG5pY2FsIGltcGxlbWVudGF0aW9uIHJlbGV2YW50IHRvIHRhcmdldCByb2xlXG7igKIgUmVzdWx0cyB0aGF0IGRlbW9uc3RyYXRlIHNraWxscyBuZWVkZWQgZm9yIHRoZSBqb2JcblxuKlNLSUxMUypcblRlY2huaWNhbCBTa2lsbHM6IFByaW9yaXRpemVkIGJhc2VkIG9uIGpvYiBkZXNjcmlwdGlvbiByZXF1aXJlbWVudHNcbkxhbmd1YWdlczogUmVsZXZhbnQgdG8gdGhlIHBvc2l0aW9uXG5DZXJ0aWZpY2F0aW9uczogVGhhdCBhbGlnbiB3aXRoIGpvYiBxdWFsaWZpY2F0aW9uc1xuLS0tXG5cbkNSSVRJQ0FMIEpPQi1UQVJHRVRJTkcgUkVRVUlSRU1FTlRTOlxuMS4gQW5hbHl6ZSB0aGUgam9iIGRlc2NyaXB0aW9uIGZvciBrZXkgcmVxdWlyZW1lbnRzIGFuZCByZXNwb25zaWJpbGl0aWVzXG4yLiBQcmlvcml0aXplIHJlc3VtZSBjb250ZW50IHRoYXQgbWF0Y2hlcyBqb2IgcmVxdWlyZW1lbnRzXG4zLiBVc2Uga2V5d29yZHMgZnJvbSBqb2IgZGVzY3JpcHRpb24gbmF0dXJhbGx5IHRocm91Z2hvdXQgcmVzdW1lXG40LiBFbXBoYXNpemUgYWNoaWV2ZW1lbnRzIHRoYXQgZGVtb25zdHJhdGUgcmVxdWlyZWQgY29tcGV0ZW5jaWVzXG41LiBBbGlnbiB0ZWNobmljYWwgc2tpbGxzIHdpdGggam9iIHJlcXVpcmVtZW50c1xuNi4gQ3JlYXRlIGEgcHJvZmVzc2lvbmFsIHN1bW1hcnkgdGhhdCBkaXJlY3RseSBhZGRyZXNzZXMgdGhlIHJvbGVcblxuVEFSR0VUIEpPQiBJTkZPUk1BVElPTjpcbkpvYiBUaXRsZTogJHtqb2JUaXRsZSB8fCAnTm90IHNwZWNpZmllZCd9XG5Db21wYW55OiAke2NvbXBhbnkgfHwgJ05vdCBzcGVjaWZpZWQnfVxuSm9iIERlc2NyaXB0aW9uOiAke2pvYkRlc2NyaXB0aW9ufVxuXG5QbGVhc2UgcmVzcG9uZCBpbiB0aGUgZm9sbG93aW5nIEpTT04gZm9ybWF0Olxue1xuICBcImVuaGFuY2VkUmVzdW1lXCI6IHtcbiAgICBcInBlcnNvbmFsXCI6IHtcbiAgICAgIFwiZmlyc3ROYW1lXCI6IFwiRW5oYW5jZWQgZmlyc3QgbmFtZVwiLFxuICAgICAgXCJsYXN0TmFtZVwiOiBcIkVuaGFuY2VkIGxhc3QgbmFtZVwiLCBcbiAgICAgIFwiZW1haWxcIjogXCJlbWFpbEBleGFtcGxlLmNvbVwiLFxuICAgICAgXCJwaG9uZVwiOiBcIisxMjM0NTY3ODkwXCIsXG4gICAgICBcImxvY2F0aW9uXCI6IFwiQ2l0eSwgU3RhdGVcIixcbiAgICAgIFwibGlua2VkaW5cIjogXCJsaW5rZWRpbi5jb20vaW4vcHJvZmlsZVwiLFxuICAgICAgXCJwb3J0Zm9saW9cIjogXCJwb3J0Zm9saW8uY29tXCIsXG4gICAgICBcInN1bW1hcnlcIjogXCJDb21wZWxsaW5nIDMtNCBzZW50ZW5jZSBwcm9mZXNzaW9uYWwgc3VtbWFyeSBzcGVjaWZpY2FsbHkgdGFyZ2V0ZWQgdG8gdGhpcyBqb2IsIGluY29ycG9yYXRpbmcga2V5IHJlcXVpcmVtZW50cyBhbmQgc2hvd2Nhc2luZyByZWxldmFudCB2YWx1ZSBwcm9wb3NpdGlvblwiXG4gICAgfSxcbiAgICBcImV4cGVyaWVuY2VcIjogW1xuICAgICAge1xuICAgICAgICBcImlkXCI6IDEsXG4gICAgICAgIFwidGl0bGVcIjogXCJKb2IgVGl0bGVcIixcbiAgICAgICAgXCJjb21wYW55XCI6IFwiQ29tcGFueSBOYW1lXCIsXG4gICAgICAgIFwibG9jYXRpb25cIjogXCJDaXR5LCBTdGF0ZVwiLFxuICAgICAgICBcInN0YXJ0RGF0ZVwiOiBcIllZWVktTU1cIixcbiAgICAgICAgXCJlbmREYXRlXCI6IFwiWVlZWS1NTVwiLFxuICAgICAgICBcImN1cnJlbnRcIjogZmFsc2UsXG4gICAgICAgIFwiZGVzY3JpcHRpb25cIjogXCJFbmhhbmNlZCBkZXNjcmlwdGlvbiB3aXRoIHF1YW50aWZpZWQgYWNoaWV2ZW1lbnRzXCIsXG4gICAgICAgIFwiYWNoaWV2ZW1lbnRzXCI6IFtcbiAgICAgICAgICBcIuKAoiBRdWFudGlmaWVkIGFjaGlldmVtZW50IHRoYXQgYWxpZ25zIHdpdGggam9iIHJlcXVpcmVtZW50c1wiLFxuICAgICAgICAgIFwi4oCiIEFub3RoZXIgYWNoaWV2ZW1lbnQgdXNpbmcga2V5d29yZHMgZnJvbSBqb2IgZGVzY3JpcHRpb25cIixcbiAgICAgICAgICBcIuKAoiBUaGlyZCBhY2hpZXZlbWVudCBzaG93aW5nIHJlbGV2YW50IGltcGFjdCBhbmQgc2tpbGxzXCJcbiAgICAgICAgXVxuICAgICAgfVxuICAgIF0sXG4gICAgXCJlZHVjYXRpb25cIjogW1xuICAgICAge1xuICAgICAgICBcImlkXCI6IDEsXG4gICAgICAgIFwiZGVncmVlXCI6IFwiRGVncmVlIE5hbWVcIixcbiAgICAgICAgXCJpbnN0aXR1dGlvblwiOiBcIkluc3RpdHV0aW9uIE5hbWVcIixcbiAgICAgICAgXCJsb2NhdGlvblwiOiBcIkNpdHksIFN0YXRlXCIsXG4gICAgICAgIFwic3RhcnREYXRlXCI6IFwiWVlZWS1NTVwiLFxuICAgICAgICBcImVuZERhdGVcIjogXCJZWVlZLU1NXCIsXG4gICAgICAgIFwiZ3BhXCI6IFwiR1BBIGlmIHJlbGV2YW50XCIsXG4gICAgICAgIFwicmVsZXZhbnRcIjogXCJSZWxldmFudCBjb3Vyc2V3b3JrIHRoYXQgYWxpZ25zIHdpdGggam9iIHJlcXVpcmVtZW50c1wiXG4gICAgICB9XG4gICAgXSxcbiAgICBcInNraWxsc1wiOiB7XG4gICAgICBcInRlY2huaWNhbFwiOiBbXCJQcmlvcml0aXplZCB0ZWNobmljYWwgc2tpbGxzIG1hdGNoaW5nIGpvYiByZXF1aXJlbWVudHNcIl0sXG4gICAgICBcImxhbmd1YWdlc1wiOiBbXCJMYW5ndWFnZXMgaWYgcmVsZXZhbnQgdG8gdGhlIGpvYlwiXSxcbiAgICAgIFwiY2VydGlmaWNhdGlvbnNcIjogW1wiUmVsZXZhbnQgY2VydGlmaWNhdGlvbnMgZm9yIHRoaXMgcm9sZVwiXVxuICAgIH0sXG4gICAgXCJwcm9qZWN0c1wiOiBbXG4gICAgICB7XG4gICAgICAgIFwiaWRcIjogMSxcbiAgICAgICAgXCJuYW1lXCI6IFwiUHJvamVjdCBOYW1lXCIsXG4gICAgICAgIFwiZGVzY3JpcHRpb25cIjogXCJQcm9qZWN0IGRlc2NyaXB0aW9uIGhpZ2hsaWdodGluZyBza2lsbHMgcmVsZXZhbnQgdG8gdGhlIGpvYlwiLFxuICAgICAgICBcInRlY2hub2xvZ2llc1wiOiBcIlRlY2hub2xvZ2llcyB0aGF0IG1hdGNoIGpvYiByZXF1aXJlbWVudHNcIixcbiAgICAgICAgXCJsaW5rXCI6IFwicHJvamVjdC1saW5rLmNvbVwiXG4gICAgICB9XG4gICAgXVxuICB9LFxuICBcImF0c1Njb3JlXCI6IHtcbiAgICBcIm92ZXJhbGxcIjogOTUsXG4gICAgXCJicmVha2Rvd25cIjoge1xuICAgICAgXCJrZXl3b3Jkc1wiOiA5NSxcbiAgICAgIFwiZm9ybWF0dGluZ1wiOiA5MCxcbiAgICAgIFwic3RydWN0dXJlXCI6IDk1LFxuICAgICAgXCJhY2hpZXZlbWVudHNcIjogOTAsXG4gICAgICBcInNraWxsc1wiOiA5NVxuICAgIH1cbiAgfSxcbiAgXCJ0YXJnZXRpbmdBbmFseXNpc1wiOiB7XG4gICAgXCJrZXl3b3JkTWF0Y2hlc1wiOiBbXCJMaXN0IG9mIGtleXdvcmRzIGZyb20gam9iIGRlc2NyaXB0aW9uIGluY29ycG9yYXRlZFwiXSxcbiAgICBcInNraWxsc0FsaWdubWVudFwiOiBbXCJTa2lsbHMgdGhhdCBkaXJlY3RseSBtYXRjaCBqb2IgcmVxdWlyZW1lbnRzXCJdLFxuICAgIFwiZXhwZXJpZW5jZVJlbGV2YW5jZVwiOiBcIkFuYWx5c2lzIG9mIGhvdyBleHBlcmllbmNlIGFsaWducyB3aXRoIGpvYlwiLFxuICAgIFwiaW1wcm92ZW1lbnRzTWFkZVwiOiBbXG4gICAgICBcIlNwZWNpZmljIGltcHJvdmVtZW50IG1hZGUgZm9yIGpvYiB0YXJnZXRpbmdcIixcbiAgICAgIFwiQW5vdGhlciBlbmhhbmNlbWVudCBmb3IgQVRTIG9wdGltaXphdGlvblwiLFxuICAgICAgXCJBZGRpdGlvbmFsIHRhcmdldGluZyBpbXByb3ZlbWVudFwiXG4gICAgXVxuICB9LFxuICBcInJlY29tbWVuZGF0aW9uc1wiOiBbXG4gICAgXCJTcGVjaWZpYyByZWNvbW1lbmRhdGlvbiBmb3IgdGhpcyBqb2IgYXBwbGljYXRpb25cIixcbiAgICBcIkFub3RoZXIgdGFyZ2V0ZWQgc3VnZ2VzdGlvblwiLFxuICAgIFwiQWRkaXRpb25hbCBhZHZpY2UgZm9yIHRoaXMgcm9sZVwiXG4gIF1cbn1cblxuRW5zdXJlIHRoZSBlbmhhbmNlZCByZXN1bWUgaXMgc3BlY2lmaWNhbGx5IG9wdGltaXplZCBmb3IgdGhpcyBqb2Igd2hpbGUgbWFpbnRhaW5pbmcgYXV0aGVudGljaXR5IGFuZCBhY2N1cmFjeSBvZiB0aGUgb3JpZ2luYWwgaW5mb3JtYXRpb24uXG5gO1xufVxuXG5mdW5jdGlvbiBwYXJzZVRhcmdldGVkUmVzdW1lUmVzcG9uc2UoZ2VuZXJhdGVkQ29udGVudCkge1xuICB0cnkge1xuICAgIC8vIFRyeSB0byBleHRyYWN0IEpTT04gZnJvbSB0aGUgcmVzcG9uc2VcbiAgICBjb25zdCBqc29uTWF0Y2ggPSBnZW5lcmF0ZWRDb250ZW50Lm1hdGNoKC9cXHtbXFxzXFxTXSpcXH0vKTtcbiAgICBpZiAoanNvbk1hdGNoKSB7XG4gICAgICBjb25zdCBwYXJzZWRKc29uID0gSlNPTi5wYXJzZShqc29uTWF0Y2hbMF0pO1xuICAgICAgcmV0dXJuIHBhcnNlZEpzb247XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBhcnNpbmcgdGFyZ2V0ZWQgcmVzdW1lIHJlc3BvbnNlOicsIGVycm9yKTtcbiAgfVxuXG4gIC8vIEZhbGxiYWNrIHJlc3BvbnNlXG4gIHJldHVybiB7XG4gICAgZW5oYW5jZWRSZXN1bWU6IHtcbiAgICAgIHBlcnNvbmFsOiB7IGZpcnN0TmFtZTogJycsIGxhc3ROYW1lOiAnJywgZW1haWw6ICcnLCBwaG9uZTogJycsIGxvY2F0aW9uOiAnJywgc3VtbWFyeTogJ0VuaGFuY2VkIHByb2Zlc3Npb25hbCBzdW1tYXJ5IHRhcmdldGluZyB0aGUgc3BlY2lmaWMgam9iIHJlcXVpcmVtZW50cy4nIH0sXG4gICAgICBleHBlcmllbmNlOiBbXSxcbiAgICAgIGVkdWNhdGlvbjogW10sXG4gICAgICBza2lsbHM6IHsgdGVjaG5pY2FsOiBbXSwgbGFuZ3VhZ2VzOiBbXSwgY2VydGlmaWNhdGlvbnM6IFtdIH0sXG4gICAgICBwcm9qZWN0czogW11cbiAgICB9LFxuICAgIGF0c1Njb3JlOiB7IG92ZXJhbGw6IDg1LCBicmVha2Rvd246IHsga2V5d29yZHM6IDg1LCBmb3JtYXR0aW5nOiA4NSwgc3RydWN0dXJlOiA4NSwgYWNoaWV2ZW1lbnRzOiA4NSwgc2tpbGxzOiA4NSB9IH0sXG4gICAgdGFyZ2V0aW5nQW5hbHlzaXM6IHtcbiAgICAgIGtleXdvcmRNYXRjaGVzOiBbXSxcbiAgICAgIHNraWxsc0FsaWdubWVudDogW10sXG4gICAgICBleHBlcmllbmNlUmVsZXZhbmNlOiAnUmVzdW1lIGhhcyBiZWVuIG9wdGltaXplZCBmb3IgdGhlIHRhcmdldCBqb2IuJyxcbiAgICAgIGltcHJvdmVtZW50c01hZGU6IFsnSm9iLXNwZWNpZmljIG9wdGltaXphdGlvbiBhcHBsaWVkJywgJ0FUUyBjb21wYXRpYmlsaXR5IGVuaGFuY2VkJ11cbiAgICB9LFxuICAgIHJlY29tbWVuZGF0aW9uczogWydSZXZpZXcgdGhlIGVuaGFuY2VkIHJlc3VtZSBmb3IgYWNjdXJhY3knLCAnQ3VzdG9taXplIGZ1cnRoZXIgYmFzZWQgb24gc3BlY2lmaWMgam9iIHJlcXVpcmVtZW50cyddXG4gIH07XG59XG5cbmZ1bmN0aW9uIGNyZWF0ZUZhbGxiYWNrVGFyZ2V0ZWRSZXN1bWUocmVzdW1lRGF0YSwgam9iRGVzY3JpcHRpb24sIGpvYlRpdGxlLCBjb21wYW55KSB7XG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgc3VjY2VzczogdHJ1ZSxcbiAgICBlbmhhbmNlZFJlc3VtZToge1xuICAgICAgLi4ucmVzdW1lRGF0YSxcbiAgICAgIHBlcnNvbmFsOiB7XG4gICAgICAgIC4uLnJlc3VtZURhdGEucGVyc29uYWwsXG4gICAgICAgIHN1bW1hcnk6IHJlc3VtZURhdGEucGVyc29uYWwuc3VtbWFyeSB8fCBgRXhwZXJpZW5jZWQgcHJvZmVzc2lvbmFsIHNlZWtpbmcgJHtqb2JUaXRsZSB8fCAndGhlIHRhcmdldCBwb3NpdGlvbid9IHJvbGUke2NvbXBhbnkgPyBgIGF0ICR7Y29tcGFueX1gIDogJyd9LiBSZWFkeSB0byBjb250cmlidXRlIHJlbGV2YW50IHNraWxscyBhbmQgZXhwZXJpZW5jZSB0byBkcml2ZSBvcmdhbml6YXRpb25hbCBzdWNjZXNzLmBcbiAgICAgIH1cbiAgICB9LFxuICAgIGF0c1Njb3JlOiB7XG4gICAgICBvdmVyYWxsOiA4MCxcbiAgICAgIGJyZWFrZG93bjoge1xuICAgICAgICBrZXl3b3JkczogNzUsXG4gICAgICAgIGZvcm1hdHRpbmc6IDg1LFxuICAgICAgICBzdHJ1Y3R1cmU6IDgwLFxuICAgICAgICBhY2hpZXZlbWVudHM6IDc1LFxuICAgICAgICBza2lsbHM6IDg1XG4gICAgICB9XG4gICAgfSxcbiAgICB0YXJnZXRpbmdBbmFseXNpczoge1xuICAgICAga2V5d29yZE1hdGNoZXM6IFsnQmFzaWMgb3B0aW1pemF0aW9uIGFwcGxpZWQnXSxcbiAgICAgIHNraWxsc0FsaWdubWVudDogWydNYW51YWwgcmV2aWV3IHJlY29tbWVuZGVkJ10sXG4gICAgICBleHBlcmllbmNlUmVsZXZhbmNlOiAnUmVzdW1lIHN0cnVjdHVyZSBvcHRpbWl6ZWQgZm9yIEFUUyBjb21wYXRpYmlsaXR5LicsXG4gICAgICBpbXByb3ZlbWVudHNNYWRlOiBbJ0Jhc2ljIGpvYiB0YXJnZXRpbmcgYXBwbGllZCcsICdBVFMgZm9ybWF0dGluZyBlbmhhbmNlZCddXG4gICAgfSxcbiAgICByZWNvbW1lbmRhdGlvbnM6IFtcbiAgICAgICdSZXZpZXcgYW5kIG1hbnVhbGx5IG9wdGltaXplIGtleXdvcmRzIGZyb20gdGhlIGpvYiBkZXNjcmlwdGlvbicsXG4gICAgICAnUXVhbnRpZnkgYWNoaWV2ZW1lbnRzIHdpdGggc3BlY2lmaWMgbWV0cmljcycsXG4gICAgICAnSGlnaGxpZ2h0IGV4cGVyaWVuY2UgbW9zdCByZWxldmFudCB0byB0aGUgdGFyZ2V0IHJvbGUnLFxuICAgICAgJ0NvbnNpZGVyIGFkZGluZyBza2lsbHMgbWVudGlvbmVkIGluIHRoZSBqb2IgZGVzY3JpcHRpb24nXG4gICAgXSxcbiAgICBmYWxsYmFjazogdHJ1ZVxuICB9KTtcbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJHb29nbGVHZW5lcmF0aXZlQUkiLCJnZW5BSSIsInByb2Nlc3MiLCJlbnYiLCJHRU1JTklfQVBJX0tFWSIsIlBPU1QiLCJyZXF1ZXN0IiwiY29uc29sZSIsImxvZyIsImV4dHJhY3RlZFJlc3VtZURhdGEiLCJqb2JEZXNjcmlwdGlvbiIsImpvYlRpdGxlIiwiY29tcGFueSIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsImxlbmd0aCIsImNyZWF0ZUZhbGxiYWNrVGFyZ2V0ZWRSZXN1bWUiLCJtb2RlbCIsImdldEdlbmVyYXRpdmVNb2RlbCIsImdlbmVyYXRpb25Db25maWciLCJ0ZW1wZXJhdHVyZSIsInRvcEsiLCJ0b3BQIiwibWF4T3V0cHV0VG9rZW5zIiwicHJvbXB0IiwiY3JlYXRlVGFyZ2V0ZWRSZXN1bWVQcm9tcHQiLCJyZXN1bHQiLCJnZW5lcmF0ZUNvbnRlbnQiLCJyZXNwb25zZSIsImdlbmVyYXRlZENvbnRlbnQiLCJ0ZXh0IiwiZW5oYW5jZWRSZXN1bWUiLCJwYXJzZVRhcmdldGVkUmVzdW1lUmVzcG9uc2UiLCJzdWNjZXNzIiwiam9iVGFyZ2V0aW5nIiwiZGVzY3JpcHRpb24iLCJzdWJzdHJpbmciLCJwcm9jZXNzZWRBdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImFpRXJyb3IiLCJkZXRhaWxzIiwibWVzc2FnZSIsInJlc3VtZURhdGEiLCJyYXdSZXN1bWVUZXh0IiwicGVyc29uYWwiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsImVtYWlsIiwicGhvbmUiLCJsb2NhdGlvbiIsImxpbmtlZGluIiwicG9ydGZvbGlvIiwic3VtbWFyeSIsImVkdWNhdGlvbiIsIm1hcCIsImVkdSIsImRlZ3JlZSIsImluc3RpdHV0aW9uIiwic3RhcnREYXRlIiwiZW5kRGF0ZSIsImpvaW4iLCJleHBlcmllbmNlIiwiZXhwIiwidGl0bGUiLCJjdXJyZW50Iiwic2tpbGxzIiwidGVjaG5pY2FsIiwibGFuZ3VhZ2VzIiwiY2VydGlmaWNhdGlvbnMiLCJwcm9qZWN0cyIsInByb2oiLCJuYW1lIiwidGVjaG5vbG9naWVzIiwianNvbk1hdGNoIiwibWF0Y2giLCJwYXJzZWRKc29uIiwiSlNPTiIsInBhcnNlIiwiYXRzU2NvcmUiLCJvdmVyYWxsIiwiYnJlYWtkb3duIiwia2V5d29yZHMiLCJmb3JtYXR0aW5nIiwic3RydWN0dXJlIiwiYWNoaWV2ZW1lbnRzIiwidGFyZ2V0aW5nQW5hbHlzaXMiLCJrZXl3b3JkTWF0Y2hlcyIsInNraWxsc0FsaWdubWVudCIsImV4cGVyaWVuY2VSZWxldmFuY2UiLCJpbXByb3ZlbWVudHNNYWRlIiwicmVjb21tZW5kYXRpb25zIiwiZmFsbGJhY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-targeted-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();