/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-targeted-resume/route";
exports.ids = ["app/api/generate-targeted-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_targeted_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-targeted-resume/route.js */ \"(rsc)/./src/app/api/generate-targeted-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-targeted-resume/route\",\n        pathname: \"/api/generate-targeted-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-targeted-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\generate-targeted-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_targeted_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-targeted-resume/route.js":
/*!*******************************************************!*\
  !*** ./src/app/api/generate-targeted-resume/route.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        console.log('🎯 Targeted resume generation API called');\n        const { extractedResumeData, jobDescription, jobTitle, company } = await request.json();\n        if (!extractedResumeData || !jobDescription) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required data: resume data and job description are required'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📊 Processing targeted resume generation...');\n        console.log('🎯 Job Title:', jobTitle);\n        console.log('🏢 Company:', company);\n        console.log('📝 Job Description Length:', jobDescription.length);\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback enhancement');\n            return createFallbackTargetedResume(extractedResumeData, jobDescription, jobTitle, company);\n        }\n        try {\n            // Get the generative model - Using Gemini Pro\n            const model = genAI.getGenerativeModel({\n                model: 'gemini-1.5-pro-latest',\n                generationConfig: {\n                    temperature: 0.7,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 5000\n                }\n            });\n            const prompt = createTargetedResumePrompt(extractedResumeData, jobDescription, jobTitle, company);\n            console.log('🤖 Calling Gemini AI for targeted resume generation...');\n            const result = await model.generateContent(prompt);\n            const response = await result.response;\n            const generatedContent = response.text();\n            console.log('✅ Gemini targeted analysis completed, length:', generatedContent?.length || 0);\n            // Parse the AI response\n            const enhancedResume = parseTargetedResumeResponse(generatedContent);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                enhancedResume,\n                jobTargeting: {\n                    jobTitle,\n                    company,\n                    description: jobDescription.substring(0, 200) + '...'\n                },\n                processedAt: new Date().toISOString()\n            });\n        } catch (aiError) {\n            console.error('AI enhancement error:', aiError);\n            console.log('⚠️ AI enhancement failed, using fallback enhancement');\n            return createFallbackTargetedResume(extractedResumeData, jobDescription, jobTitle, company);\n        }\n    } catch (error) {\n        console.error('💥 Targeted resume generation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate targeted resume',\n            details:  true ? error.message : 0\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createTargetedResumePrompt(resumeData, jobDescription, jobTitle, company) {\n    return `\nYou are an expert resume writer and ATS optimization specialist. Create a highly targeted, ATS-optimized resume based on the provided resume data and job description.\n\nORIGINAL RESUME DATA:\n${JSON.stringify(resumeData, null, 2)}\n\nTARGET JOB INFORMATION:\nJob Title: ${jobTitle || 'Not specified'}\nCompany: ${company || 'Not specified'}\nJob Description: ${jobDescription}\n\nTASK: Create an enhanced, job-targeted resume that:\n1. Optimizes keywords from the job description naturally throughout the resume\n2. Highlights the most relevant experience and skills for this specific role\n3. Quantifies achievements with metrics where possible\n4. Creates a compelling professional summary targeted to this job\n5. Prioritizes and emphasizes experiences most relevant to the job requirements\n6. Uses action verbs and industry terminology from the job description\n7. Ensures ATS compatibility with proper formatting and keyword density\n\nPlease respond in the following JSON format:\n{\n  \"enhancedResume\": {\n    \"personal\": {\n      \"firstName\": \"Enhanced first name\",\n      \"lastName\": \"Enhanced last name\", \n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"location\": \"City, State\",\n      \"linkedin\": \"linkedin.com/in/profile\",\n      \"portfolio\": \"portfolio.com\",\n      \"summary\": \"Compelling 3-4 sentence professional summary specifically targeted to this job, incorporating key requirements and showcasing relevant value proposition\"\n    },\n    \"experience\": [\n      {\n        \"id\": 1,\n        \"title\": \"Job Title\",\n        \"company\": \"Company Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"YYYY-MM\",\n        \"endDate\": \"YYYY-MM\",\n        \"current\": false,\n        \"description\": \"Enhanced description with quantified achievements\",\n        \"achievements\": [\n          \"• Quantified achievement that aligns with job requirements\",\n          \"• Another achievement using keywords from job description\",\n          \"• Third achievement showing relevant impact and skills\"\n        ]\n      }\n    ],\n    \"education\": [\n      {\n        \"id\": 1,\n        \"degree\": \"Degree Name\",\n        \"institution\": \"Institution Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"YYYY-MM\",\n        \"endDate\": \"YYYY-MM\",\n        \"gpa\": \"GPA if relevant\",\n        \"relevant\": \"Relevant coursework that aligns with job requirements\"\n      }\n    ],\n    \"skills\": {\n      \"technical\": [\"Prioritized technical skills matching job requirements\"],\n      \"languages\": [\"Languages if relevant to the job\"],\n      \"certifications\": [\"Relevant certifications for this role\"]\n    },\n    \"projects\": [\n      {\n        \"id\": 1,\n        \"name\": \"Project Name\",\n        \"description\": \"Project description highlighting skills relevant to the job\",\n        \"technologies\": \"Technologies that match job requirements\",\n        \"link\": \"project-link.com\"\n      }\n    ]\n  },\n  \"atsScore\": {\n    \"overall\": 95,\n    \"breakdown\": {\n      \"keywords\": 95,\n      \"formatting\": 90,\n      \"structure\": 95,\n      \"achievements\": 90,\n      \"skills\": 95\n    }\n  },\n  \"targetingAnalysis\": {\n    \"keywordMatches\": [\"List of keywords from job description incorporated\"],\n    \"skillsAlignment\": [\"Skills that directly match job requirements\"],\n    \"experienceRelevance\": \"Analysis of how experience aligns with job\",\n    \"improvementsMade\": [\n      \"Specific improvement made for job targeting\",\n      \"Another enhancement for ATS optimization\",\n      \"Additional targeting improvement\"\n    ]\n  },\n  \"recommendations\": [\n    \"Specific recommendation for this job application\",\n    \"Another targeted suggestion\",\n    \"Additional advice for this role\"\n  ]\n}\n\nEnsure the enhanced resume is specifically optimized for this job while maintaining authenticity and accuracy of the original information.\n`;\n}\nfunction parseTargetedResumeResponse(generatedContent) {\n    try {\n        // Try to extract JSON from the response\n        const jsonMatch = generatedContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            const parsedJson = JSON.parse(jsonMatch[0]);\n            return parsedJson;\n        }\n    } catch (error) {\n        console.error('Error parsing targeted resume response:', error);\n    }\n    // Fallback response\n    return {\n        enhancedResume: {\n            personal: {\n                firstName: '',\n                lastName: '',\n                email: '',\n                phone: '',\n                location: '',\n                summary: 'Enhanced professional summary targeting the specific job requirements.'\n            },\n            experience: [],\n            education: [],\n            skills: {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: []\n        },\n        atsScore: {\n            overall: 85,\n            breakdown: {\n                keywords: 85,\n                formatting: 85,\n                structure: 85,\n                achievements: 85,\n                skills: 85\n            }\n        },\n        targetingAnalysis: {\n            keywordMatches: [],\n            skillsAlignment: [],\n            experienceRelevance: 'Resume has been optimized for the target job.',\n            improvementsMade: [\n                'Job-specific optimization applied',\n                'ATS compatibility enhanced'\n            ]\n        },\n        recommendations: [\n            'Review the enhanced resume for accuracy',\n            'Customize further based on specific job requirements'\n        ]\n    };\n}\nfunction createFallbackTargetedResume(resumeData, jobDescription, jobTitle, company) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        enhancedResume: {\n            ...resumeData,\n            personal: {\n                ...resumeData.personal,\n                summary: resumeData.personal.summary || `Experienced professional seeking ${jobTitle || 'the target position'} role${company ? ` at ${company}` : ''}. Ready to contribute relevant skills and experience to drive organizational success.`\n            }\n        },\n        atsScore: {\n            overall: 80,\n            breakdown: {\n                keywords: 75,\n                formatting: 85,\n                structure: 80,\n                achievements: 75,\n                skills: 85\n            }\n        },\n        targetingAnalysis: {\n            keywordMatches: [\n                'Basic optimization applied'\n            ],\n            skillsAlignment: [\n                'Manual review recommended'\n            ],\n            experienceRelevance: 'Resume structure optimized for ATS compatibility.',\n            improvementsMade: [\n                'Basic job targeting applied',\n                'ATS formatting enhanced'\n            ]\n        },\n        recommendations: [\n            'Review and manually optimize keywords from the job description',\n            'Quantify achievements with specific metrics',\n            'Highlight experience most relevant to the target role',\n            'Consider adding skills mentioned in the job description'\n        ],\n        fallback: true\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-targeted-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-targeted-resume%2Froute&page=%2Fapi%2Fgenerate-targeted-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-targeted-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();