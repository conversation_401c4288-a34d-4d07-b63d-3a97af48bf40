import { NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export async function POST(request) {
  try {
    console.log('🎯 AI content suggestions API called');
    
    const { fieldType, currentValue, context } = await request.json();
    
    if (!fieldType) {
      return NextResponse.json(
        { error: 'Field type is required' },
        { status: 400 }
      );
    }

    console.log('📊 Generating suggestions for:', fieldType);
    console.log('🔍 Context:', context);

    // Check if API key is configured
    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {
      console.log('⚠️ Gemini API key not configured, using fallback suggestions');
      return createFallbackSuggestions(fieldType, currentValue, context);
    }

    try {
      // Get the generative model - Using Gemini 2.0 Flash
      const model = genAI.getGenerativeModel({ 
        model: 'gemini-2.0-flash-exp',
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2000,
        }
      });

      const prompt = createSuggestionPrompt(fieldType, currentValue, context);

      console.log('🤖 Calling Gemini AI for content suggestions...');
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const generatedContent = response.text();

      console.log('✅ Gemini suggestions completed, length:', generatedContent?.length || 0);

      // Parse the AI response
      const suggestions = parseSuggestionsResponse(generatedContent, fieldType);

      return NextResponse.json({
        success: true,
        suggestions,
        fieldType,
        processedAt: new Date().toISOString()
      });

    } catch (aiError) {
      console.error('AI suggestions error:', aiError);
      console.log('⚠️ AI suggestions failed, using fallback suggestions');
      return createFallbackSuggestions(fieldType, currentValue, context);
    }

  } catch (error) {
    console.error('💥 Content suggestions error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate content suggestions', 
        details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

function createSuggestionPrompt(fieldType, currentValue, context) {
  const basePrompt = `You are an expert resume writer and career coach. Generate 3 high-quality, professional suggestions for the following resume field.

FIELD TYPE: ${fieldType}
CURRENT VALUE: ${currentValue || 'Not provided'}
CONTEXT: ${JSON.stringify(context)}

Requirements:
- Make suggestions professional and ATS-friendly
- Use action verbs and quantifiable achievements where appropriate
- Tailor suggestions to the field type and context
- Keep suggestions concise but impactful
- Use industry-standard terminology

`;

  switch (fieldType) {
    case 'summary':
      return basePrompt + `
Generate 3 professional summary suggestions that:
- Are 2-3 sentences long
- Highlight key skills and experience
- Include quantifiable achievements when possible
- Use strong action words
- Are tailored to the person's background

Format: Return only the 3 suggestions, one per line, without numbering or bullets.`;

    case 'experience':
      return basePrompt + `
Generate 3 professional experience bullet points that:
- Start with strong action verbs
- Include quantifiable results and metrics
- Highlight technical skills and achievements
- Are specific and impactful
- Follow the format: "• [Action verb] + [what you did] + [quantified result]"

Format: Return only the 3 bullet points, one per line, each starting with "•".`;

    case 'skills':
      return basePrompt + `
Generate 3 different skill combinations that:
- Include relevant technical skills for the field
- Mix programming languages, frameworks, and tools
- Are appropriate for the experience level
- Include modern, in-demand technologies
- Are formatted as comma-separated lists

Format: Return only the 3 skill lists, one per line, comma-separated.`;

    case 'projects':
      return basePrompt + `
Generate 3 project descriptions that:
- Describe realistic, impressive projects
- Include technical details and technologies used
- Mention the impact or results achieved
- Are relevant to the person's field
- Are 1-2 sentences long

Format: Return only the 3 project descriptions, one per line.`;

    default:
      return basePrompt + `
Generate 3 professional suggestions for this field that are:
- Relevant and industry-appropriate
- Well-written and professional
- Specific and actionable
- ATS-friendly

Format: Return only the 3 suggestions, one per line.`;
  }
}

function parseSuggestionsResponse(generatedContent, fieldType) {
  try {
    // Split the response into lines and clean them up
    const lines = generatedContent
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .filter(line => !line.match(/^(Here are|Suggestion|Option|\d+\.|\*)/i)) // Remove headers
      .slice(0, 3); // Take only first 3 suggestions

    if (lines.length > 0) {
      return lines;
    }
  } catch (error) {
    console.error('Error parsing suggestions response:', error);
  }

  // Fallback suggestions
  return getFallbackSuggestionsByType(fieldType);
}

function getFallbackSuggestionsByType(fieldType) {
  switch (fieldType) {
    case 'summary':
      return [
        "Experienced professional with strong background in technology and proven track record of delivering high-quality solutions.",
        "Results-driven specialist with expertise in modern development practices and commitment to continuous learning.",
        "Dedicated professional with comprehensive experience in software development and team collaboration."
      ];
    case 'experience':
      return [
        "• Developed and maintained web applications using modern frameworks, improving user experience by 40%",
        "• Collaborated with cross-functional teams to deliver software solutions, reducing project timeline by 25%",
        "• Implemented best practices for code quality and testing, increasing system reliability by 30%"
      ];
    case 'skills':
      return [
        "JavaScript, React, Node.js, Python, MongoDB, Git",
        "HTML5, CSS3, TypeScript, PostgreSQL, Docker, AWS",
        "Java, Spring Boot, MySQL, Jenkins, Kubernetes, Agile"
      ];
    case 'projects':
      return [
        "Developed a full-stack e-commerce platform with React and Node.js, supporting 1000+ concurrent users",
        "Built a real-time chat application using WebSocket technology, achieving 99.9% uptime",
        "Created a data visualization dashboard with D3.js, processing 10M+ data points efficiently"
      ];
    default:
      return [
        "Professional suggestion for your resume field",
        "Industry-standard content recommendation",
        "ATS-optimized professional content"
      ];
  }
}

function createFallbackSuggestions(fieldType, currentValue, context) {
  return NextResponse.json({
    success: true,
    suggestions: getFallbackSuggestionsByType(fieldType),
    fieldType,
    fallback: true,
    processedAt: new Date().toISOString()
  });
}
