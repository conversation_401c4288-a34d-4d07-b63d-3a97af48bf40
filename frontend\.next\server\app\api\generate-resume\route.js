/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-resume/route";
exports.ids = ["app/api/generate-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-resume/route.js */ \"(rsc)/./src/app/api/generate-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-resume/route\",\n        pathname: \"/api/generate-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\generate-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-resume/route.js":
/*!**********************************************!*\
  !*** ./src/app/api/generate-resume/route.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n// Initialize Gemini AI with API key\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    let formData = {};\n    try {\n        console.log('🚀 Resume generation API called');\n        formData = await request.json();\n        console.log('📝 Form data received:', JSON.stringify(formData, null, 2));\n        // Validate required fields\n        if (!formData.personal?.firstName || !formData.personal?.lastName || !formData.personal?.email) {\n            console.error('❌ Missing required fields');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: 'Missing required personal information (First Name, Last Name, and Email are required)'\n            }, {\n                status: 400\n            });\n        }\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback');\n            // Fallback: return a basic resume structure\n            const fallbackResume = createFallbackResume(formData);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                resumeData: fallbackResume,\n                atsScore: fallbackResume.atsScore?.overall || 75,\n                suggestions: fallbackResume.atsScore?.improvements || [],\n                downloadUrl: '/api/download-resume/' + Date.now(),\n                message: 'Resume generated successfully (using template)',\n                fallback: true\n            });\n        }\n        console.log('🤖 Initializing Gemini AI...');\n        // Get the generative model\n        const model = genAI.getGenerativeModel({\n            model: 'gemini-1.5-flash-latest',\n            generationConfig: {\n                temperature: 0.7,\n                topK: 40,\n                topP: 0.95,\n                maxOutputTokens: 2048\n            }\n        });\n        // Prepare prompt for Gemini API\n        const prompt = createResumePrompt(formData);\n        console.log('📋 Prompt created, length:', prompt.length);\n        // Generate content using the official SDK\n        console.log('🔄 Calling Gemini API...');\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const generatedContent = response.text();\n        console.log('✅ Gemini response received, length:', generatedContent?.length || 0);\n        if (!generatedContent) {\n            throw new Error('No content generated from Gemini API');\n        }\n        // Process the generated content and create resume\n        console.log('🔧 Processing generated content...');\n        const resumeData = processGeneratedContent(generatedContent, formData);\n        console.log('📊 Resume data processed:', {\n            hasEnhancedContent: !!resumeData.enhancedContent,\n            atsScore: resumeData.atsScore?.overall,\n            suggestionsCount: resumeData.atsScore?.improvements?.length || 0\n        });\n        // Return the processed data with enhanced structure\n        const responseData = {\n            success: true,\n            resumeData,\n            atsScore: resumeData.atsScore?.overall || 75,\n            suggestions: resumeData.atsScore?.improvements || [],\n            downloadUrl: '/api/download-resume/' + Date.now(),\n            message: 'Resume generated successfully with AI optimization',\n            generatedAt: new Date().toISOString()\n        };\n        console.log('✨ Sending successful response');\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(responseData);\n    } catch (error) {\n        console.error('💥 Resume generation error:', error);\n        console.error('Error stack:', error.stack);\n        // Enhanced error handling with fallback\n        try {\n            const fallbackResume = createFallbackResume(formData || {});\n            const fallbackResponse = {\n                success: true,\n                resumeData: fallbackResume,\n                atsScore: fallbackResume.atsScore?.overall || 75,\n                suggestions: fallbackResume.atsScore?.improvements || [],\n                downloadUrl: '/api/download-resume/' + Date.now(),\n                message: 'Resume generated successfully (fallback mode)',\n                fallback: true,\n                error:  true ? error.message : 0\n            };\n            console.log('🔄 Sending fallback response');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(fallbackResponse);\n        } catch (fallbackError) {\n            console.error('💥 Fallback also failed:', fallbackError);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: 'Failed to generate resume',\n                details:  true ? error.message : 0\n            }, {\n                status: 500\n            });\n        }\n    }\n}\nfunction createResumePrompt(formData) {\n    const { personal, education, experience, skills, projects } = formData;\n    return `You are an expert resume writer and ATS optimization specialist. Create a highly optimized, ATS-friendly professional resume based on the following information. Your goal is to maximize ATS compatibility while maintaining professional appeal.\n\nCRITICAL ATS OPTIMIZATION REQUIREMENTS:\n1. Use standard section headers (PROFESSIONAL SUMMARY, EXPERIENCE, EDUCATION, SKILLS, PROJECTS)\n2. Include relevant industry keywords and action verbs\n3. Quantify achievements with specific metrics and percentages\n4. Use bullet points with strong action verbs (Led, Managed, Developed, Implemented, Achieved, etc.)\n5. Ensure keyword density for technical skills and industry terms\n6. Format dates consistently (MM/YYYY format)\n7. Use standard job titles and company descriptions\n\nINPUT DATA:\nPersonal Information:\n- Name: ${personal.firstName} ${personal.lastName}\n- Email: ${personal.email}\n- Phone: ${personal.phone || 'Not provided'}\n- Location: ${personal.location || 'Not provided'}\n- LinkedIn: ${personal.linkedin || 'Not provided'}\n- Portfolio: ${personal.portfolio || 'Not provided'}\n- Summary: ${personal.summary || 'Not provided'}\n\nEducation: ${education ? JSON.stringify(education, null, 2) : 'Not provided'}\nExperience: ${experience ? JSON.stringify(experience, null, 2) : 'Not provided'}\nSkills: ${skills ? JSON.stringify(skills, null, 2) : 'Not provided'}\nProjects: ${projects ? JSON.stringify(projects, null, 2) : 'Not provided'}\n\nENHANCEMENT INSTRUCTIONS:\n1. **Professional Summary**: Rewrite to be compelling, keyword-rich, and achievement-focused (3-4 lines max)\n2. **Experience**: Transform basic job descriptions into achievement-focused bullet points with:\n   - Strong action verbs\n   - Quantified results (percentages, numbers, metrics)\n   - Industry-relevant keywords\n   - Impact statements\n3. **Skills**: Organize into categories (Technical, Languages, Certifications) with industry-standard terminology\n4. **Projects**: Enhance descriptions with technical details and business impact\n5. **Education**: Add relevant coursework, honors, or achievements if applicable\n\nOUTPUT FORMAT: Return a JSON object with this exact structure:\n{\n  \"enhancedContent\": {\n    \"professionalSummary\": \"Enhanced summary text\",\n    \"experience\": [\n      {\n        \"title\": \"Enhanced job title\",\n        \"company\": \"Company name\",\n        \"location\": \"Location\",\n        \"startDate\": \"MM/YYYY\",\n        \"endDate\": \"MM/YYYY or Present\",\n        \"achievements\": [\"Enhanced bullet point 1\", \"Enhanced bullet point 2\", \"Enhanced bullet point 3\"]\n      }\n    ],\n    \"education\": [enhanced education entries],\n    \"skills\": {\n      \"technical\": [\"enhanced technical skills\"],\n      \"languages\": [\"languages\"],\n      \"certifications\": [\"certifications\"]\n    },\n    \"projects\": [enhanced project entries]\n  },\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 90,\n      \"formatting\": 85,\n      \"achievements\": 80,\n      \"skills\": 90\n    },\n    \"improvements\": [\"Specific improvement suggestions\"]\n  },\n  \"keywords\": [\"list of important keywords included\"]\n}\n\nMake the resume content significantly more professional and impactful than the original input.`;\n}\nfunction processGeneratedContent(content, originalData) {\n    try {\n        // Extract JSON from the AI response (handle potential markdown formatting)\n        let jsonContent = content;\n        // Remove markdown code blocks if present\n        if (content.includes('```json')) {\n            const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonContent = jsonMatch[1];\n            }\n        } else if (content.includes('```')) {\n            const jsonMatch = content.match(/```\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonContent = jsonMatch[1];\n            }\n        }\n        // Parse the enhanced content\n        const enhancedData = JSON.parse(jsonContent);\n        // Validate the structure\n        if (!enhancedData.enhancedContent || !enhancedData.atsScore) {\n            throw new Error('Invalid AI response structure');\n        }\n        return {\n            enhancedContent: enhancedData.enhancedContent,\n            atsScore: enhancedData.atsScore,\n            keywords: enhancedData.keywords || [],\n            originalData,\n            timestamp: new Date().toISOString(),\n            version: '2.0',\n            type: 'ai-enhanced'\n        };\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n        // Fallback: create a basic enhanced structure\n        return createEnhancedFallback(originalData, content);\n    }\n}\nfunction createEnhancedFallback(originalData, rawContent) {\n    const { personal, education, experience, skills, projects } = originalData;\n    return {\n        enhancedContent: {\n            professionalSummary: personal.summary || 'Dedicated professional with strong background and commitment to excellence.',\n            experience: experience?.map((exp)=>({\n                    title: exp.title,\n                    company: exp.company,\n                    location: exp.location,\n                    startDate: exp.startDate,\n                    endDate: exp.current ? 'Present' : exp.endDate,\n                    achievements: exp.description ? [\n                        exp.description\n                    ] : [\n                        'Contributed to team success and organizational goals'\n                    ]\n                })) || [],\n            education: education || [],\n            skills: skills || {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: projects || []\n        },\n        atsScore: {\n            overall: 75,\n            breakdown: {\n                keywords: 70,\n                formatting: 80,\n                achievements: 70,\n                skills: 80\n            },\n            improvements: [\n                'Add more quantified achievements',\n                'Include industry keywords',\n                'Enhance technical skills section'\n            ]\n        },\n        keywords: [\n            'professional',\n            'experienced',\n            'skilled'\n        ],\n        originalData,\n        timestamp: new Date().toISOString(),\n        version: '2.0',\n        type: 'fallback-enhanced',\n        rawContent\n    };\n}\nfunction createFallbackResume(formData) {\n    console.log('🔄 Creating fallback resume for:', formData);\n    // Ensure formData has the expected structure\n    const safeFormData = {\n        personal: formData?.personal || {},\n        education: formData?.education || [],\n        experience: formData?.experience || [],\n        skills: formData?.skills || {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: formData?.projects || []\n    };\n    const { personal, education, experience, skills, projects } = safeFormData;\n    const fallbackData = {\n        enhancedContent: {\n            professionalSummary: personal.summary || 'Dedicated professional with strong educational background and commitment to excellence.',\n            experience: experience.map((exp)=>({\n                    title: exp.title || 'Position',\n                    company: exp.company || 'Company',\n                    location: exp.location || '',\n                    startDate: exp.startDate || '',\n                    endDate: exp.current ? 'Present' : exp.endDate || '',\n                    achievements: exp.description ? [\n                        exp.description\n                    ] : [\n                        'Contributed to team objectives and organizational success'\n                    ]\n                })),\n            education: education,\n            skills: {\n                technical: skills.technical || [],\n                languages: skills.languages || [],\n                certifications: skills.certifications || []\n            },\n            projects: projects\n        },\n        atsScore: {\n            overall: 70,\n            breakdown: {\n                keywords: 65,\n                formatting: 75,\n                achievements: 65,\n                skills: 75\n            },\n            improvements: [\n                'Add quantified achievements with specific metrics',\n                'Include more industry-relevant keywords',\n                'Enhance technical skills with proficiency levels',\n                'Add action verbs to experience descriptions'\n            ]\n        },\n        keywords: [\n            'professional',\n            'experienced',\n            'dedicated',\n            'skilled'\n        ],\n        originalData: safeFormData,\n        timestamp: new Date().toISOString(),\n        version: '2.0',\n        type: 'fallback'\n    };\n    console.log('✅ Fallback resume created:', fallbackData);\n    return fallbackData;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();