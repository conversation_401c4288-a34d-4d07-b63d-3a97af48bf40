/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-resume/route";
exports.ids = ["app/api/generate-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-resume/route.js */ \"(rsc)/./src/app/api/generate-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-resume/route\",\n        pathname: \"/api/generate-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\generate-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-resume/route.js":
/*!**********************************************!*\
  !*** ./src/app/api/generate-resume/route.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Note: Replace with your actual Gemini API integration\n// This is a placeholder structure for the Gemini API integration\nconst GEMINI_API_KEY = process.env.GEMINI_API_KEY;\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';\nasync function POST(request) {\n    try {\n        const formData = await request.json();\n        // Validate required fields\n        if (!formData.personal?.firstName || !formData.personal?.lastName || !formData.personal?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required personal information (First Name, Last Name, and Email are required)'\n            }, {\n                status: 400\n            });\n        }\n        // Check if API key is configured\n        if (!GEMINI_API_KEY || GEMINI_API_KEY === 'your_actual_gemini_api_key_here') {\n            console.error('Gemini API key not configured, using fallback');\n            // Fallback: return a basic resume structure\n            const fallbackResume = createFallbackResume(formData);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                resumeData: fallbackResume,\n                downloadUrl: '/api/download-resume/' + Date.now(),\n                message: 'Resume generated successfully (using template)',\n                fallback: true\n            });\n        }\n        // Prepare prompt for Gemini API\n        const prompt = createResumePrompt(formData);\n        // Call Gemini API\n        const geminiResponse = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                contents: [\n                    {\n                        parts: [\n                            {\n                                text: prompt\n                            }\n                        ]\n                    }\n                ],\n                generationConfig: {\n                    temperature: 0.7,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 2048\n                }\n            })\n        });\n        if (!geminiResponse.ok) {\n            throw new Error(`Gemini API error: ${geminiResponse.status}`);\n        }\n        const geminiData = await geminiResponse.json();\n        const generatedContent = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;\n        if (!generatedContent) {\n            throw new Error('No content generated from Gemini API');\n        }\n        // Process the generated content and create resume\n        const resumeData = processGeneratedContent(generatedContent, formData);\n        // In a real implementation, you would:\n        // 1. Generate PDF from the resume data\n        // 2. Store it in cloud storage\n        // 3. Return the download URL\n        // For now, return the processed data\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            resumeData,\n            // Mock URL - replace with actual PDF generation\n            downloadUrl: '/api/download-resume/' + Date.now(),\n            message: 'Resume generated successfully'\n        });\n    } catch (error) {\n        console.error('Resume generation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate resume',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createResumePrompt(formData) {\n    const { personal, education, experience, skills, projects } = formData;\n    return `You are an expert resume writer and ATS optimization specialist. Create a highly optimized, ATS-friendly professional resume based on the following information. Your goal is to maximize ATS compatibility while maintaining professional appeal.\n\nCRITICAL ATS OPTIMIZATION REQUIREMENTS:\n1. Use standard section headers (PROFESSIONAL SUMMARY, EXPERIENCE, EDUCATION, SKILLS, PROJECTS)\n2. Include relevant industry keywords and action verbs\n3. Quantify achievements with specific metrics and percentages\n4. Use bullet points with strong action verbs (Led, Managed, Developed, Implemented, Achieved, etc.)\n5. Ensure keyword density for technical skills and industry terms\n6. Format dates consistently (MM/YYYY format)\n7. Use standard job titles and company descriptions\n\nINPUT DATA:\nPersonal Information:\n- Name: ${personal.firstName} ${personal.lastName}\n- Email: ${personal.email}\n- Phone: ${personal.phone || 'Not provided'}\n- Location: ${personal.location || 'Not provided'}\n- LinkedIn: ${personal.linkedin || 'Not provided'}\n- Portfolio: ${personal.portfolio || 'Not provided'}\n- Summary: ${personal.summary || 'Not provided'}\n\nEducation: ${education ? JSON.stringify(education, null, 2) : 'Not provided'}\nExperience: ${experience ? JSON.stringify(experience, null, 2) : 'Not provided'}\nSkills: ${skills ? JSON.stringify(skills, null, 2) : 'Not provided'}\nProjects: ${projects ? JSON.stringify(projects, null, 2) : 'Not provided'}\n\nENHANCEMENT INSTRUCTIONS:\n1. **Professional Summary**: Rewrite to be compelling, keyword-rich, and achievement-focused (3-4 lines max)\n2. **Experience**: Transform basic job descriptions into achievement-focused bullet points with:\n   - Strong action verbs\n   - Quantified results (percentages, numbers, metrics)\n   - Industry-relevant keywords\n   - Impact statements\n3. **Skills**: Organize into categories (Technical, Languages, Certifications) with industry-standard terminology\n4. **Projects**: Enhance descriptions with technical details and business impact\n5. **Education**: Add relevant coursework, honors, or achievements if applicable\n\nOUTPUT FORMAT: Return a JSON object with this exact structure:\n{\n  \"enhancedContent\": {\n    \"professionalSummary\": \"Enhanced summary text\",\n    \"experience\": [\n      {\n        \"title\": \"Enhanced job title\",\n        \"company\": \"Company name\",\n        \"location\": \"Location\",\n        \"startDate\": \"MM/YYYY\",\n        \"endDate\": \"MM/YYYY or Present\",\n        \"achievements\": [\"Enhanced bullet point 1\", \"Enhanced bullet point 2\", \"Enhanced bullet point 3\"]\n      }\n    ],\n    \"education\": [enhanced education entries],\n    \"skills\": {\n      \"technical\": [\"enhanced technical skills\"],\n      \"languages\": [\"languages\"],\n      \"certifications\": [\"certifications\"]\n    },\n    \"projects\": [enhanced project entries]\n  },\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 90,\n      \"formatting\": 85,\n      \"achievements\": 80,\n      \"skills\": 90\n    },\n    \"improvements\": [\"Specific improvement suggestions\"]\n  },\n  \"keywords\": [\"list of important keywords included\"]\n}\n\nMake the resume content significantly more professional and impactful than the original input.`;\n}\nfunction processGeneratedContent(content, originalData) {\n    try {\n        // Extract JSON from the AI response (handle potential markdown formatting)\n        let jsonContent = content;\n        // Remove markdown code blocks if present\n        if (content.includes('```json')) {\n            const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonContent = jsonMatch[1];\n            }\n        } else if (content.includes('```')) {\n            const jsonMatch = content.match(/```\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonContent = jsonMatch[1];\n            }\n        }\n        // Parse the enhanced content\n        const enhancedData = JSON.parse(jsonContent);\n        // Validate the structure\n        if (!enhancedData.enhancedContent || !enhancedData.atsScore) {\n            throw new Error('Invalid AI response structure');\n        }\n        return {\n            enhancedContent: enhancedData.enhancedContent,\n            atsScore: enhancedData.atsScore,\n            keywords: enhancedData.keywords || [],\n            originalData,\n            timestamp: new Date().toISOString(),\n            version: '2.0',\n            type: 'ai-enhanced'\n        };\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n        // Fallback: create a basic enhanced structure\n        return createEnhancedFallback(originalData, content);\n    }\n}\nfunction createEnhancedFallback(originalData, rawContent) {\n    const { personal, education, experience, skills, projects } = originalData;\n    return {\n        enhancedContent: {\n            professionalSummary: personal.summary || 'Dedicated professional with strong background and commitment to excellence.',\n            experience: experience?.map((exp)=>({\n                    title: exp.title,\n                    company: exp.company,\n                    location: exp.location,\n                    startDate: exp.startDate,\n                    endDate: exp.current ? 'Present' : exp.endDate,\n                    achievements: exp.description ? [\n                        exp.description\n                    ] : [\n                        'Contributed to team success and organizational goals'\n                    ]\n                })) || [],\n            education: education || [],\n            skills: skills || {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: projects || []\n        },\n        atsScore: {\n            overall: 75,\n            breakdown: {\n                keywords: 70,\n                formatting: 80,\n                achievements: 70,\n                skills: 80\n            },\n            improvements: [\n                'Add more quantified achievements',\n                'Include industry keywords',\n                'Enhance technical skills section'\n            ]\n        },\n        keywords: [\n            'professional',\n            'experienced',\n            'skilled'\n        ],\n        originalData,\n        timestamp: new Date().toISOString(),\n        version: '2.0',\n        type: 'fallback-enhanced',\n        rawContent\n    };\n}\nfunction createFallbackResume(formData) {\n    const { personal, education, experience, skills, projects } = formData;\n    return {\n        enhancedContent: {\n            professionalSummary: personal.summary || 'Dedicated professional with strong educational background and commitment to excellence.',\n            experience: experience?.map((exp)=>({\n                    title: exp.title || 'Position',\n                    company: exp.company || 'Company',\n                    location: exp.location || '',\n                    startDate: exp.startDate || '',\n                    endDate: exp.current ? 'Present' : exp.endDate || '',\n                    achievements: exp.description ? [\n                        exp.description\n                    ] : [\n                        'Contributed to team objectives and organizational success'\n                    ]\n                })) || [],\n            education: education || [],\n            skills: skills || {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: projects || []\n        },\n        atsScore: {\n            overall: 70,\n            breakdown: {\n                keywords: 65,\n                formatting: 75,\n                achievements: 65,\n                skills: 75\n            },\n            improvements: [\n                'Add quantified achievements with specific metrics',\n                'Include more industry-relevant keywords',\n                'Enhance technical skills with proficiency levels',\n                'Add action verbs to experience descriptions'\n            ]\n        },\n        keywords: [\n            'professional',\n            'experienced',\n            'dedicated',\n            'skilled'\n        ],\n        originalData: formData,\n        timestamp: new Date().toISOString(),\n        version: '2.0',\n        type: 'fallback'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();