/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-resume/route";
exports.ids = ["app/api/generate-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-resume/route.js */ \"(rsc)/./src/app/api/generate-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-resume/route\",\n        pathname: \"/api/generate-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\generate-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-resume/route.js":
/*!**********************************************!*\
  !*** ./src/app/api/generate-resume/route.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n// Initialize Gemini AI with API key\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        const formData1 = await request.json();\n        // Validate required fields\n        if (!formData1.personal?.firstName || !formData1.personal?.lastName || !formData1.personal?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: 'Missing required personal information (First Name, Last Name, and Email are required)'\n            }, {\n                status: 400\n            });\n        }\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.error('Gemini API key not configured, using fallback');\n            // Fallback: return a basic resume structure\n            const fallbackResume = createFallbackResume(formData1);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                resumeData: fallbackResume,\n                downloadUrl: '/api/download-resume/' + Date.now(),\n                message: 'Resume generated successfully (using template)',\n                fallback: true\n            });\n        }\n        // Get the generative model\n        const model = genAI.getGenerativeModel({\n            model: 'gemini-pro',\n            generationConfig: {\n                temperature: 0.7,\n                topK: 40,\n                topP: 0.95,\n                maxOutputTokens: 2048\n            }\n        });\n        // Prepare prompt for Gemini API\n        const prompt = createResumePrompt(formData1);\n        // Generate content using the official SDK\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const generatedContent = response.text();\n        if (!generatedContent) {\n            throw new Error('No content generated from Gemini API');\n        }\n        // Process the generated content and create resume\n        const resumeData = processGeneratedContent(generatedContent, formData1);\n        // Return the processed data with enhanced structure\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            resumeData,\n            atsScore: resumeData.atsScore?.overall || 75,\n            suggestions: resumeData.atsScore?.improvements || [],\n            downloadUrl: '/api/download-resume/' + Date.now(),\n            message: 'Resume generated successfully with AI optimization',\n            generatedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Resume generation error:', error);\n        // Enhanced error handling with fallback\n        const fallbackResume = createFallbackResume(formData);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            resumeData: fallbackResume,\n            downloadUrl: '/api/download-resume/' + Date.now(),\n            message: 'Resume generated successfully (fallback mode)',\n            fallback: true,\n            error:  true ? error.message : 0\n        });\n    }\n}\nfunction createResumePrompt(formData1) {\n    const { personal, education, experience, skills, projects } = formData1;\n    return `You are an expert resume writer and ATS optimization specialist. Create a highly optimized, ATS-friendly professional resume based on the following information. Your goal is to maximize ATS compatibility while maintaining professional appeal.\n\nCRITICAL ATS OPTIMIZATION REQUIREMENTS:\n1. Use standard section headers (PROFESSIONAL SUMMARY, EXPERIENCE, EDUCATION, SKILLS, PROJECTS)\n2. Include relevant industry keywords and action verbs\n3. Quantify achievements with specific metrics and percentages\n4. Use bullet points with strong action verbs (Led, Managed, Developed, Implemented, Achieved, etc.)\n5. Ensure keyword density for technical skills and industry terms\n6. Format dates consistently (MM/YYYY format)\n7. Use standard job titles and company descriptions\n\nINPUT DATA:\nPersonal Information:\n- Name: ${personal.firstName} ${personal.lastName}\n- Email: ${personal.email}\n- Phone: ${personal.phone || 'Not provided'}\n- Location: ${personal.location || 'Not provided'}\n- LinkedIn: ${personal.linkedin || 'Not provided'}\n- Portfolio: ${personal.portfolio || 'Not provided'}\n- Summary: ${personal.summary || 'Not provided'}\n\nEducation: ${education ? JSON.stringify(education, null, 2) : 'Not provided'}\nExperience: ${experience ? JSON.stringify(experience, null, 2) : 'Not provided'}\nSkills: ${skills ? JSON.stringify(skills, null, 2) : 'Not provided'}\nProjects: ${projects ? JSON.stringify(projects, null, 2) : 'Not provided'}\n\nENHANCEMENT INSTRUCTIONS:\n1. **Professional Summary**: Rewrite to be compelling, keyword-rich, and achievement-focused (3-4 lines max)\n2. **Experience**: Transform basic job descriptions into achievement-focused bullet points with:\n   - Strong action verbs\n   - Quantified results (percentages, numbers, metrics)\n   - Industry-relevant keywords\n   - Impact statements\n3. **Skills**: Organize into categories (Technical, Languages, Certifications) with industry-standard terminology\n4. **Projects**: Enhance descriptions with technical details and business impact\n5. **Education**: Add relevant coursework, honors, or achievements if applicable\n\nOUTPUT FORMAT: Return a JSON object with this exact structure:\n{\n  \"enhancedContent\": {\n    \"professionalSummary\": \"Enhanced summary text\",\n    \"experience\": [\n      {\n        \"title\": \"Enhanced job title\",\n        \"company\": \"Company name\",\n        \"location\": \"Location\",\n        \"startDate\": \"MM/YYYY\",\n        \"endDate\": \"MM/YYYY or Present\",\n        \"achievements\": [\"Enhanced bullet point 1\", \"Enhanced bullet point 2\", \"Enhanced bullet point 3\"]\n      }\n    ],\n    \"education\": [enhanced education entries],\n    \"skills\": {\n      \"technical\": [\"enhanced technical skills\"],\n      \"languages\": [\"languages\"],\n      \"certifications\": [\"certifications\"]\n    },\n    \"projects\": [enhanced project entries]\n  },\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 90,\n      \"formatting\": 85,\n      \"achievements\": 80,\n      \"skills\": 90\n    },\n    \"improvements\": [\"Specific improvement suggestions\"]\n  },\n  \"keywords\": [\"list of important keywords included\"]\n}\n\nMake the resume content significantly more professional and impactful than the original input.`;\n}\nfunction processGeneratedContent(content, originalData) {\n    try {\n        // Extract JSON from the AI response (handle potential markdown formatting)\n        let jsonContent = content;\n        // Remove markdown code blocks if present\n        if (content.includes('```json')) {\n            const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonContent = jsonMatch[1];\n            }\n        } else if (content.includes('```')) {\n            const jsonMatch = content.match(/```\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonContent = jsonMatch[1];\n            }\n        }\n        // Parse the enhanced content\n        const enhancedData = JSON.parse(jsonContent);\n        // Validate the structure\n        if (!enhancedData.enhancedContent || !enhancedData.atsScore) {\n            throw new Error('Invalid AI response structure');\n        }\n        return {\n            enhancedContent: enhancedData.enhancedContent,\n            atsScore: enhancedData.atsScore,\n            keywords: enhancedData.keywords || [],\n            originalData,\n            timestamp: new Date().toISOString(),\n            version: '2.0',\n            type: 'ai-enhanced'\n        };\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n        // Fallback: create a basic enhanced structure\n        return createEnhancedFallback(originalData, content);\n    }\n}\nfunction createEnhancedFallback(originalData, rawContent) {\n    const { personal, education, experience, skills, projects } = originalData;\n    return {\n        enhancedContent: {\n            professionalSummary: personal.summary || 'Dedicated professional with strong background and commitment to excellence.',\n            experience: experience?.map((exp)=>({\n                    title: exp.title,\n                    company: exp.company,\n                    location: exp.location,\n                    startDate: exp.startDate,\n                    endDate: exp.current ? 'Present' : exp.endDate,\n                    achievements: exp.description ? [\n                        exp.description\n                    ] : [\n                        'Contributed to team success and organizational goals'\n                    ]\n                })) || [],\n            education: education || [],\n            skills: skills || {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: projects || []\n        },\n        atsScore: {\n            overall: 75,\n            breakdown: {\n                keywords: 70,\n                formatting: 80,\n                achievements: 70,\n                skills: 80\n            },\n            improvements: [\n                'Add more quantified achievements',\n                'Include industry keywords',\n                'Enhance technical skills section'\n            ]\n        },\n        keywords: [\n            'professional',\n            'experienced',\n            'skilled'\n        ],\n        originalData,\n        timestamp: new Date().toISOString(),\n        version: '2.0',\n        type: 'fallback-enhanced',\n        rawContent\n    };\n}\nfunction createFallbackResume(formData1) {\n    const { personal, education, experience, skills, projects } = formData1;\n    return {\n        enhancedContent: {\n            professionalSummary: personal.summary || 'Dedicated professional with strong educational background and commitment to excellence.',\n            experience: experience?.map((exp)=>({\n                    title: exp.title || 'Position',\n                    company: exp.company || 'Company',\n                    location: exp.location || '',\n                    startDate: exp.startDate || '',\n                    endDate: exp.current ? 'Present' : exp.endDate || '',\n                    achievements: exp.description ? [\n                        exp.description\n                    ] : [\n                        'Contributed to team objectives and organizational success'\n                    ]\n                })) || [],\n            education: education || [],\n            skills: skills || {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: projects || []\n        },\n        atsScore: {\n            overall: 70,\n            breakdown: {\n                keywords: 65,\n                formatting: 75,\n                achievements: 65,\n                skills: 75\n            },\n            improvements: [\n                'Add quantified achievements with specific metrics',\n                'Include more industry-relevant keywords',\n                'Enhance technical skills with proficiency levels',\n                'Add action verbs to experience descriptions'\n            ]\n        },\n        keywords: [\n            'professional',\n            'experienced',\n            'dedicated',\n            'skilled'\n        ],\n        originalData: formData1,\n        timestamp: new Date().toISOString(),\n        version: '2.0',\n        type: 'fallback'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS1yZXN1bWUvcm91dGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJEO0FBQ2hCO0FBRTNDLG9DQUFvQztBQUNwQyxNQUFNRSxRQUFRLElBQUlGLHFFQUFrQkEsQ0FBQ0csUUFBUUMsR0FBRyxDQUFDQyxjQUFjO0FBRXhELGVBQWVDLEtBQUtDLE9BQU87SUFDaEMsSUFBSTtRQUNGLE1BQU1DLFlBQVcsTUFBTUQsUUFBUUUsSUFBSTtRQUVuQywyQkFBMkI7UUFDM0IsSUFBSSxDQUFDRCxVQUFTRSxRQUFRLEVBQUVDLGFBQWEsQ0FBQ0gsVUFBU0UsUUFBUSxFQUFFRSxZQUFZLENBQUNKLFVBQVNFLFFBQVEsRUFBRUcsT0FBTztZQUM5RixPQUFPWixxREFBWUEsQ0FBQ1EsSUFBSSxDQUN0QjtnQkFBRUssT0FBTztZQUF3RixHQUNqRztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsaUNBQWlDO1FBQ2pDLElBQUksQ0FBQ1osUUFBUUMsR0FBRyxDQUFDQyxjQUFjLElBQUlGLFFBQVFDLEdBQUcsQ0FBQ0MsY0FBYyxLQUFLLDRCQUE0QjtZQUM1RlcsUUFBUUYsS0FBSyxDQUFDO1lBQ2QsNENBQTRDO1lBQzVDLE1BQU1HLGlCQUFpQkMscUJBQXFCVjtZQUM1QyxPQUFPUCxxREFBWUEsQ0FBQ1EsSUFBSSxDQUFDO2dCQUN2QlUsU0FBUztnQkFDVEMsWUFBWUg7Z0JBQ1pJLGFBQWEsMEJBQTBCQyxLQUFLQyxHQUFHO2dCQUMvQ0MsU0FBUztnQkFDVEMsVUFBVTtZQUNaO1FBQ0Y7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTUMsUUFBUXhCLE1BQU15QixrQkFBa0IsQ0FBQztZQUNyQ0QsT0FBTztZQUNQRSxrQkFBa0I7Z0JBQ2hCQyxhQUFhO2dCQUNiQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxpQkFBaUI7WUFDbkI7UUFDRjtRQUVBLGdDQUFnQztRQUNoQyxNQUFNQyxTQUFTQyxtQkFBbUIxQjtRQUVsQywwQ0FBMEM7UUFDMUMsTUFBTTJCLFNBQVMsTUFBTVQsTUFBTVUsZUFBZSxDQUFDSDtRQUMzQyxNQUFNSSxXQUFXLE1BQU1GLE9BQU9FLFFBQVE7UUFDdEMsTUFBTUMsbUJBQW1CRCxTQUFTRSxJQUFJO1FBRXRDLElBQUksQ0FBQ0Qsa0JBQWtCO1lBQ3JCLE1BQU0sSUFBSUUsTUFBTTtRQUNsQjtRQUVBLGtEQUFrRDtRQUNsRCxNQUFNcEIsYUFBYXFCLHdCQUF3Qkgsa0JBQWtCOUI7UUFFN0Qsb0RBQW9EO1FBQ3BELE9BQU9QLHFEQUFZQSxDQUFDUSxJQUFJLENBQUM7WUFDdkJVLFNBQVM7WUFDVEM7WUFDQXNCLFVBQVV0QixXQUFXc0IsUUFBUSxFQUFFQyxXQUFXO1lBQzFDQyxhQUFheEIsV0FBV3NCLFFBQVEsRUFBRUcsZ0JBQWdCLEVBQUU7WUFDcER4QixhQUFhLDBCQUEwQkMsS0FBS0MsR0FBRztZQUMvQ0MsU0FBUztZQUNUc0IsYUFBYSxJQUFJeEIsT0FBT3lCLFdBQVc7UUFDckM7SUFFRixFQUFFLE9BQU9qQyxPQUFPO1FBQ2RFLFFBQVFGLEtBQUssQ0FBQyw0QkFBNEJBO1FBRTFDLHdDQUF3QztRQUN4QyxNQUFNRyxpQkFBaUJDLHFCQUFxQlY7UUFDNUMsT0FBT1AscURBQVlBLENBQUNRLElBQUksQ0FBQztZQUN2QlUsU0FBUztZQUNUQyxZQUFZSDtZQUNaSSxhQUFhLDBCQUEwQkMsS0FBS0MsR0FBRztZQUMvQ0MsU0FBUztZQUNUQyxVQUFVO1lBQ1ZYLE9BQU9YLEtBQXNDLEdBQUdXLE1BQU1VLE9BQU8sR0FBR3dCLENBQVNBO1FBQzNFO0lBQ0Y7QUFDRjtBQUVBLFNBQVNkLG1CQUFtQjFCLFNBQVE7SUFDbEMsTUFBTSxFQUFFRSxRQUFRLEVBQUV1QyxTQUFTLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBRzVDO0lBRTlELE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7OztRQWFGLEVBQUVFLFNBQVNDLFNBQVMsQ0FBQyxDQUFDLEVBQUVELFNBQVNFLFFBQVEsQ0FBQztTQUN6QyxFQUFFRixTQUFTRyxLQUFLLENBQUM7U0FDakIsRUFBRUgsU0FBUzJDLEtBQUssSUFBSSxlQUFlO1lBQ2hDLEVBQUUzQyxTQUFTNEMsUUFBUSxJQUFJLGVBQWU7WUFDdEMsRUFBRTVDLFNBQVM2QyxRQUFRLElBQUksZUFBZTthQUNyQyxFQUFFN0MsU0FBUzhDLFNBQVMsSUFBSSxlQUFlO1dBQ3pDLEVBQUU5QyxTQUFTK0MsT0FBTyxJQUFJLGVBQWU7O1dBRXJDLEVBQUVSLFlBQVlTLEtBQUtDLFNBQVMsQ0FBQ1YsV0FBVyxNQUFNLEtBQUssZUFBZTtZQUNqRSxFQUFFQyxhQUFhUSxLQUFLQyxTQUFTLENBQUNULFlBQVksTUFBTSxLQUFLLGVBQWU7UUFDeEUsRUFBRUMsU0FBU08sS0FBS0MsU0FBUyxDQUFDUixRQUFRLE1BQU0sS0FBSyxlQUFlO1VBQzFELEVBQUVDLFdBQVdNLEtBQUtDLFNBQVMsQ0FBQ1AsVUFBVSxNQUFNLEtBQUssZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhGQWdEb0IsQ0FBQztBQUMvRjtBQUVBLFNBQVNYLHdCQUF3Qm1CLE9BQU8sRUFBRUMsWUFBWTtJQUNwRCxJQUFJO1FBQ0YsMkVBQTJFO1FBQzNFLElBQUlDLGNBQWNGO1FBRWxCLHlDQUF5QztRQUN6QyxJQUFJQSxRQUFRRyxRQUFRLENBQUMsWUFBWTtZQUMvQixNQUFNQyxZQUFZSixRQUFRSyxLQUFLLENBQUM7WUFDaEMsSUFBSUQsV0FBVztnQkFDYkYsY0FBY0UsU0FBUyxDQUFDLEVBQUU7WUFDNUI7UUFDRixPQUFPLElBQUlKLFFBQVFHLFFBQVEsQ0FBQyxRQUFRO1lBQ2xDLE1BQU1DLFlBQVlKLFFBQVFLLEtBQUssQ0FBQztZQUNoQyxJQUFJRCxXQUFXO2dCQUNiRixjQUFjRSxTQUFTLENBQUMsRUFBRTtZQUM1QjtRQUNGO1FBRUEsNkJBQTZCO1FBQzdCLE1BQU1FLGVBQWVSLEtBQUtTLEtBQUssQ0FBQ0w7UUFFaEMseUJBQXlCO1FBQ3pCLElBQUksQ0FBQ0ksYUFBYUUsZUFBZSxJQUFJLENBQUNGLGFBQWF4QixRQUFRLEVBQUU7WUFDM0QsTUFBTSxJQUFJRixNQUFNO1FBQ2xCO1FBRUEsT0FBTztZQUNMNEIsaUJBQWlCRixhQUFhRSxlQUFlO1lBQzdDMUIsVUFBVXdCLGFBQWF4QixRQUFRO1lBQy9CMkIsVUFBVUgsYUFBYUcsUUFBUSxJQUFJLEVBQUU7WUFDckNSO1lBQ0FTLFdBQVcsSUFBSWhELE9BQU95QixXQUFXO1lBQ2pDd0IsU0FBUztZQUNUQyxNQUFNO1FBQ1I7SUFFRixFQUFFLE9BQU8xRCxPQUFPO1FBQ2RFLFFBQVFGLEtBQUssQ0FBQyw4QkFBOEJBO1FBRTVDLDhDQUE4QztRQUM5QyxPQUFPMkQsdUJBQXVCWixjQUFjRDtJQUM5QztBQUNGO0FBRUEsU0FBU2EsdUJBQXVCWixZQUFZLEVBQUVhLFVBQVU7SUFDdEQsTUFBTSxFQUFFaEUsUUFBUSxFQUFFdUMsU0FBUyxFQUFFQyxVQUFVLEVBQUVDLE1BQU0sRUFBRUMsUUFBUSxFQUFFLEdBQUdTO0lBRTlELE9BQU87UUFDTE8saUJBQWlCO1lBQ2ZPLHFCQUFxQmpFLFNBQVMrQyxPQUFPLElBQUk7WUFDekNQLFlBQVlBLFlBQVkwQixJQUFJQyxDQUFBQSxNQUFRO29CQUNsQ0MsT0FBT0QsSUFBSUMsS0FBSztvQkFDaEJDLFNBQVNGLElBQUlFLE9BQU87b0JBQ3BCekIsVUFBVXVCLElBQUl2QixRQUFRO29CQUN0QjBCLFdBQVdILElBQUlHLFNBQVM7b0JBQ3hCQyxTQUFTSixJQUFJSyxPQUFPLEdBQUcsWUFBWUwsSUFBSUksT0FBTztvQkFDOUNFLGNBQWNOLElBQUlPLFdBQVcsR0FBRzt3QkFBQ1AsSUFBSU8sV0FBVztxQkFBQyxHQUFHO3dCQUFDO3FCQUF1RDtnQkFDOUcsT0FBTyxFQUFFO1lBQ1RuQyxXQUFXQSxhQUFhLEVBQUU7WUFDMUJFLFFBQVFBLFVBQVU7Z0JBQUVrQyxXQUFXLEVBQUU7Z0JBQUVDLFdBQVcsRUFBRTtnQkFBRUMsZ0JBQWdCLEVBQUU7WUFBQztZQUNyRW5DLFVBQVVBLFlBQVksRUFBRTtRQUMxQjtRQUNBVixVQUFVO1lBQ1JDLFNBQVM7WUFDVDZDLFdBQVc7Z0JBQ1RuQixVQUFVO2dCQUNWb0IsWUFBWTtnQkFDWk4sY0FBYztnQkFDZGhDLFFBQVE7WUFDVjtZQUNBTixjQUFjO2dCQUFDO2dCQUFvQztnQkFBNkI7YUFBbUM7UUFDckg7UUFDQXdCLFVBQVU7WUFBQztZQUFnQjtZQUFlO1NBQVU7UUFDcERSO1FBQ0FTLFdBQVcsSUFBSWhELE9BQU95QixXQUFXO1FBQ2pDd0IsU0FBUztRQUNUQyxNQUFNO1FBQ05FO0lBQ0Y7QUFDRjtBQUVBLFNBQVN4RCxxQkFBcUJWLFNBQVE7SUFDcEMsTUFBTSxFQUFFRSxRQUFRLEVBQUV1QyxTQUFTLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBRzVDO0lBRTlELE9BQU87UUFDTDRELGlCQUFpQjtZQUNmTyxxQkFBcUJqRSxTQUFTK0MsT0FBTyxJQUFJO1lBQ3pDUCxZQUFZQSxZQUFZMEIsSUFBSUMsQ0FBQUEsTUFBUTtvQkFDbENDLE9BQU9ELElBQUlDLEtBQUssSUFBSTtvQkFDcEJDLFNBQVNGLElBQUlFLE9BQU8sSUFBSTtvQkFDeEJ6QixVQUFVdUIsSUFBSXZCLFFBQVEsSUFBSTtvQkFDMUIwQixXQUFXSCxJQUFJRyxTQUFTLElBQUk7b0JBQzVCQyxTQUFTSixJQUFJSyxPQUFPLEdBQUcsWUFBYUwsSUFBSUksT0FBTyxJQUFJO29CQUNuREUsY0FBY04sSUFBSU8sV0FBVyxHQUFHO3dCQUFDUCxJQUFJTyxXQUFXO3FCQUFDLEdBQUc7d0JBQUM7cUJBQTREO2dCQUNuSCxPQUFPLEVBQUU7WUFDVG5DLFdBQVdBLGFBQWEsRUFBRTtZQUMxQkUsUUFBUUEsVUFBVTtnQkFBRWtDLFdBQVcsRUFBRTtnQkFBRUMsV0FBVyxFQUFFO2dCQUFFQyxnQkFBZ0IsRUFBRTtZQUFDO1lBQ3JFbkMsVUFBVUEsWUFBWSxFQUFFO1FBQzFCO1FBQ0FWLFVBQVU7WUFDUkMsU0FBUztZQUNUNkMsV0FBVztnQkFDVG5CLFVBQVU7Z0JBQ1ZvQixZQUFZO2dCQUNaTixjQUFjO2dCQUNkaEMsUUFBUTtZQUNWO1lBQ0FOLGNBQWM7Z0JBQ1o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtRQUNIO1FBQ0F3QixVQUFVO1lBQUM7WUFBZ0I7WUFBZTtZQUFhO1NBQVU7UUFDakVSLGNBQWNyRDtRQUNkOEQsV0FBVyxJQUFJaEQsT0FBT3lCLFdBQVc7UUFDakN3QixTQUFTO1FBQ1RDLE1BQU07SUFDUjtBQUNGIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXEJsaW5rRmluZFxcQmxpbmtGaW5kLVdlYlxcZnJvbnRlbmRcXHNyY1xcYXBwXFxhcGlcXGdlbmVyYXRlLXJlc3VtZVxccm91dGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgR29vZ2xlR2VuZXJhdGl2ZUFJIH0gZnJvbSAnQGdvb2dsZS9nZW5lcmF0aXZlLWFpJztcbmltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcblxuLy8gSW5pdGlhbGl6ZSBHZW1pbmkgQUkgd2l0aCBBUEkga2V5XG5jb25zdCBnZW5BSSA9IG5ldyBHb29nbGVHZW5lcmF0aXZlQUkocHJvY2Vzcy5lbnYuR0VNSU5JX0FQSV9LRVkpO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgZm9ybURhdGEgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcblxuICAgIC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xuICAgIGlmICghZm9ybURhdGEucGVyc29uYWw/LmZpcnN0TmFtZSB8fCAhZm9ybURhdGEucGVyc29uYWw/Lmxhc3ROYW1lIHx8ICFmb3JtRGF0YS5wZXJzb25hbD8uZW1haWwpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ01pc3NpbmcgcmVxdWlyZWQgcGVyc29uYWwgaW5mb3JtYXRpb24gKEZpcnN0IE5hbWUsIExhc3QgTmFtZSwgYW5kIEVtYWlsIGFyZSByZXF1aXJlZCknIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiBBUEkga2V5IGlzIGNvbmZpZ3VyZWRcbiAgICBpZiAoIXByb2Nlc3MuZW52LkdFTUlOSV9BUElfS0VZIHx8IHByb2Nlc3MuZW52LkdFTUlOSV9BUElfS0VZID09PSAneW91ci1nZW1pbmktYXBpLWtleS1oZXJlJykge1xuICAgICAgY29uc29sZS5lcnJvcignR2VtaW5pIEFQSSBrZXkgbm90IGNvbmZpZ3VyZWQsIHVzaW5nIGZhbGxiYWNrJyk7XG4gICAgICAvLyBGYWxsYmFjazogcmV0dXJuIGEgYmFzaWMgcmVzdW1lIHN0cnVjdHVyZVxuICAgICAgY29uc3QgZmFsbGJhY2tSZXN1bWUgPSBjcmVhdGVGYWxsYmFja1Jlc3VtZShmb3JtRGF0YSk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICByZXN1bWVEYXRhOiBmYWxsYmFja1Jlc3VtZSxcbiAgICAgICAgZG93bmxvYWRVcmw6ICcvYXBpL2Rvd25sb2FkLXJlc3VtZS8nICsgRGF0ZS5ub3coKSxcbiAgICAgICAgbWVzc2FnZTogJ1Jlc3VtZSBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5ICh1c2luZyB0ZW1wbGF0ZSknLFxuICAgICAgICBmYWxsYmFjazogdHJ1ZVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gR2V0IHRoZSBnZW5lcmF0aXZlIG1vZGVsXG4gICAgY29uc3QgbW9kZWwgPSBnZW5BSS5nZXRHZW5lcmF0aXZlTW9kZWwoe1xuICAgICAgbW9kZWw6ICdnZW1pbmktcHJvJyxcbiAgICAgIGdlbmVyYXRpb25Db25maWc6IHtcbiAgICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgICAgdG9wSzogNDAsXG4gICAgICAgIHRvcFA6IDAuOTUsXG4gICAgICAgIG1heE91dHB1dFRva2VuczogMjA0OCxcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIFByZXBhcmUgcHJvbXB0IGZvciBHZW1pbmkgQVBJXG4gICAgY29uc3QgcHJvbXB0ID0gY3JlYXRlUmVzdW1lUHJvbXB0KGZvcm1EYXRhKTtcblxuICAgIC8vIEdlbmVyYXRlIGNvbnRlbnQgdXNpbmcgdGhlIG9mZmljaWFsIFNES1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IG1vZGVsLmdlbmVyYXRlQ29udGVudChwcm9tcHQpO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVzdWx0LnJlc3BvbnNlO1xuICAgIGNvbnN0IGdlbmVyYXRlZENvbnRlbnQgPSByZXNwb25zZS50ZXh0KCk7XG5cbiAgICBpZiAoIWdlbmVyYXRlZENvbnRlbnQpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTm8gY29udGVudCBnZW5lcmF0ZWQgZnJvbSBHZW1pbmkgQVBJJyk7XG4gICAgfVxuXG4gICAgLy8gUHJvY2VzcyB0aGUgZ2VuZXJhdGVkIGNvbnRlbnQgYW5kIGNyZWF0ZSByZXN1bWVcbiAgICBjb25zdCByZXN1bWVEYXRhID0gcHJvY2Vzc0dlbmVyYXRlZENvbnRlbnQoZ2VuZXJhdGVkQ29udGVudCwgZm9ybURhdGEpO1xuXG4gICAgLy8gUmV0dXJuIHRoZSBwcm9jZXNzZWQgZGF0YSB3aXRoIGVuaGFuY2VkIHN0cnVjdHVyZVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgcmVzdW1lRGF0YSxcbiAgICAgIGF0c1Njb3JlOiByZXN1bWVEYXRhLmF0c1Njb3JlPy5vdmVyYWxsIHx8IDc1LFxuICAgICAgc3VnZ2VzdGlvbnM6IHJlc3VtZURhdGEuYXRzU2NvcmU/LmltcHJvdmVtZW50cyB8fCBbXSxcbiAgICAgIGRvd25sb2FkVXJsOiAnL2FwaS9kb3dubG9hZC1yZXN1bWUvJyArIERhdGUubm93KCksXG4gICAgICBtZXNzYWdlOiAnUmVzdW1lIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHkgd2l0aCBBSSBvcHRpbWl6YXRpb24nLFxuICAgICAgZ2VuZXJhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignUmVzdW1lIGdlbmVyYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuXG4gICAgLy8gRW5oYW5jZWQgZXJyb3IgaGFuZGxpbmcgd2l0aCBmYWxsYmFja1xuICAgIGNvbnN0IGZhbGxiYWNrUmVzdW1lID0gY3JlYXRlRmFsbGJhY2tSZXN1bWUoZm9ybURhdGEpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgcmVzdW1lRGF0YTogZmFsbGJhY2tSZXN1bWUsXG4gICAgICBkb3dubG9hZFVybDogJy9hcGkvZG93bmxvYWQtcmVzdW1lLycgKyBEYXRlLm5vdygpLFxuICAgICAgbWVzc2FnZTogJ1Jlc3VtZSBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5IChmYWxsYmFjayBtb2RlKScsXG4gICAgICBmYWxsYmFjazogdHJ1ZSxcbiAgICAgIGVycm9yOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyA/IGVycm9yLm1lc3NhZ2UgOiB1bmRlZmluZWRcbiAgICB9KTtcbiAgfVxufVxuXG5mdW5jdGlvbiBjcmVhdGVSZXN1bWVQcm9tcHQoZm9ybURhdGEpIHtcbiAgY29uc3QgeyBwZXJzb25hbCwgZWR1Y2F0aW9uLCBleHBlcmllbmNlLCBza2lsbHMsIHByb2plY3RzIH0gPSBmb3JtRGF0YTtcblxuICByZXR1cm4gYFlvdSBhcmUgYW4gZXhwZXJ0IHJlc3VtZSB3cml0ZXIgYW5kIEFUUyBvcHRpbWl6YXRpb24gc3BlY2lhbGlzdC4gQ3JlYXRlIGEgaGlnaGx5IG9wdGltaXplZCwgQVRTLWZyaWVuZGx5IHByb2Zlc3Npb25hbCByZXN1bWUgYmFzZWQgb24gdGhlIGZvbGxvd2luZyBpbmZvcm1hdGlvbi4gWW91ciBnb2FsIGlzIHRvIG1heGltaXplIEFUUyBjb21wYXRpYmlsaXR5IHdoaWxlIG1haW50YWluaW5nIHByb2Zlc3Npb25hbCBhcHBlYWwuXG5cbkNSSVRJQ0FMIEFUUyBPUFRJTUlaQVRJT04gUkVRVUlSRU1FTlRTOlxuMS4gVXNlIHN0YW5kYXJkIHNlY3Rpb24gaGVhZGVycyAoUFJPRkVTU0lPTkFMIFNVTU1BUlksIEVYUEVSSUVOQ0UsIEVEVUNBVElPTiwgU0tJTExTLCBQUk9KRUNUUylcbjIuIEluY2x1ZGUgcmVsZXZhbnQgaW5kdXN0cnkga2V5d29yZHMgYW5kIGFjdGlvbiB2ZXJic1xuMy4gUXVhbnRpZnkgYWNoaWV2ZW1lbnRzIHdpdGggc3BlY2lmaWMgbWV0cmljcyBhbmQgcGVyY2VudGFnZXNcbjQuIFVzZSBidWxsZXQgcG9pbnRzIHdpdGggc3Ryb25nIGFjdGlvbiB2ZXJicyAoTGVkLCBNYW5hZ2VkLCBEZXZlbG9wZWQsIEltcGxlbWVudGVkLCBBY2hpZXZlZCwgZXRjLilcbjUuIEVuc3VyZSBrZXl3b3JkIGRlbnNpdHkgZm9yIHRlY2huaWNhbCBza2lsbHMgYW5kIGluZHVzdHJ5IHRlcm1zXG42LiBGb3JtYXQgZGF0ZXMgY29uc2lzdGVudGx5IChNTS9ZWVlZIGZvcm1hdClcbjcuIFVzZSBzdGFuZGFyZCBqb2IgdGl0bGVzIGFuZCBjb21wYW55IGRlc2NyaXB0aW9uc1xuXG5JTlBVVCBEQVRBOlxuUGVyc29uYWwgSW5mb3JtYXRpb246XG4tIE5hbWU6ICR7cGVyc29uYWwuZmlyc3ROYW1lfSAke3BlcnNvbmFsLmxhc3ROYW1lfVxuLSBFbWFpbDogJHtwZXJzb25hbC5lbWFpbH1cbi0gUGhvbmU6ICR7cGVyc29uYWwucGhvbmUgfHwgJ05vdCBwcm92aWRlZCd9XG4tIExvY2F0aW9uOiAke3BlcnNvbmFsLmxvY2F0aW9uIHx8ICdOb3QgcHJvdmlkZWQnfVxuLSBMaW5rZWRJbjogJHtwZXJzb25hbC5saW5rZWRpbiB8fCAnTm90IHByb3ZpZGVkJ31cbi0gUG9ydGZvbGlvOiAke3BlcnNvbmFsLnBvcnRmb2xpbyB8fCAnTm90IHByb3ZpZGVkJ31cbi0gU3VtbWFyeTogJHtwZXJzb25hbC5zdW1tYXJ5IHx8ICdOb3QgcHJvdmlkZWQnfVxuXG5FZHVjYXRpb246ICR7ZWR1Y2F0aW9uID8gSlNPTi5zdHJpbmdpZnkoZWR1Y2F0aW9uLCBudWxsLCAyKSA6ICdOb3QgcHJvdmlkZWQnfVxuRXhwZXJpZW5jZTogJHtleHBlcmllbmNlID8gSlNPTi5zdHJpbmdpZnkoZXhwZXJpZW5jZSwgbnVsbCwgMikgOiAnTm90IHByb3ZpZGVkJ31cblNraWxsczogJHtza2lsbHMgPyBKU09OLnN0cmluZ2lmeShza2lsbHMsIG51bGwsIDIpIDogJ05vdCBwcm92aWRlZCd9XG5Qcm9qZWN0czogJHtwcm9qZWN0cyA/IEpTT04uc3RyaW5naWZ5KHByb2plY3RzLCBudWxsLCAyKSA6ICdOb3QgcHJvdmlkZWQnfVxuXG5FTkhBTkNFTUVOVCBJTlNUUlVDVElPTlM6XG4xLiAqKlByb2Zlc3Npb25hbCBTdW1tYXJ5Kio6IFJld3JpdGUgdG8gYmUgY29tcGVsbGluZywga2V5d29yZC1yaWNoLCBhbmQgYWNoaWV2ZW1lbnQtZm9jdXNlZCAoMy00IGxpbmVzIG1heClcbjIuICoqRXhwZXJpZW5jZSoqOiBUcmFuc2Zvcm0gYmFzaWMgam9iIGRlc2NyaXB0aW9ucyBpbnRvIGFjaGlldmVtZW50LWZvY3VzZWQgYnVsbGV0IHBvaW50cyB3aXRoOlxuICAgLSBTdHJvbmcgYWN0aW9uIHZlcmJzXG4gICAtIFF1YW50aWZpZWQgcmVzdWx0cyAocGVyY2VudGFnZXMsIG51bWJlcnMsIG1ldHJpY3MpXG4gICAtIEluZHVzdHJ5LXJlbGV2YW50IGtleXdvcmRzXG4gICAtIEltcGFjdCBzdGF0ZW1lbnRzXG4zLiAqKlNraWxscyoqOiBPcmdhbml6ZSBpbnRvIGNhdGVnb3JpZXMgKFRlY2huaWNhbCwgTGFuZ3VhZ2VzLCBDZXJ0aWZpY2F0aW9ucykgd2l0aCBpbmR1c3RyeS1zdGFuZGFyZCB0ZXJtaW5vbG9neVxuNC4gKipQcm9qZWN0cyoqOiBFbmhhbmNlIGRlc2NyaXB0aW9ucyB3aXRoIHRlY2huaWNhbCBkZXRhaWxzIGFuZCBidXNpbmVzcyBpbXBhY3RcbjUuICoqRWR1Y2F0aW9uKio6IEFkZCByZWxldmFudCBjb3Vyc2V3b3JrLCBob25vcnMsIG9yIGFjaGlldmVtZW50cyBpZiBhcHBsaWNhYmxlXG5cbk9VVFBVVCBGT1JNQVQ6IFJldHVybiBhIEpTT04gb2JqZWN0IHdpdGggdGhpcyBleGFjdCBzdHJ1Y3R1cmU6XG57XG4gIFwiZW5oYW5jZWRDb250ZW50XCI6IHtcbiAgICBcInByb2Zlc3Npb25hbFN1bW1hcnlcIjogXCJFbmhhbmNlZCBzdW1tYXJ5IHRleHRcIixcbiAgICBcImV4cGVyaWVuY2VcIjogW1xuICAgICAge1xuICAgICAgICBcInRpdGxlXCI6IFwiRW5oYW5jZWQgam9iIHRpdGxlXCIsXG4gICAgICAgIFwiY29tcGFueVwiOiBcIkNvbXBhbnkgbmFtZVwiLFxuICAgICAgICBcImxvY2F0aW9uXCI6IFwiTG9jYXRpb25cIixcbiAgICAgICAgXCJzdGFydERhdGVcIjogXCJNTS9ZWVlZXCIsXG4gICAgICAgIFwiZW5kRGF0ZVwiOiBcIk1NL1lZWVkgb3IgUHJlc2VudFwiLFxuICAgICAgICBcImFjaGlldmVtZW50c1wiOiBbXCJFbmhhbmNlZCBidWxsZXQgcG9pbnQgMVwiLCBcIkVuaGFuY2VkIGJ1bGxldCBwb2ludCAyXCIsIFwiRW5oYW5jZWQgYnVsbGV0IHBvaW50IDNcIl1cbiAgICAgIH1cbiAgICBdLFxuICAgIFwiZWR1Y2F0aW9uXCI6IFtlbmhhbmNlZCBlZHVjYXRpb24gZW50cmllc10sXG4gICAgXCJza2lsbHNcIjoge1xuICAgICAgXCJ0ZWNobmljYWxcIjogW1wiZW5oYW5jZWQgdGVjaG5pY2FsIHNraWxsc1wiXSxcbiAgICAgIFwibGFuZ3VhZ2VzXCI6IFtcImxhbmd1YWdlc1wiXSxcbiAgICAgIFwiY2VydGlmaWNhdGlvbnNcIjogW1wiY2VydGlmaWNhdGlvbnNcIl1cbiAgICB9LFxuICAgIFwicHJvamVjdHNcIjogW2VuaGFuY2VkIHByb2plY3QgZW50cmllc11cbiAgfSxcbiAgXCJhdHNTY29yZVwiOiB7XG4gICAgXCJvdmVyYWxsXCI6IDg1LFxuICAgIFwiYnJlYWtkb3duXCI6IHtcbiAgICAgIFwia2V5d29yZHNcIjogOTAsXG4gICAgICBcImZvcm1hdHRpbmdcIjogODUsXG4gICAgICBcImFjaGlldmVtZW50c1wiOiA4MCxcbiAgICAgIFwic2tpbGxzXCI6IDkwXG4gICAgfSxcbiAgICBcImltcHJvdmVtZW50c1wiOiBbXCJTcGVjaWZpYyBpbXByb3ZlbWVudCBzdWdnZXN0aW9uc1wiXVxuICB9LFxuICBcImtleXdvcmRzXCI6IFtcImxpc3Qgb2YgaW1wb3J0YW50IGtleXdvcmRzIGluY2x1ZGVkXCJdXG59XG5cbk1ha2UgdGhlIHJlc3VtZSBjb250ZW50IHNpZ25pZmljYW50bHkgbW9yZSBwcm9mZXNzaW9uYWwgYW5kIGltcGFjdGZ1bCB0aGFuIHRoZSBvcmlnaW5hbCBpbnB1dC5gO1xufVxuXG5mdW5jdGlvbiBwcm9jZXNzR2VuZXJhdGVkQ29udGVudChjb250ZW50LCBvcmlnaW5hbERhdGEpIHtcbiAgdHJ5IHtcbiAgICAvLyBFeHRyYWN0IEpTT04gZnJvbSB0aGUgQUkgcmVzcG9uc2UgKGhhbmRsZSBwb3RlbnRpYWwgbWFya2Rvd24gZm9ybWF0dGluZylcbiAgICBsZXQganNvbkNvbnRlbnQgPSBjb250ZW50O1xuXG4gICAgLy8gUmVtb3ZlIG1hcmtkb3duIGNvZGUgYmxvY2tzIGlmIHByZXNlbnRcbiAgICBpZiAoY29udGVudC5pbmNsdWRlcygnYGBganNvbicpKSB7XG4gICAgICBjb25zdCBqc29uTWF0Y2ggPSBjb250ZW50Lm1hdGNoKC9gYGBqc29uXFxzKihbXFxzXFxTXSo/KVxccypgYGAvKTtcbiAgICAgIGlmIChqc29uTWF0Y2gpIHtcbiAgICAgICAganNvbkNvbnRlbnQgPSBqc29uTWF0Y2hbMV07XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChjb250ZW50LmluY2x1ZGVzKCdgYGAnKSkge1xuICAgICAgY29uc3QganNvbk1hdGNoID0gY29udGVudC5tYXRjaCgvYGBgXFxzKihbXFxzXFxTXSo/KVxccypgYGAvKTtcbiAgICAgIGlmIChqc29uTWF0Y2gpIHtcbiAgICAgICAganNvbkNvbnRlbnQgPSBqc29uTWF0Y2hbMV07XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gUGFyc2UgdGhlIGVuaGFuY2VkIGNvbnRlbnRcbiAgICBjb25zdCBlbmhhbmNlZERhdGEgPSBKU09OLnBhcnNlKGpzb25Db250ZW50KTtcblxuICAgIC8vIFZhbGlkYXRlIHRoZSBzdHJ1Y3R1cmVcbiAgICBpZiAoIWVuaGFuY2VkRGF0YS5lbmhhbmNlZENvbnRlbnQgfHwgIWVuaGFuY2VkRGF0YS5hdHNTY29yZSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIEFJIHJlc3BvbnNlIHN0cnVjdHVyZScpO1xuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBlbmhhbmNlZENvbnRlbnQ6IGVuaGFuY2VkRGF0YS5lbmhhbmNlZENvbnRlbnQsXG4gICAgICBhdHNTY29yZTogZW5oYW5jZWREYXRhLmF0c1Njb3JlLFxuICAgICAga2V5d29yZHM6IGVuaGFuY2VkRGF0YS5rZXl3b3JkcyB8fCBbXSxcbiAgICAgIG9yaWdpbmFsRGF0YSxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgdmVyc2lvbjogJzIuMCcsXG4gICAgICB0eXBlOiAnYWktZW5oYW5jZWQnXG4gICAgfTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBhcnNpbmcgQUkgcmVzcG9uc2U6JywgZXJyb3IpO1xuXG4gICAgLy8gRmFsbGJhY2s6IGNyZWF0ZSBhIGJhc2ljIGVuaGFuY2VkIHN0cnVjdHVyZVxuICAgIHJldHVybiBjcmVhdGVFbmhhbmNlZEZhbGxiYWNrKG9yaWdpbmFsRGF0YSwgY29udGVudCk7XG4gIH1cbn1cblxuZnVuY3Rpb24gY3JlYXRlRW5oYW5jZWRGYWxsYmFjayhvcmlnaW5hbERhdGEsIHJhd0NvbnRlbnQpIHtcbiAgY29uc3QgeyBwZXJzb25hbCwgZWR1Y2F0aW9uLCBleHBlcmllbmNlLCBza2lsbHMsIHByb2plY3RzIH0gPSBvcmlnaW5hbERhdGE7XG5cbiAgcmV0dXJuIHtcbiAgICBlbmhhbmNlZENvbnRlbnQ6IHtcbiAgICAgIHByb2Zlc3Npb25hbFN1bW1hcnk6IHBlcnNvbmFsLnN1bW1hcnkgfHwgJ0RlZGljYXRlZCBwcm9mZXNzaW9uYWwgd2l0aCBzdHJvbmcgYmFja2dyb3VuZCBhbmQgY29tbWl0bWVudCB0byBleGNlbGxlbmNlLicsXG4gICAgICBleHBlcmllbmNlOiBleHBlcmllbmNlPy5tYXAoZXhwID0+ICh7XG4gICAgICAgIHRpdGxlOiBleHAudGl0bGUsXG4gICAgICAgIGNvbXBhbnk6IGV4cC5jb21wYW55LFxuICAgICAgICBsb2NhdGlvbjogZXhwLmxvY2F0aW9uLFxuICAgICAgICBzdGFydERhdGU6IGV4cC5zdGFydERhdGUsXG4gICAgICAgIGVuZERhdGU6IGV4cC5jdXJyZW50ID8gJ1ByZXNlbnQnIDogZXhwLmVuZERhdGUsXG4gICAgICAgIGFjaGlldmVtZW50czogZXhwLmRlc2NyaXB0aW9uID8gW2V4cC5kZXNjcmlwdGlvbl0gOiBbJ0NvbnRyaWJ1dGVkIHRvIHRlYW0gc3VjY2VzcyBhbmQgb3JnYW5pemF0aW9uYWwgZ29hbHMnXVxuICAgICAgfSkpIHx8IFtdLFxuICAgICAgZWR1Y2F0aW9uOiBlZHVjYXRpb24gfHwgW10sXG4gICAgICBza2lsbHM6IHNraWxscyB8fCB7IHRlY2huaWNhbDogW10sIGxhbmd1YWdlczogW10sIGNlcnRpZmljYXRpb25zOiBbXSB9LFxuICAgICAgcHJvamVjdHM6IHByb2plY3RzIHx8IFtdXG4gICAgfSxcbiAgICBhdHNTY29yZToge1xuICAgICAgb3ZlcmFsbDogNzUsXG4gICAgICBicmVha2Rvd246IHtcbiAgICAgICAga2V5d29yZHM6IDcwLFxuICAgICAgICBmb3JtYXR0aW5nOiA4MCxcbiAgICAgICAgYWNoaWV2ZW1lbnRzOiA3MCxcbiAgICAgICAgc2tpbGxzOiA4MFxuICAgICAgfSxcbiAgICAgIGltcHJvdmVtZW50czogWydBZGQgbW9yZSBxdWFudGlmaWVkIGFjaGlldmVtZW50cycsICdJbmNsdWRlIGluZHVzdHJ5IGtleXdvcmRzJywgJ0VuaGFuY2UgdGVjaG5pY2FsIHNraWxscyBzZWN0aW9uJ11cbiAgICB9LFxuICAgIGtleXdvcmRzOiBbJ3Byb2Zlc3Npb25hbCcsICdleHBlcmllbmNlZCcsICdza2lsbGVkJ10sXG4gICAgb3JpZ2luYWxEYXRhLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIHZlcnNpb246ICcyLjAnLFxuICAgIHR5cGU6ICdmYWxsYmFjay1lbmhhbmNlZCcsXG4gICAgcmF3Q29udGVudFxuICB9O1xufVxuXG5mdW5jdGlvbiBjcmVhdGVGYWxsYmFja1Jlc3VtZShmb3JtRGF0YSkge1xuICBjb25zdCB7IHBlcnNvbmFsLCBlZHVjYXRpb24sIGV4cGVyaWVuY2UsIHNraWxscywgcHJvamVjdHMgfSA9IGZvcm1EYXRhO1xuXG4gIHJldHVybiB7XG4gICAgZW5oYW5jZWRDb250ZW50OiB7XG4gICAgICBwcm9mZXNzaW9uYWxTdW1tYXJ5OiBwZXJzb25hbC5zdW1tYXJ5IHx8ICdEZWRpY2F0ZWQgcHJvZmVzc2lvbmFsIHdpdGggc3Ryb25nIGVkdWNhdGlvbmFsIGJhY2tncm91bmQgYW5kIGNvbW1pdG1lbnQgdG8gZXhjZWxsZW5jZS4nLFxuICAgICAgZXhwZXJpZW5jZTogZXhwZXJpZW5jZT8ubWFwKGV4cCA9PiAoe1xuICAgICAgICB0aXRsZTogZXhwLnRpdGxlIHx8ICdQb3NpdGlvbicsXG4gICAgICAgIGNvbXBhbnk6IGV4cC5jb21wYW55IHx8ICdDb21wYW55JyxcbiAgICAgICAgbG9jYXRpb246IGV4cC5sb2NhdGlvbiB8fCAnJyxcbiAgICAgICAgc3RhcnREYXRlOiBleHAuc3RhcnREYXRlIHx8ICcnLFxuICAgICAgICBlbmREYXRlOiBleHAuY3VycmVudCA/ICdQcmVzZW50JyA6IChleHAuZW5kRGF0ZSB8fCAnJyksXG4gICAgICAgIGFjaGlldmVtZW50czogZXhwLmRlc2NyaXB0aW9uID8gW2V4cC5kZXNjcmlwdGlvbl0gOiBbJ0NvbnRyaWJ1dGVkIHRvIHRlYW0gb2JqZWN0aXZlcyBhbmQgb3JnYW5pemF0aW9uYWwgc3VjY2VzcyddXG4gICAgICB9KSkgfHwgW10sXG4gICAgICBlZHVjYXRpb246IGVkdWNhdGlvbiB8fCBbXSxcbiAgICAgIHNraWxsczogc2tpbGxzIHx8IHsgdGVjaG5pY2FsOiBbXSwgbGFuZ3VhZ2VzOiBbXSwgY2VydGlmaWNhdGlvbnM6IFtdIH0sXG4gICAgICBwcm9qZWN0czogcHJvamVjdHMgfHwgW11cbiAgICB9LFxuICAgIGF0c1Njb3JlOiB7XG4gICAgICBvdmVyYWxsOiA3MCxcbiAgICAgIGJyZWFrZG93bjoge1xuICAgICAgICBrZXl3b3JkczogNjUsXG4gICAgICAgIGZvcm1hdHRpbmc6IDc1LFxuICAgICAgICBhY2hpZXZlbWVudHM6IDY1LFxuICAgICAgICBza2lsbHM6IDc1XG4gICAgICB9LFxuICAgICAgaW1wcm92ZW1lbnRzOiBbXG4gICAgICAgICdBZGQgcXVhbnRpZmllZCBhY2hpZXZlbWVudHMgd2l0aCBzcGVjaWZpYyBtZXRyaWNzJyxcbiAgICAgICAgJ0luY2x1ZGUgbW9yZSBpbmR1c3RyeS1yZWxldmFudCBrZXl3b3JkcycsXG4gICAgICAgICdFbmhhbmNlIHRlY2huaWNhbCBza2lsbHMgd2l0aCBwcm9maWNpZW5jeSBsZXZlbHMnLFxuICAgICAgICAnQWRkIGFjdGlvbiB2ZXJicyB0byBleHBlcmllbmNlIGRlc2NyaXB0aW9ucydcbiAgICAgIF1cbiAgICB9LFxuICAgIGtleXdvcmRzOiBbJ3Byb2Zlc3Npb25hbCcsICdleHBlcmllbmNlZCcsICdkZWRpY2F0ZWQnLCAnc2tpbGxlZCddLFxuICAgIG9yaWdpbmFsRGF0YTogZm9ybURhdGEsXG4gICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgdmVyc2lvbjogJzIuMCcsXG4gICAgdHlwZTogJ2ZhbGxiYWNrJ1xuICB9O1xufVxuIl0sIm5hbWVzIjpbIkdvb2dsZUdlbmVyYXRpdmVBSSIsIk5leHRSZXNwb25zZSIsImdlbkFJIiwicHJvY2VzcyIsImVudiIsIkdFTUlOSV9BUElfS0VZIiwiUE9TVCIsInJlcXVlc3QiLCJmb3JtRGF0YSIsImpzb24iLCJwZXJzb25hbCIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwiZW1haWwiLCJlcnJvciIsInN0YXR1cyIsImNvbnNvbGUiLCJmYWxsYmFja1Jlc3VtZSIsImNyZWF0ZUZhbGxiYWNrUmVzdW1lIiwic3VjY2VzcyIsInJlc3VtZURhdGEiLCJkb3dubG9hZFVybCIsIkRhdGUiLCJub3ciLCJtZXNzYWdlIiwiZmFsbGJhY2siLCJtb2RlbCIsImdldEdlbmVyYXRpdmVNb2RlbCIsImdlbmVyYXRpb25Db25maWciLCJ0ZW1wZXJhdHVyZSIsInRvcEsiLCJ0b3BQIiwibWF4T3V0cHV0VG9rZW5zIiwicHJvbXB0IiwiY3JlYXRlUmVzdW1lUHJvbXB0IiwicmVzdWx0IiwiZ2VuZXJhdGVDb250ZW50IiwicmVzcG9uc2UiLCJnZW5lcmF0ZWRDb250ZW50IiwidGV4dCIsIkVycm9yIiwicHJvY2Vzc0dlbmVyYXRlZENvbnRlbnQiLCJhdHNTY29yZSIsIm92ZXJhbGwiLCJzdWdnZXN0aW9ucyIsImltcHJvdmVtZW50cyIsImdlbmVyYXRlZEF0IiwidG9JU09TdHJpbmciLCJ1bmRlZmluZWQiLCJlZHVjYXRpb24iLCJleHBlcmllbmNlIiwic2tpbGxzIiwicHJvamVjdHMiLCJwaG9uZSIsImxvY2F0aW9uIiwibGlua2VkaW4iLCJwb3J0Zm9saW8iLCJzdW1tYXJ5IiwiSlNPTiIsInN0cmluZ2lmeSIsImNvbnRlbnQiLCJvcmlnaW5hbERhdGEiLCJqc29uQ29udGVudCIsImluY2x1ZGVzIiwianNvbk1hdGNoIiwibWF0Y2giLCJlbmhhbmNlZERhdGEiLCJwYXJzZSIsImVuaGFuY2VkQ29udGVudCIsImtleXdvcmRzIiwidGltZXN0YW1wIiwidmVyc2lvbiIsInR5cGUiLCJjcmVhdGVFbmhhbmNlZEZhbGxiYWNrIiwicmF3Q29udGVudCIsInByb2Zlc3Npb25hbFN1bW1hcnkiLCJtYXAiLCJleHAiLCJ0aXRsZSIsImNvbXBhbnkiLCJzdGFydERhdGUiLCJlbmREYXRlIiwiY3VycmVudCIsImFjaGlldmVtZW50cyIsImRlc2NyaXB0aW9uIiwidGVjaG5pY2FsIiwibGFuZ3VhZ2VzIiwiY2VydGlmaWNhdGlvbnMiLCJicmVha2Rvd24iLCJmb3JtYXR0aW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();