/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-resume/route";
exports.ids = ["app/api/generate-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-resume/route.js */ \"(rsc)/./src/app/api/generate-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-resume/route\",\n        pathname: \"/api/generate-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\generate-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-resume/route.js":
/*!**********************************************!*\
  !*** ./src/app/api/generate-resume/route.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Note: Replace with your actual Gemini API integration\n// This is a placeholder structure for the Gemini API integration\nconst GEMINI_API_KEY = process.env.GEMINI_API_KEY;\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';\nasync function POST(request) {\n    try {\n        const formData = await request.json();\n        // Validate required fields\n        if (!formData.personal?.name || !formData.personal?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required personal information'\n            }, {\n                status: 400\n            });\n        }\n        // Check if API key is configured\n        if (!GEMINI_API_KEY || GEMINI_API_KEY === 'your_actual_gemini_api_key_here') {\n            console.error('Gemini API key not configured, using fallback');\n            // Fallback: return a basic resume structure\n            const fallbackResume = createFallbackResume(formData);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                resumeData: fallbackResume,\n                downloadUrl: '/api/download-resume/' + Date.now(),\n                message: 'Resume generated successfully (using template)',\n                fallback: true\n            });\n        }\n        // Prepare prompt for Gemini API\n        const prompt = createResumePrompt(formData);\n        // Call Gemini API\n        const geminiResponse = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                contents: [\n                    {\n                        parts: [\n                            {\n                                text: prompt\n                            }\n                        ]\n                    }\n                ],\n                generationConfig: {\n                    temperature: 0.7,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 2048\n                }\n            })\n        });\n        if (!geminiResponse.ok) {\n            throw new Error(`Gemini API error: ${geminiResponse.status}`);\n        }\n        const geminiData = await geminiResponse.json();\n        const generatedContent = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;\n        if (!generatedContent) {\n            throw new Error('No content generated from Gemini API');\n        }\n        // Process the generated content and create resume\n        const resumeData = processGeneratedContent(generatedContent, formData);\n        // In a real implementation, you would:\n        // 1. Generate PDF from the resume data\n        // 2. Store it in cloud storage\n        // 3. Return the download URL\n        // For now, return the processed data\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            resumeData,\n            // Mock URL - replace with actual PDF generation\n            downloadUrl: '/api/download-resume/' + Date.now(),\n            message: 'Resume generated successfully'\n        });\n    } catch (error) {\n        console.error('Resume generation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate resume',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createResumePrompt(formData) {\n    const { personal, education, experience, skills } = formData;\n    return `Create a professional resume based on the following information:\n\nPersonal Information:\n- Name: ${personal.name}\n- Email: ${personal.email}\n- Phone: ${personal.phone || 'Not provided'}\n\nEducation:\n- Degree: ${education.degree || 'Not provided'}\n- Institution: ${education.institution || 'Not provided'}\n- Field of Study: ${education.field || 'Not provided'}\n- Graduation Year: ${education.graduationYear || 'Not provided'}\n\nExperience: ${experience ? JSON.stringify(experience) : 'Not provided'}\nSkills: ${skills ? JSON.stringify(skills) : 'Not provided'}\n\nPlease generate a well-structured, professional resume in a clean format. Include:\n1. A professional summary\n2. Work experience with bullet points highlighting achievements\n3. Education details\n4. Skills section\n5. Any additional relevant sections\n\nMake it ATS-friendly and professional. Return the content in a structured format that can be easily converted to PDF.`;\n}\nfunction processGeneratedContent(content, originalData) {\n    // Process the Gemini-generated content\n    // This would typically involve parsing the AI response and structuring it\n    return {\n        generatedContent: content,\n        originalData,\n        timestamp: new Date().toISOString(),\n        version: '1.0'\n    };\n}\nfunction createFallbackResume(formData) {\n    const { personal, education } = formData;\n    return {\n        generatedContent: `\n# ${personal.name}\n**Email:** ${personal.email}\n**Phone:** ${personal.phone || 'Not provided'}\n\n## Professional Summary\nDedicated professional with strong educational background and commitment to excellence.\n\n## Education\n**${education.degree || 'Degree'}** in ${education.field || 'Field of Study'}\n${education.institution || 'Institution'} | ${education.graduationYear || 'Year'}\n\n## Skills\n- Strong communication skills\n- Problem-solving abilities\n- Team collaboration\n- Attention to detail\n\n## Experience\nReady to contribute to a dynamic team and grow professionally.\n    `,\n        originalData: formData,\n        timestamp: new Date().toISOString(),\n        version: '1.0',\n        type: 'fallback'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS1yZXN1bWUvcm91dGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkM7QUFFM0Msd0RBQXdEO0FBQ3hELGlFQUFpRTtBQUNqRSxNQUFNQyxpQkFBaUJDLFFBQVFDLEdBQUcsQ0FBQ0YsY0FBYztBQUNqRCxNQUFNRyxpQkFBaUI7QUFFaEIsZUFBZUMsS0FBS0MsT0FBTztJQUNoQyxJQUFJO1FBQ0YsTUFBTUMsV0FBVyxNQUFNRCxRQUFRRSxJQUFJO1FBRW5DLDJCQUEyQjtRQUMzQixJQUFJLENBQUNELFNBQVNFLFFBQVEsRUFBRUMsUUFBUSxDQUFDSCxTQUFTRSxRQUFRLEVBQUVFLE9BQU87WUFDekQsT0FBT1gscURBQVlBLENBQUNRLElBQUksQ0FDdEI7Z0JBQUVJLE9BQU87WUFBd0MsR0FDakQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLGlDQUFpQztRQUNqQyxJQUFJLENBQUNaLGtCQUFrQkEsbUJBQW1CLG1DQUFtQztZQUMzRWEsUUFBUUYsS0FBSyxDQUFDO1lBQ2QsNENBQTRDO1lBQzVDLE1BQU1HLGlCQUFpQkMscUJBQXFCVDtZQUM1QyxPQUFPUCxxREFBWUEsQ0FBQ1EsSUFBSSxDQUFDO2dCQUN2QlMsU0FBUztnQkFDVEMsWUFBWUg7Z0JBQ1pJLGFBQWEsMEJBQTBCQyxLQUFLQyxHQUFHO2dCQUMvQ0MsU0FBUztnQkFDVEMsVUFBVTtZQUNaO1FBQ0Y7UUFFQSxnQ0FBZ0M7UUFDaEMsTUFBTUMsU0FBU0MsbUJBQW1CbEI7UUFFbEMsa0JBQWtCO1FBQ2xCLE1BQU1tQixpQkFBaUIsTUFBTUMsTUFBTSxHQUFHdkIsZUFBZSxLQUFLLEVBQUVILGdCQUFnQixFQUFFO1lBQzVFMkIsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ25CQyxVQUFVO29CQUFDO3dCQUNUQyxPQUFPOzRCQUFDO2dDQUNOQyxNQUFNWDs0QkFDUjt5QkFBRTtvQkFDSjtpQkFBRTtnQkFDRlksa0JBQWtCO29CQUNoQkMsYUFBYTtvQkFDYkMsTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsaUJBQWlCO2dCQUNuQjtZQUNGO1FBQ0Y7UUFFQSxJQUFJLENBQUNkLGVBQWVlLEVBQUUsRUFBRTtZQUN0QixNQUFNLElBQUlDLE1BQU0sQ0FBQyxrQkFBa0IsRUFBRWhCLGVBQWViLE1BQU0sRUFBRTtRQUM5RDtRQUVBLE1BQU04QixhQUFhLE1BQU1qQixlQUFlbEIsSUFBSTtRQUM1QyxNQUFNb0MsbUJBQW1CRCxXQUFXRSxVQUFVLEVBQUUsQ0FBQyxFQUFFLEVBQUVDLFNBQVNaLE9BQU8sQ0FBQyxFQUFFLEVBQUVDO1FBRTFFLElBQUksQ0FBQ1Msa0JBQWtCO1lBQ3JCLE1BQU0sSUFBSUYsTUFBTTtRQUNsQjtRQUVBLGtEQUFrRDtRQUNsRCxNQUFNeEIsYUFBYTZCLHdCQUF3Qkgsa0JBQWtCckM7UUFFN0QsdUNBQXVDO1FBQ3ZDLHVDQUF1QztRQUN2QywrQkFBK0I7UUFDL0IsNkJBQTZCO1FBRTdCLHFDQUFxQztRQUNyQyxPQUFPUCxxREFBWUEsQ0FBQ1EsSUFBSSxDQUFDO1lBQ3ZCUyxTQUFTO1lBQ1RDO1lBQ0EsZ0RBQWdEO1lBQ2hEQyxhQUFhLDBCQUEwQkMsS0FBS0MsR0FBRztZQUMvQ0MsU0FBUztRQUNYO0lBRUYsRUFBRSxPQUFPVixPQUFPO1FBQ2RFLFFBQVFGLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE9BQU9aLHFEQUFZQSxDQUFDUSxJQUFJLENBQ3RCO1lBQUVJLE9BQU87WUFBNkJvQyxTQUFTcEMsTUFBTVUsT0FBTztRQUFDLEdBQzdEO1lBQUVULFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsU0FBU1ksbUJBQW1CbEIsUUFBUTtJQUNsQyxNQUFNLEVBQUVFLFFBQVEsRUFBRXdDLFNBQVMsRUFBRUMsVUFBVSxFQUFFQyxNQUFNLEVBQUUsR0FBRzVDO0lBRXBELE9BQU8sQ0FBQzs7O1FBR0YsRUFBRUUsU0FBU0MsSUFBSSxDQUFDO1NBQ2YsRUFBRUQsU0FBU0UsS0FBSyxDQUFDO1NBQ2pCLEVBQUVGLFNBQVMyQyxLQUFLLElBQUksZUFBZTs7O1VBR2xDLEVBQUVILFVBQVVJLE1BQU0sSUFBSSxlQUFlO2VBQ2hDLEVBQUVKLFVBQVVLLFdBQVcsSUFBSSxlQUFlO2tCQUN2QyxFQUFFTCxVQUFVTSxLQUFLLElBQUksZUFBZTttQkFDbkMsRUFBRU4sVUFBVU8sY0FBYyxJQUFJLGVBQWU7O1lBRXBELEVBQUVOLGFBQWFuQixLQUFLQyxTQUFTLENBQUNrQixjQUFjLGVBQWU7UUFDL0QsRUFBRUMsU0FBU3BCLEtBQUtDLFNBQVMsQ0FBQ21CLFVBQVUsZUFBZTs7Ozs7Ozs7O3FIQVMwRCxDQUFDO0FBQ3RIO0FBRUEsU0FBU0osd0JBQXdCRCxPQUFPLEVBQUVXLFlBQVk7SUFDcEQsdUNBQXVDO0lBQ3ZDLDBFQUEwRTtJQUMxRSxPQUFPO1FBQ0xiLGtCQUFrQkU7UUFDbEJXO1FBQ0FDLFdBQVcsSUFBSXRDLE9BQU91QyxXQUFXO1FBQ2pDQyxTQUFTO0lBQ1g7QUFDRjtBQUVBLFNBQVM1QyxxQkFBcUJULFFBQVE7SUFDcEMsTUFBTSxFQUFFRSxRQUFRLEVBQUV3QyxTQUFTLEVBQUUsR0FBRzFDO0lBRWhDLE9BQU87UUFDTHFDLGtCQUFrQixDQUFDO0VBQ3JCLEVBQUVuQyxTQUFTQyxJQUFJLENBQUM7V0FDUCxFQUFFRCxTQUFTRSxLQUFLLENBQUM7V0FDakIsRUFBRUYsU0FBUzJDLEtBQUssSUFBSSxlQUFlOzs7Ozs7RUFNNUMsRUFBRUgsVUFBVUksTUFBTSxJQUFJLFNBQVMsTUFBTSxFQUFFSixVQUFVTSxLQUFLLElBQUksaUJBQWlCO0FBQzdFLEVBQUVOLFVBQVVLLFdBQVcsSUFBSSxjQUFjLEdBQUcsRUFBRUwsVUFBVU8sY0FBYyxJQUFJLE9BQU87Ozs7Ozs7Ozs7SUFVN0UsQ0FBQztRQUNEQyxjQUFjbEQ7UUFDZG1ELFdBQVcsSUFBSXRDLE9BQU91QyxXQUFXO1FBQ2pDQyxTQUFTO1FBQ1RDLE1BQU07SUFDUjtBQUNGIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXEJsaW5rRmluZFxcQmxpbmtGaW5kLVdlYlxcZnJvbnRlbmRcXHNyY1xcYXBwXFxhcGlcXGdlbmVyYXRlLXJlc3VtZVxccm91dGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuXG4vLyBOb3RlOiBSZXBsYWNlIHdpdGggeW91ciBhY3R1YWwgR2VtaW5pIEFQSSBpbnRlZ3JhdGlvblxuLy8gVGhpcyBpcyBhIHBsYWNlaG9sZGVyIHN0cnVjdHVyZSBmb3IgdGhlIEdlbWluaSBBUEkgaW50ZWdyYXRpb25cbmNvbnN0IEdFTUlOSV9BUElfS0VZID0gcHJvY2Vzcy5lbnYuR0VNSU5JX0FQSV9LRVk7XG5jb25zdCBHRU1JTklfQVBJX1VSTCA9ICdodHRwczovL2dlbmVyYXRpdmVsYW5ndWFnZS5nb29nbGVhcGlzLmNvbS92MWJldGEvbW9kZWxzL2dlbWluaS1wcm86Z2VuZXJhdGVDb250ZW50JztcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IGZvcm1EYXRhID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG5cbiAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcbiAgICBpZiAoIWZvcm1EYXRhLnBlcnNvbmFsPy5uYW1lIHx8ICFmb3JtRGF0YS5wZXJzb25hbD8uZW1haWwpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ01pc3NpbmcgcmVxdWlyZWQgcGVyc29uYWwgaW5mb3JtYXRpb24nIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiBBUEkga2V5IGlzIGNvbmZpZ3VyZWRcbiAgICBpZiAoIUdFTUlOSV9BUElfS0VZIHx8IEdFTUlOSV9BUElfS0VZID09PSAneW91cl9hY3R1YWxfZ2VtaW5pX2FwaV9rZXlfaGVyZScpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0dlbWluaSBBUEkga2V5IG5vdCBjb25maWd1cmVkLCB1c2luZyBmYWxsYmFjaycpO1xuICAgICAgLy8gRmFsbGJhY2s6IHJldHVybiBhIGJhc2ljIHJlc3VtZSBzdHJ1Y3R1cmVcbiAgICAgIGNvbnN0IGZhbGxiYWNrUmVzdW1lID0gY3JlYXRlRmFsbGJhY2tSZXN1bWUoZm9ybURhdGEpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgcmVzdW1lRGF0YTogZmFsbGJhY2tSZXN1bWUsXG4gICAgICAgIGRvd25sb2FkVXJsOiAnL2FwaS9kb3dubG9hZC1yZXN1bWUvJyArIERhdGUubm93KCksXG4gICAgICAgIG1lc3NhZ2U6ICdSZXN1bWUgZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSAodXNpbmcgdGVtcGxhdGUpJyxcbiAgICAgICAgZmFsbGJhY2s6IHRydWVcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIFByZXBhcmUgcHJvbXB0IGZvciBHZW1pbmkgQVBJXG4gICAgY29uc3QgcHJvbXB0ID0gY3JlYXRlUmVzdW1lUHJvbXB0KGZvcm1EYXRhKTtcblxuICAgIC8vIENhbGwgR2VtaW5pIEFQSVxuICAgIGNvbnN0IGdlbWluaVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7R0VNSU5JX0FQSV9VUkx9P2tleT0ke0dFTUlOSV9BUElfS0VZfWAsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgY29udGVudHM6IFt7XG4gICAgICAgICAgcGFydHM6IFt7XG4gICAgICAgICAgICB0ZXh0OiBwcm9tcHRcbiAgICAgICAgICB9XVxuICAgICAgICB9XSxcbiAgICAgICAgZ2VuZXJhdGlvbkNvbmZpZzoge1xuICAgICAgICAgIHRlbXBlcmF0dXJlOiAwLjcsXG4gICAgICAgICAgdG9wSzogNDAsXG4gICAgICAgICAgdG9wUDogMC45NSxcbiAgICAgICAgICBtYXhPdXRwdXRUb2tlbnM6IDIwNDgsXG4gICAgICAgIH1cbiAgICAgIH0pXG4gICAgfSk7XG5cbiAgICBpZiAoIWdlbWluaVJlc3BvbnNlLm9rKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEdlbWluaSBBUEkgZXJyb3I6ICR7Z2VtaW5pUmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cblxuICAgIGNvbnN0IGdlbWluaURhdGEgPSBhd2FpdCBnZW1pbmlSZXNwb25zZS5qc29uKCk7XG4gICAgY29uc3QgZ2VuZXJhdGVkQ29udGVudCA9IGdlbWluaURhdGEuY2FuZGlkYXRlcz8uWzBdPy5jb250ZW50Py5wYXJ0cz8uWzBdPy50ZXh0O1xuXG4gICAgaWYgKCFnZW5lcmF0ZWRDb250ZW50KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIGNvbnRlbnQgZ2VuZXJhdGVkIGZyb20gR2VtaW5pIEFQSScpO1xuICAgIH1cblxuICAgIC8vIFByb2Nlc3MgdGhlIGdlbmVyYXRlZCBjb250ZW50IGFuZCBjcmVhdGUgcmVzdW1lXG4gICAgY29uc3QgcmVzdW1lRGF0YSA9IHByb2Nlc3NHZW5lcmF0ZWRDb250ZW50KGdlbmVyYXRlZENvbnRlbnQsIGZvcm1EYXRhKTtcbiAgICBcbiAgICAvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHlvdSB3b3VsZDpcbiAgICAvLyAxLiBHZW5lcmF0ZSBQREYgZnJvbSB0aGUgcmVzdW1lIGRhdGFcbiAgICAvLyAyLiBTdG9yZSBpdCBpbiBjbG91ZCBzdG9yYWdlXG4gICAgLy8gMy4gUmV0dXJuIHRoZSBkb3dubG9hZCBVUkxcbiAgICBcbiAgICAvLyBGb3Igbm93LCByZXR1cm4gdGhlIHByb2Nlc3NlZCBkYXRhXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICByZXN1bWVEYXRhLFxuICAgICAgLy8gTW9jayBVUkwgLSByZXBsYWNlIHdpdGggYWN0dWFsIFBERiBnZW5lcmF0aW9uXG4gICAgICBkb3dubG9hZFVybDogJy9hcGkvZG93bmxvYWQtcmVzdW1lLycgKyBEYXRlLm5vdygpLFxuICAgICAgbWVzc2FnZTogJ1Jlc3VtZSBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5J1xuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignUmVzdW1lIGdlbmVyYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZ2VuZXJhdGUgcmVzdW1lJywgZGV0YWlsczogZXJyb3IubWVzc2FnZSB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG5mdW5jdGlvbiBjcmVhdGVSZXN1bWVQcm9tcHQoZm9ybURhdGEpIHtcbiAgY29uc3QgeyBwZXJzb25hbCwgZWR1Y2F0aW9uLCBleHBlcmllbmNlLCBza2lsbHMgfSA9IGZvcm1EYXRhO1xuICBcbiAgcmV0dXJuIGBDcmVhdGUgYSBwcm9mZXNzaW9uYWwgcmVzdW1lIGJhc2VkIG9uIHRoZSBmb2xsb3dpbmcgaW5mb3JtYXRpb246XG5cblBlcnNvbmFsIEluZm9ybWF0aW9uOlxuLSBOYW1lOiAke3BlcnNvbmFsLm5hbWV9XG4tIEVtYWlsOiAke3BlcnNvbmFsLmVtYWlsfVxuLSBQaG9uZTogJHtwZXJzb25hbC5waG9uZSB8fCAnTm90IHByb3ZpZGVkJ31cblxuRWR1Y2F0aW9uOlxuLSBEZWdyZWU6ICR7ZWR1Y2F0aW9uLmRlZ3JlZSB8fCAnTm90IHByb3ZpZGVkJ31cbi0gSW5zdGl0dXRpb246ICR7ZWR1Y2F0aW9uLmluc3RpdHV0aW9uIHx8ICdOb3QgcHJvdmlkZWQnfVxuLSBGaWVsZCBvZiBTdHVkeTogJHtlZHVjYXRpb24uZmllbGQgfHwgJ05vdCBwcm92aWRlZCd9XG4tIEdyYWR1YXRpb24gWWVhcjogJHtlZHVjYXRpb24uZ3JhZHVhdGlvblllYXIgfHwgJ05vdCBwcm92aWRlZCd9XG5cbkV4cGVyaWVuY2U6ICR7ZXhwZXJpZW5jZSA/IEpTT04uc3RyaW5naWZ5KGV4cGVyaWVuY2UpIDogJ05vdCBwcm92aWRlZCd9XG5Ta2lsbHM6ICR7c2tpbGxzID8gSlNPTi5zdHJpbmdpZnkoc2tpbGxzKSA6ICdOb3QgcHJvdmlkZWQnfVxuXG5QbGVhc2UgZ2VuZXJhdGUgYSB3ZWxsLXN0cnVjdHVyZWQsIHByb2Zlc3Npb25hbCByZXN1bWUgaW4gYSBjbGVhbiBmb3JtYXQuIEluY2x1ZGU6XG4xLiBBIHByb2Zlc3Npb25hbCBzdW1tYXJ5XG4yLiBXb3JrIGV4cGVyaWVuY2Ugd2l0aCBidWxsZXQgcG9pbnRzIGhpZ2hsaWdodGluZyBhY2hpZXZlbWVudHNcbjMuIEVkdWNhdGlvbiBkZXRhaWxzXG40LiBTa2lsbHMgc2VjdGlvblxuNS4gQW55IGFkZGl0aW9uYWwgcmVsZXZhbnQgc2VjdGlvbnNcblxuTWFrZSBpdCBBVFMtZnJpZW5kbHkgYW5kIHByb2Zlc3Npb25hbC4gUmV0dXJuIHRoZSBjb250ZW50IGluIGEgc3RydWN0dXJlZCBmb3JtYXQgdGhhdCBjYW4gYmUgZWFzaWx5IGNvbnZlcnRlZCB0byBQREYuYDtcbn1cblxuZnVuY3Rpb24gcHJvY2Vzc0dlbmVyYXRlZENvbnRlbnQoY29udGVudCwgb3JpZ2luYWxEYXRhKSB7XG4gIC8vIFByb2Nlc3MgdGhlIEdlbWluaS1nZW5lcmF0ZWQgY29udGVudFxuICAvLyBUaGlzIHdvdWxkIHR5cGljYWxseSBpbnZvbHZlIHBhcnNpbmcgdGhlIEFJIHJlc3BvbnNlIGFuZCBzdHJ1Y3R1cmluZyBpdFxuICByZXR1cm4ge1xuICAgIGdlbmVyYXRlZENvbnRlbnQ6IGNvbnRlbnQsXG4gICAgb3JpZ2luYWxEYXRhLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIHZlcnNpb246ICcxLjAnXG4gIH07XG59XG5cbmZ1bmN0aW9uIGNyZWF0ZUZhbGxiYWNrUmVzdW1lKGZvcm1EYXRhKSB7XG4gIGNvbnN0IHsgcGVyc29uYWwsIGVkdWNhdGlvbiB9ID0gZm9ybURhdGE7XG5cbiAgcmV0dXJuIHtcbiAgICBnZW5lcmF0ZWRDb250ZW50OiBgXG4jICR7cGVyc29uYWwubmFtZX1cbioqRW1haWw6KiogJHtwZXJzb25hbC5lbWFpbH1cbioqUGhvbmU6KiogJHtwZXJzb25hbC5waG9uZSB8fCAnTm90IHByb3ZpZGVkJ31cblxuIyMgUHJvZmVzc2lvbmFsIFN1bW1hcnlcbkRlZGljYXRlZCBwcm9mZXNzaW9uYWwgd2l0aCBzdHJvbmcgZWR1Y2F0aW9uYWwgYmFja2dyb3VuZCBhbmQgY29tbWl0bWVudCB0byBleGNlbGxlbmNlLlxuXG4jIyBFZHVjYXRpb25cbioqJHtlZHVjYXRpb24uZGVncmVlIHx8ICdEZWdyZWUnfSoqIGluICR7ZWR1Y2F0aW9uLmZpZWxkIHx8ICdGaWVsZCBvZiBTdHVkeSd9XG4ke2VkdWNhdGlvbi5pbnN0aXR1dGlvbiB8fCAnSW5zdGl0dXRpb24nfSB8ICR7ZWR1Y2F0aW9uLmdyYWR1YXRpb25ZZWFyIHx8ICdZZWFyJ31cblxuIyMgU2tpbGxzXG4tIFN0cm9uZyBjb21tdW5pY2F0aW9uIHNraWxsc1xuLSBQcm9ibGVtLXNvbHZpbmcgYWJpbGl0aWVzXG4tIFRlYW0gY29sbGFib3JhdGlvblxuLSBBdHRlbnRpb24gdG8gZGV0YWlsXG5cbiMjIEV4cGVyaWVuY2VcblJlYWR5IHRvIGNvbnRyaWJ1dGUgdG8gYSBkeW5hbWljIHRlYW0gYW5kIGdyb3cgcHJvZmVzc2lvbmFsbHkuXG4gICAgYCxcbiAgICBvcmlnaW5hbERhdGE6IGZvcm1EYXRhLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIHZlcnNpb246ICcxLjAnLFxuICAgIHR5cGU6ICdmYWxsYmFjaydcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJHRU1JTklfQVBJX0tFWSIsInByb2Nlc3MiLCJlbnYiLCJHRU1JTklfQVBJX1VSTCIsIlBPU1QiLCJyZXF1ZXN0IiwiZm9ybURhdGEiLCJqc29uIiwicGVyc29uYWwiLCJuYW1lIiwiZW1haWwiLCJlcnJvciIsInN0YXR1cyIsImNvbnNvbGUiLCJmYWxsYmFja1Jlc3VtZSIsImNyZWF0ZUZhbGxiYWNrUmVzdW1lIiwic3VjY2VzcyIsInJlc3VtZURhdGEiLCJkb3dubG9hZFVybCIsIkRhdGUiLCJub3ciLCJtZXNzYWdlIiwiZmFsbGJhY2siLCJwcm9tcHQiLCJjcmVhdGVSZXN1bWVQcm9tcHQiLCJnZW1pbmlSZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiY29udGVudHMiLCJwYXJ0cyIsInRleHQiLCJnZW5lcmF0aW9uQ29uZmlnIiwidGVtcGVyYXR1cmUiLCJ0b3BLIiwidG9wUCIsIm1heE91dHB1dFRva2VucyIsIm9rIiwiRXJyb3IiLCJnZW1pbmlEYXRhIiwiZ2VuZXJhdGVkQ29udGVudCIsImNhbmRpZGF0ZXMiLCJjb250ZW50IiwicHJvY2Vzc0dlbmVyYXRlZENvbnRlbnQiLCJkZXRhaWxzIiwiZWR1Y2F0aW9uIiwiZXhwZXJpZW5jZSIsInNraWxscyIsInBob25lIiwiZGVncmVlIiwiaW5zdGl0dXRpb24iLCJmaWVsZCIsImdyYWR1YXRpb25ZZWFyIiwib3JpZ2luYWxEYXRhIiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJ2ZXJzaW9uIiwidHlwZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();