/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-resume/route";
exports.ids = ["app/api/generate-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-resume/route.js */ \"(rsc)/./src/app/api/generate-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-resume/route\",\n        pathname: \"/api/generate-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\generate-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-resume/route.js":
/*!**********************************************!*\
  !*** ./src/app/api/generate-resume/route.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Note: Replace with your actual Gemini API integration\n// This is a placeholder structure for the Gemini API integration\nconst GEMINI_API_KEY = process.env.GEMINI_API_KEY;\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';\nasync function POST(request) {\n    try {\n        const formData = await request.json();\n        // Validate required fields\n        if (!formData.personal?.firstName || !formData.personal?.lastName || !formData.personal?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required personal information (First Name, Last Name, and Email are required)'\n            }, {\n                status: 400\n            });\n        }\n        // Check if API key is configured\n        if (!GEMINI_API_KEY || GEMINI_API_KEY === 'your_actual_gemini_api_key_here') {\n            console.error('Gemini API key not configured, using fallback');\n            // Fallback: return a basic resume structure\n            const fallbackResume = createFallbackResume(formData);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                resumeData: fallbackResume,\n                downloadUrl: '/api/download-resume/' + Date.now(),\n                message: 'Resume generated successfully (using template)',\n                fallback: true\n            });\n        }\n        // Prepare prompt for Gemini API\n        const prompt = createResumePrompt(formData);\n        // Call Gemini API\n        const geminiResponse = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                contents: [\n                    {\n                        parts: [\n                            {\n                                text: prompt\n                            }\n                        ]\n                    }\n                ],\n                generationConfig: {\n                    temperature: 0.7,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 2048\n                }\n            })\n        });\n        if (!geminiResponse.ok) {\n            throw new Error(`Gemini API error: ${geminiResponse.status}`);\n        }\n        const geminiData = await geminiResponse.json();\n        const generatedContent = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;\n        if (!generatedContent) {\n            throw new Error('No content generated from Gemini API');\n        }\n        // Process the generated content and create resume\n        const resumeData = processGeneratedContent(generatedContent, formData);\n        // In a real implementation, you would:\n        // 1. Generate PDF from the resume data\n        // 2. Store it in cloud storage\n        // 3. Return the download URL\n        // For now, return the processed data\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            resumeData,\n            // Mock URL - replace with actual PDF generation\n            downloadUrl: '/api/download-resume/' + Date.now(),\n            message: 'Resume generated successfully'\n        });\n    } catch (error) {\n        console.error('Resume generation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate resume',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createResumePrompt(formData) {\n    const { personal, education, experience, skills, projects } = formData;\n    return `Create a professional resume based on the following information:\n\nPersonal Information:\n- Name: ${personal.firstName} ${personal.lastName}\n- Email: ${personal.email}\n- Phone: ${personal.phone || 'Not provided'}\n- Location: ${personal.location || 'Not provided'}\n- LinkedIn: ${personal.linkedin || 'Not provided'}\n- Portfolio: ${personal.portfolio || 'Not provided'}\n- Summary: ${personal.summary || 'Not provided'}\n\nEducation: ${education ? JSON.stringify(education) : 'Not provided'}\nExperience: ${experience ? JSON.stringify(experience) : 'Not provided'}\nSkills: ${skills ? JSON.stringify(skills) : 'Not provided'}\nProjects: ${projects ? JSON.stringify(projects) : 'Not provided'}\n\nPlease generate a well-structured, professional resume in a clean format. Include:\n1. A professional summary\n2. Work experience with bullet points highlighting achievements\n3. Education details\n4. Skills section\n5. Any additional relevant sections\n\nMake it ATS-friendly and professional. Return the content in a structured format that can be easily converted to PDF.`;\n}\nfunction processGeneratedContent(content, originalData) {\n    // Process the Gemini-generated content\n    // This would typically involve parsing the AI response and structuring it\n    return {\n        generatedContent: content,\n        originalData,\n        timestamp: new Date().toISOString(),\n        version: '1.0'\n    };\n}\nfunction createFallbackResume(formData) {\n    const { personal, education, experience, skills, projects } = formData;\n    return {\n        generatedContent: `\n# ${personal.firstName} ${personal.lastName}\n**Email:** ${personal.email}\n**Phone:** ${personal.phone || 'Not provided'}\n**Location:** ${personal.location || 'Not provided'}\n${personal.linkedin ? `**LinkedIn:** ${personal.linkedin}` : ''}\n${personal.portfolio ? `**Portfolio:** ${personal.portfolio}` : ''}\n\n## Professional Summary\n${personal.summary || 'Dedicated professional with strong educational background and commitment to excellence.'}\n\n## Education\n${education && education.length > 0 ? education.map((edu)=>`**${edu.degree || 'Degree'}** - ${edu.institution || 'Institution'}\n  ${edu.startDate} - ${edu.endDate} | ${edu.location || ''}\n  ${edu.gpa ? `GPA: ${edu.gpa}` : ''}\n  ${edu.relevant || ''}`).join('\\n\\n') : 'Education details not provided'}\n\n## Experience\n${experience && experience.length > 0 ? experience.map((exp)=>`**${exp.title || 'Position'}** - ${exp.company || 'Company'}\n  ${exp.startDate} - ${exp.current ? 'Present' : exp.endDate} | ${exp.location || ''}\n  ${exp.description || ''}`).join('\\n\\n') : 'Experience details not provided'}\n\n## Skills\n${skills && skills.technical && skills.technical.length > 0 ? `**Technical:** ${skills.technical.join(', ')}` : ''}\n${skills && skills.languages && skills.languages.length > 0 ? `**Languages:** ${skills.languages.join(', ')}` : ''}\n${skills && skills.certifications && skills.certifications.length > 0 ? `**Certifications:** ${skills.certifications.join(', ')}` : ''}\n\n## Projects\n${projects && projects.length > 0 ? projects.map((project)=>`**${project.name || 'Project'}**\n  ${project.description || ''}\n  ${project.technologies ? `Technologies: ${project.technologies}` : ''}\n  ${project.link ? `Link: ${project.link}` : ''}`).join('\\n\\n') : 'Projects not provided'}\n    `,\n        originalData: formData,\n        timestamp: new Date().toISOString(),\n        version: '1.0',\n        type: 'fallback'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();