"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { 
  User, 
  GraduationCap, 
  Briefcase, 
  Award, 
  FileText,
  CheckCircle,
  Circle
} from 'lucide-react';

const StepIndicator = ({ 
  currentStep, 
  totalSteps, 
  steps, 
  onStepClick, 
  allowClickNavigation = true,
  className = "" 
}) => {
  const getStepIcon = (stepIndex) => {
    const icons = [User, GraduationCap, Briefcase, Award, FileText];
    return icons[stepIndex] || Circle;
  };

  const getStepStatus = (stepIndex) => {
    if (stepIndex < currentStep) return 'completed';
    if (stepIndex === currentStep) return 'current';
    return 'upcoming';
  };

  const getStepColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500 border-green-500 text-white';
      case 'current':
        return 'bg-neural-purple border-neural-purple text-white';
      case 'upcoming':
        return 'bg-gray-700 border-gray-600 text-gray-400';
      default:
        return 'bg-gray-700 border-gray-600 text-gray-400';
    }
  };

  const getConnectorColor = (stepIndex) => {
    return stepIndex < currentStep ? 'bg-green-500' : 'bg-gray-600';
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Mobile Step Indicator */}
      <div className="block md:hidden">
        <div className="flex items-center justify-between mb-4">
          <div className="text-sm font-medium text-gray-300 whitespace-nowrap">
            Step {currentStep + 1} of {totalSteps}
          </div>
          <div className="text-sm text-gray-400 whitespace-nowrap">
            {Math.round(((currentStep + 1) / totalSteps) * 100)}% Complete
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
          <motion.div
            className="bg-neural-purple h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>

        {/* Current Step Name */}
        <div className="text-center">
          <h3 className="text-lg font-semibold text-white">
            {steps[currentStep]?.title}
          </h3>
          <p className="text-sm text-gray-400 mt-1">
            {steps[currentStep]?.description}
          </p>
        </div>
      </div>

      {/* Desktop Step Indicator */}
      <div className="hidden md:block">
        <div className="flex items-center justify-between pb-16">
          {steps.map((step, index) => {
            const Icon = getStepIcon(index);
            const status = getStepStatus(index);
            const isClickable = allowClickNavigation && (index <= currentStep || status === 'completed');

            return (
              <div key={index} className="flex items-center flex-1">
                {/* Step Circle */}
                <div className="relative flex items-center justify-center">
                  <motion.button
                    onClick={() => isClickable && onStepClick && onStepClick(index)}
                    disabled={!isClickable}
                    className={`
                      relative z-10 flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
                      ${getStepColor(status)}
                      ${isClickable ? 'cursor-pointer hover:scale-110' : 'cursor-not-allowed'}
                    `}
                    whileHover={isClickable ? { scale: 1.1 } : {}}
                    whileTap={isClickable ? { scale: 0.95 } : {}}
                  >
                    {status === 'completed' ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </motion.button>

                  {/* Step Label */}
                  <div className="absolute top-12 left-1/2 transform -translate-x-1/2 text-center w-24">
                    <div className={`text-sm font-medium ${
                      status === 'current' ? 'text-neural-purple' :
                      status === 'completed' ? 'text-green-400' : 'text-gray-400'
                    }`}>
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {step.description}
                    </div>
                  </div>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="flex-1 mx-4">
                    <div className="relative">
                      <div className="h-0.5 bg-gray-600 w-full"></div>
                      <motion.div
                        className={`h-0.5 absolute top-0 left-0 ${getConnectorColor(index)}`}
                        initial={{ width: 0 }}
                        animate={{
                          width: index < currentStep ? '100%' : '0%'
                        }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                      />
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Progress Summary */}
        <div className="mt-8 text-center">
          <div className="text-sm text-gray-400">
            Progress: {currentStep + 1} of {totalSteps} steps completed
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {currentStep < totalSteps - 1
              ? `Next: ${steps[currentStep + 1]?.title}`
              : 'Ready to generate your resume!'
            }
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepIndicator;
