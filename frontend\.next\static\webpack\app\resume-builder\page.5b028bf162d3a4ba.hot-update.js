"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/StickyNavigation.jsx":
/*!*********************************************!*\
  !*** ./src/components/StickyNavigation.jsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,ChevronLeft,ChevronRight,Save,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,ChevronLeft,ChevronRight,Save,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,ChevronLeft,ChevronRight,Save,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,ChevronLeft,ChevronRight,Save,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst StickyNavigation = (param)=>{\n    let { currentStep, totalSteps, onPrevious, onNext, onGenerate, isGenerating = false, canProceed = true, steps = [], className = \"\" } = param;\n    const isFirstStep = currentStep === 0;\n    const isLastStep = currentStep === totalSteps - 1;\n    const currentStepData = steps[currentStep];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            y: 100,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        className: \"fixed bottom-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-sm border-t border-gray-700 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: !isFirstStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: onPrevious,\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg transition-all duration-200 text-gray-300 hover:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"Previous\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                lineNumber: 39,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, undefined) // Spacer for alignment\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 text-center px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-medium text-white whitespace-nowrap overflow-hidden text-ellipsis\",\n                                    children: (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.title) || \"Step \".concat(currentStep + 1)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1 whitespace-nowrap\",\n                                    children: [\n                                        currentStep + 1,\n                                        \" of \",\n                                        totalSteps,\n                                        \" • \",\n                                        Math.round((currentStep + 1) / totalSteps * 100),\n                                        \"% Complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: !isLastStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: onNext,\n                                disabled: !canProceed,\n                                whileHover: canProceed ? {\n                                    scale: 1.02\n                                } : {},\n                                whileTap: canProceed ? {\n                                    scale: 0.98\n                                } : {},\n                                className: \"\\n                  flex items-center gap-2 px-6 py-2 rounded-lg transition-all duration-200 font-medium\\n                  \".concat(canProceed ? 'bg-neural-purple hover:bg-neural-purple/80 text-white' : 'bg-gray-700 text-gray-400 cursor-not-allowed', \"\\n                \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Next\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: onGenerate,\n                                disabled: isGenerating || !canProceed,\n                                whileHover: !isGenerating && canProceed ? {\n                                    scale: 1.02\n                                } : {},\n                                whileTap: !isGenerating && canProceed ? {\n                                    scale: 0.98\n                                } : {},\n                                className: \"\\n                  flex items-center gap-2 px-6 py-2 rounded-lg transition-all duration-200 font-medium\\n                  \".concat(!isGenerating && canProceed ? 'bg-gradient-to-r from-neural-purple to-neural-pink hover:from-neural-purple/80 hover:to-neural-pink/80 text-white' : 'bg-gray-700 text-gray-400 cursor-not-allowed', \"\\n                \"),\n                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            animate: {\n                                                rotate: 360\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                repeat: Infinity,\n                                                ease: \"linear\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Generating...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Generate Resume\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-800 h-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"bg-gradient-to-r from-neural-purple to-neural-pink h-1\",\n                    initial: {\n                        width: 0\n                    },\n                    animate: {\n                        width: \"\".concat((currentStep + 1) / totalSteps * 100, \"%\")\n                    },\n                    transition: {\n                        duration: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StickyNavigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StickyNavigation);\nvar _c;\n$RefreshReg$(_c, \"StickyNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/StickyNavigation.jsx\n"));

/***/ })

});