"use client";
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  HelpCircle, 
  Target, 
  TrendingUp, 
  Zap, 
  FileText,
  Award,
  Lightbulb,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

const ATSTooltip = ({ fieldType, className = "" }) => {
  const [isVisible, setIsVisible] = useState(false);

  const getTooltipContent = (type) => {
    switch (type) {
      case 'firstName':
      case 'lastName':
        return {
          icon: Target,
          title: 'Name Requirements',
          tips: [
            'Use your full legal name as it appears on official documents',
            'Avoid nicknames or abbreviations',
            'ATS systems scan for complete names for verification'
          ]
        };

      case 'email':
        return {
          icon: Target,
          title: 'Email Best Practices',
          tips: [
            'Use a professional email address',
            'Avoid numbers or special characters when possible',
            '<NAME_EMAIL> format',
            'Gmail, Outlook, and Yahoo are widely accepted'
          ]
        };

      case 'summary':
        return {
          icon: FileText,
          title: 'Professional Summary ATS Tips',
          tips: [
            'Include 2-3 sentences highlighting your experience',
            'Use industry-specific keywords naturally',
            'Mention years of experience and key skills',
            'Avoid first-person pronouns (I, me, my)',
            'Keep it between 50-150 words for optimal ATS parsing'
          ]
        };

      case 'job_title':
        return {
          icon: TrendingUp,
          title: 'Job Title Optimization',
          tips: [
            'Use standard industry job titles when possible',
            'Include seniority level (Junior, Senior, Lead, Principal)',
            'Avoid internal company jargon or unique titles',
            'Match keywords from job descriptions you\'re targeting',
            'Examples: "Senior Software Engineer", "Data Analyst", "Product Manager"'
          ]
        };

      case 'company':
        return {
          icon: TrendingUp,
          title: 'Company Name Guidelines',
          tips: [
            'Use the official company name',
            'Include well-known abbreviations in parentheses if applicable',
            'Example: "Tata Consultancy Services (TCS)"',
            'Avoid internal division names unless widely recognized'
          ]
        };

      case 'experience_description':
        return {
          icon: TrendingUp,
          title: 'Experience Description ATS Optimization',
          tips: [
            'Start each bullet with strong action verbs (Led, Developed, Implemented)',
            'Include quantified achievements with numbers and percentages',
            'Use industry-specific technical keywords',
            'Mention technologies, tools, and methodologies used',
            'Focus on results and impact, not just responsibilities',
            'Keep bullets concise but descriptive (1-2 lines each)'
          ]
        };

      case 'degree':
        return {
          icon: Award,
          title: 'Degree Information',
          tips: [
            'Use the full, official degree name',
            'Include relevant specializations or concentrations',
            'Examples: "Bachelor of Science in Computer Science"',
            'Mention relevant coursework if space permits'
          ]
        };

      case 'institution':
        return {
          icon: Award,
          title: 'Institution Guidelines',
          tips: [
            'Use the complete, official institution name',
            'Include "University", "College", or "Institute" in the name',
            'Mention location if the institution isn\'t widely known',
            'Use commonly recognized abbreviations if applicable'
          ]
        };

      case 'technical_skills':
        return {
          icon: Zap,
          title: 'Technical Skills ATS Strategy',
          tips: [
            'List 6-12 most relevant technical skills',
            'Use exact technology names (React.js, not just React)',
            'Include both programming languages and frameworks',
            'Match skills mentioned in target job descriptions',
            'Group similar technologies together',
            'Update regularly to include current technologies'
          ]
        };

      case 'project_name':
        return {
          icon: Lightbulb,
          title: 'Project Name Best Practices',
          tips: [
            'Use descriptive, professional project names',
            'Avoid internal codenames or abbreviations',
            'Include the type of application/system if relevant',
            'Examples: "E-commerce Platform", "Customer Management System"'
          ]
        };

      case 'project_description':
        return {
          icon: Lightbulb,
          title: 'Project Description ATS Tips',
          tips: [
            'Start with strong action verbs (Built, Designed, Created)',
            'Mention specific technologies and tools used',
            'Include quantified results when possible',
            'Describe the project\'s purpose and your role',
            'Highlight technical challenges overcome',
            'Keep it concise but comprehensive (2-4 sentences)'
          ]
        };

      default:
        return {
          icon: HelpCircle,
          title: 'ATS Optimization',
          tips: [
            'Use relevant keywords for your industry',
            'Keep formatting simple and clean',
            'Avoid graphics, tables, and complex layouts',
            'Use standard section headings'
          ]
        };
    }
  };

  const content = getTooltipContent(fieldType);
  const Icon = content.icon;

  return (
    <div className={`relative inline-block ${className}`}>
      <button
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        onClick={() => setIsVisible(!isVisible)}
        className="text-gray-400 hover:text-neural-purple transition-colors"
      >
        <HelpCircle className="h-4 w-4" />
      </button>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 10 }}
            className="absolute z-50 w-80 p-4 bg-gray-900 border border-gray-700 rounded-lg shadow-xl bottom-full left-0 mb-2"
          >
            <div className="flex items-center gap-2 mb-3">
              <Icon className="h-5 w-5 text-neural-purple" />
              <h4 className="font-semibold text-white">{content.title}</h4>
            </div>
            
            <div className="space-y-2">
              {content.tips.map((tip, index) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-400 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-gray-300 leading-relaxed">{tip}</p>
                </div>
              ))}
            </div>

            {/* Arrow pointing down */}
            <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-700"></div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ATSTooltip;
