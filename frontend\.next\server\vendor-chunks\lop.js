/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lop";
exports.ids = ["vendor-chunks/lop"];
exports.modules = {

/***/ "(rsc)/./node_modules/lop/index.js":
/*!***********************************!*\
  !*** ./node_modules/lop/index.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.Parser = __webpack_require__(/*! ./lib/parser */ \"(rsc)/./node_modules/lop/lib/parser.js\").Parser;\nexports.rules = __webpack_require__(/*! ./lib/rules */ \"(rsc)/./node_modules/lop/lib/rules.js\");\nexports.errors = __webpack_require__(/*! ./lib/errors */ \"(rsc)/./node_modules/lop/lib/errors.js\");\nexports.results = __webpack_require__(/*! ./lib/parsing-results */ \"(rsc)/./node_modules/lop/lib/parsing-results.js\");\nexports.StringSource = __webpack_require__(/*! ./lib/StringSource */ \"(rsc)/./node_modules/lop/lib/StringSource.js\");\nexports.Token = __webpack_require__(/*! ./lib/Token */ \"(rsc)/./node_modules/lop/lib/Token.js\");\nexports.bottomUp = __webpack_require__(/*! ./lib/bottom-up */ \"(rsc)/./node_modules/lop/lib/bottom-up.js\");\nexports.RegexTokeniser = __webpack_require__(/*! ./lib/regex-tokeniser */ \"(rsc)/./node_modules/lop/lib/regex-tokeniser.js\").RegexTokeniser;\n\nexports.rule = function(ruleBuilder) {\n    var rule;\n    return function(input) {\n        if (!rule) {\n            rule = ruleBuilder();\n        }\n        return rule(input);\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbG9wL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUErQztBQUMvQywrRkFBc0M7QUFDdEMsa0dBQXdDO0FBQ3hDLHFIQUFrRDtBQUNsRCxvSEFBb0Q7QUFDcEQsK0ZBQXNDO0FBQ3RDLDBHQUE2QztBQUM3QywySUFBd0U7O0FBRXhFLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXEJsaW5rRmluZFxcQmxpbmtGaW5kLVdlYlxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbG9wXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnRzLlBhcnNlciA9IHJlcXVpcmUoXCIuL2xpYi9wYXJzZXJcIikuUGFyc2VyO1xuZXhwb3J0cy5ydWxlcyA9IHJlcXVpcmUoXCIuL2xpYi9ydWxlc1wiKTtcbmV4cG9ydHMuZXJyb3JzID0gcmVxdWlyZShcIi4vbGliL2Vycm9yc1wiKTtcbmV4cG9ydHMucmVzdWx0cyA9IHJlcXVpcmUoXCIuL2xpYi9wYXJzaW5nLXJlc3VsdHNcIik7XG5leHBvcnRzLlN0cmluZ1NvdXJjZSA9IHJlcXVpcmUoXCIuL2xpYi9TdHJpbmdTb3VyY2VcIik7XG5leHBvcnRzLlRva2VuID0gcmVxdWlyZShcIi4vbGliL1Rva2VuXCIpO1xuZXhwb3J0cy5ib3R0b21VcCA9IHJlcXVpcmUoXCIuL2xpYi9ib3R0b20tdXBcIik7XG5leHBvcnRzLlJlZ2V4VG9rZW5pc2VyID0gcmVxdWlyZShcIi4vbGliL3JlZ2V4LXRva2VuaXNlclwiKS5SZWdleFRva2VuaXNlcjtcblxuZXhwb3J0cy5ydWxlID0gZnVuY3Rpb24ocnVsZUJ1aWxkZXIpIHtcbiAgICB2YXIgcnVsZTtcbiAgICByZXR1cm4gZnVuY3Rpb24oaW5wdXQpIHtcbiAgICAgICAgaWYgKCFydWxlKSB7XG4gICAgICAgICAgICBydWxlID0gcnVsZUJ1aWxkZXIoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcnVsZShpbnB1dCk7XG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/StringSource.js":
/*!**********************************************!*\
  !*** ./node_modules/lop/lib/StringSource.js ***!
  \**********************************************/
/***/ ((module) => {

eval("var StringSource = module.exports = function(string, description) {\n    var self = {\n        asString: function() {\n            return string;\n        },\n        range: function(startIndex, endIndex) {\n            return new StringSourceRange(string, description, startIndex, endIndex);\n        }\n    };\n    return self;\n};\n\nvar StringSourceRange = function(string, description, startIndex, endIndex) {\n    this._string = string;\n    this._description = description;\n    this._startIndex = startIndex;\n    this._endIndex = endIndex;\n};\n\nStringSourceRange.prototype.to = function(otherRange) {\n    // TODO: Assert that tokens are the same across both iterators\n    return new StringSourceRange(this._string, this._description, this._startIndex, otherRange._endIndex);\n};\n\nStringSourceRange.prototype.describe = function() {\n    var position = this._position();\n    var description = this._description ? this._description + \"\\n\" : \"\";\n    return description + \"Line number: \" + position.lineNumber + \"\\nCharacter number: \" + position.characterNumber;\n};\n\nStringSourceRange.prototype.lineNumber = function() {\n    return this._position().lineNumber;\n};\n\nStringSourceRange.prototype.characterNumber = function() {\n    return this._position().characterNumber;\n};\n\nStringSourceRange.prototype._position = function() {\n    var self = this;\n    var index = 0;\n    var nextNewLine = function() {\n        return self._string.indexOf(\"\\n\", index);\n    };\n\n    var lineNumber = 1;\n    while (nextNewLine() !== -1 && nextNewLine() < this._startIndex) {\n        index = nextNewLine() + 1;\n        lineNumber += 1;\n    }\n    var characterNumber = this._startIndex - index + 1;\n    return {lineNumber: lineNumber, characterNumber: characterNumber};\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/StringSource.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/Token.js":
/*!***************************************!*\
  !*** ./node_modules/lop/lib/Token.js ***!
  \***************************************/
/***/ ((module) => {

eval("module.exports = function(name, value, source) {\n    this.name = name;\n    this.value = value;\n    if (source) {\n        this.source = source;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbG9wL2xpYi9Ub2tlbi5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxCbGlua0ZpbmRcXEJsaW5rRmluZC1XZWJcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGxvcFxcbGliXFxUb2tlbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uKG5hbWUsIHZhbHVlLCBzb3VyY2UpIHtcbiAgICB0aGlzLm5hbWUgPSBuYW1lO1xuICAgIHRoaXMudmFsdWUgPSB2YWx1ZTtcbiAgICBpZiAoc291cmNlKSB7XG4gICAgICAgIHRoaXMuc291cmNlID0gc291cmNlO1xuICAgIH1cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/Token.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/TokenIterator.js":
/*!***********************************************!*\
  !*** ./node_modules/lop/lib/TokenIterator.js ***!
  \***********************************************/
/***/ ((module) => {

eval("var TokenIterator = module.exports = function(tokens, startIndex) {\n    this._tokens = tokens;\n    this._startIndex = startIndex || 0;\n};\n\nTokenIterator.prototype.head = function() {\n    return this._tokens[this._startIndex];\n};\n\nTokenIterator.prototype.tail = function(startIndex) {\n    return new TokenIterator(this._tokens, this._startIndex + 1);\n};\n\nTokenIterator.prototype.toArray = function() {\n    return this._tokens.slice(this._startIndex);\n};\n\nTokenIterator.prototype.end = function() {\n    return this._tokens[this._tokens.length - 1];\n};\n\n// TODO: doesn't need to be a method, can be a separate function,\n// which simplifies implementation of the TokenIterator interface\nTokenIterator.prototype.to = function(end) {\n    var start = this.head().source;\n    var endToken = end.head() || end.end();\n    return start.to(endToken.source);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbG9wL2xpYi9Ub2tlbkl0ZXJhdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxCbGlua0ZpbmRcXEJsaW5rRmluZC1XZWJcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGxvcFxcbGliXFxUb2tlbkl0ZXJhdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBUb2tlbkl0ZXJhdG9yID0gbW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbih0b2tlbnMsIHN0YXJ0SW5kZXgpIHtcbiAgICB0aGlzLl90b2tlbnMgPSB0b2tlbnM7XG4gICAgdGhpcy5fc3RhcnRJbmRleCA9IHN0YXJ0SW5kZXggfHwgMDtcbn07XG5cblRva2VuSXRlcmF0b3IucHJvdG90eXBlLmhlYWQgPSBmdW5jdGlvbigpIHtcbiAgICByZXR1cm4gdGhpcy5fdG9rZW5zW3RoaXMuX3N0YXJ0SW5kZXhdO1xufTtcblxuVG9rZW5JdGVyYXRvci5wcm90b3R5cGUudGFpbCA9IGZ1bmN0aW9uKHN0YXJ0SW5kZXgpIHtcbiAgICByZXR1cm4gbmV3IFRva2VuSXRlcmF0b3IodGhpcy5fdG9rZW5zLCB0aGlzLl9zdGFydEluZGV4ICsgMSk7XG59O1xuXG5Ub2tlbkl0ZXJhdG9yLnByb3RvdHlwZS50b0FycmF5ID0gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIHRoaXMuX3Rva2Vucy5zbGljZSh0aGlzLl9zdGFydEluZGV4KTtcbn07XG5cblRva2VuSXRlcmF0b3IucHJvdG90eXBlLmVuZCA9IGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiB0aGlzLl90b2tlbnNbdGhpcy5fdG9rZW5zLmxlbmd0aCAtIDFdO1xufTtcblxuLy8gVE9ETzogZG9lc24ndCBuZWVkIHRvIGJlIGEgbWV0aG9kLCBjYW4gYmUgYSBzZXBhcmF0ZSBmdW5jdGlvbixcbi8vIHdoaWNoIHNpbXBsaWZpZXMgaW1wbGVtZW50YXRpb24gb2YgdGhlIFRva2VuSXRlcmF0b3IgaW50ZXJmYWNlXG5Ub2tlbkl0ZXJhdG9yLnByb3RvdHlwZS50byA9IGZ1bmN0aW9uKGVuZCkge1xuICAgIHZhciBzdGFydCA9IHRoaXMuaGVhZCgpLnNvdXJjZTtcbiAgICB2YXIgZW5kVG9rZW4gPSBlbmQuaGVhZCgpIHx8IGVuZC5lbmQoKTtcbiAgICByZXR1cm4gc3RhcnQudG8oZW5kVG9rZW4uc291cmNlKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/TokenIterator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/bottom-up.js":
/*!*******************************************!*\
  !*** ./node_modules/lop/lib/bottom-up.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var rules = __webpack_require__(/*! ./rules */ \"(rsc)/./node_modules/lop/lib/rules.js\");\nvar results = __webpack_require__(/*! ./parsing-results */ \"(rsc)/./node_modules/lop/lib/parsing-results.js\");\n\nexports.parser = function(name, prefixRules, infixRuleBuilders) {\n    var self = {\n        rule: rule,\n        leftAssociative: leftAssociative,\n        rightAssociative: rightAssociative\n    };\n    \n    var infixRules = new InfixRules(infixRuleBuilders.map(createInfixRule));\n    var prefixRule = rules.firstOf(name, prefixRules);\n    \n    function createInfixRule(infixRuleBuilder) {\n        return {\n            name: infixRuleBuilder.name,\n            rule: lazyRule(infixRuleBuilder.ruleBuilder.bind(null, self))\n        };\n    }\n    \n    function rule() {\n        return createRule(infixRules);\n    }\n    \n    function leftAssociative(name) {\n        return createRule(infixRules.untilExclusive(name));\n    }\n    \n    function rightAssociative(name) {\n        return createRule(infixRules.untilInclusive(name));\n    }\n    \n    function createRule(infixRules) {\n        return apply.bind(null, infixRules);\n    }\n    \n    function apply(infixRules, tokens) {\n        var leftResult = prefixRule(tokens);\n        if (leftResult.isSuccess()) {\n            return infixRules.apply(leftResult);\n        } else {\n            return leftResult;\n        }\n    }\n    \n    return self;\n};\n\nfunction InfixRules(infixRules) {\n    function untilExclusive(name) {\n        return new InfixRules(infixRules.slice(0, ruleNames().indexOf(name)));\n    }\n    \n    function untilInclusive(name) {\n        return new InfixRules(infixRules.slice(0, ruleNames().indexOf(name) + 1));\n    }\n    \n    function ruleNames() {\n        return infixRules.map(function(rule) {\n            return rule.name;\n        });\n    }\n    \n    function apply(leftResult) {\n        var currentResult;\n        var source;\n        while (true) {\n            currentResult = applyToTokens(leftResult.remaining());\n            if (currentResult.isSuccess()) {\n                source = leftResult.source().to(currentResult.source());\n                leftResult = results.success(\n                    currentResult.value()(leftResult.value(), source),\n                    currentResult.remaining(),\n                    source\n                )\n            } else if (currentResult.isFailure()) {\n                return leftResult;\n            } else {\n                return currentResult;\n            }\n        }\n    }\n    \n    function applyToTokens(tokens) {\n        return rules.firstOf(\"infix\", infixRules.map(function(infix) {\n            return infix.rule;\n        }))(tokens);\n    }\n    \n    return {\n        apply: apply,\n        untilExclusive: untilExclusive,\n        untilInclusive: untilInclusive\n    }\n}\n\nexports.infix = function(name, ruleBuilder) {\n    function map(func) {\n        return exports.infix(name, function(parser) {\n            var rule = ruleBuilder(parser);\n            return function(tokens) {\n                var result = rule(tokens);\n                return result.map(function(right) {\n                    return function(left, source) {\n                        return func(left, right, source);\n                    };\n                });\n            };\n        });\n    }\n    \n    return {\n        name: name,\n        ruleBuilder: ruleBuilder,\n        map: map\n    };\n}\n\n// TODO: move into a sensible place and remove duplication\nvar lazyRule = function(ruleBuilder) {\n    var rule;\n    return function(input) {\n        if (!rule) {\n            rule = ruleBuilder();\n        }\n        return rule(input);\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/bottom-up.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/errors.js":
/*!****************************************!*\
  !*** ./node_modules/lop/lib/errors.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.error = function(options) {\n    return new Error(options);\n};\n\nvar Error = function(options) {\n    this.expected = options.expected;\n    this.actual = options.actual;\n    this._location = options.location;\n};\n\nError.prototype.describe = function() {\n    var locationDescription = this._location ? this._location.describe() + \":\\n\" : \"\";\n    return locationDescription + \"Expected \" + this.expected + \"\\nbut got \" + this.actual;\n};\n\nError.prototype.lineNumber = function() {\n    return this._location.lineNumber();\n};\n\nError.prototype.characterNumber = function() {\n    return this._location.characterNumber();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbG9wL2xpYi9lcnJvcnMuanMiLCJtYXBwaW5ncyI6IkFBQUEsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXEJsaW5rRmluZFxcQmxpbmtGaW5kLVdlYlxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbG9wXFxsaWJcXGVycm9ycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnRzLmVycm9yID0gZnVuY3Rpb24ob3B0aW9ucykge1xuICAgIHJldHVybiBuZXcgRXJyb3Iob3B0aW9ucyk7XG59O1xuXG52YXIgRXJyb3IgPSBmdW5jdGlvbihvcHRpb25zKSB7XG4gICAgdGhpcy5leHBlY3RlZCA9IG9wdGlvbnMuZXhwZWN0ZWQ7XG4gICAgdGhpcy5hY3R1YWwgPSBvcHRpb25zLmFjdHVhbDtcbiAgICB0aGlzLl9sb2NhdGlvbiA9IG9wdGlvbnMubG9jYXRpb247XG59O1xuXG5FcnJvci5wcm90b3R5cGUuZGVzY3JpYmUgPSBmdW5jdGlvbigpIHtcbiAgICB2YXIgbG9jYXRpb25EZXNjcmlwdGlvbiA9IHRoaXMuX2xvY2F0aW9uID8gdGhpcy5fbG9jYXRpb24uZGVzY3JpYmUoKSArIFwiOlxcblwiIDogXCJcIjtcbiAgICByZXR1cm4gbG9jYXRpb25EZXNjcmlwdGlvbiArIFwiRXhwZWN0ZWQgXCIgKyB0aGlzLmV4cGVjdGVkICsgXCJcXG5idXQgZ290IFwiICsgdGhpcy5hY3R1YWw7XG59O1xuXG5FcnJvci5wcm90b3R5cGUubGluZU51bWJlciA9IGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiB0aGlzLl9sb2NhdGlvbi5saW5lTnVtYmVyKCk7XG59O1xuXG5FcnJvci5wcm90b3R5cGUuY2hhcmFjdGVyTnVtYmVyID0gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIHRoaXMuX2xvY2F0aW9uLmNoYXJhY3Rlck51bWJlcigpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/lazy-iterators.js":
/*!************************************************!*\
  !*** ./node_modules/lop/lib/lazy-iterators.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("var fromArray = exports.fromArray = function(array) {\n    var index = 0;\n    var hasNext = function() {\n        return index < array.length;\n    };\n    return new LazyIterator({\n        hasNext: hasNext,\n        next: function() {\n            if (!hasNext()) {\n                throw new Error(\"No more elements\");\n            } else {\n                return array[index++];\n            }\n        }\n    });\n};\n\nvar LazyIterator = function(iterator) {\n    this._iterator = iterator;\n};\n\nLazyIterator.prototype.map = function(func) {\n    var iterator = this._iterator;\n    return new LazyIterator({\n        hasNext: function() {\n            return iterator.hasNext();\n        },\n        next: function() {\n            return func(iterator.next());\n        }\n    });\n};\n\nLazyIterator.prototype.filter = function(condition) {\n    var iterator = this._iterator;\n    \n    var moved = false;\n    var hasNext = false;\n    var next;\n    var moveIfNecessary = function() {\n        if (moved) {\n            return;\n        }\n        moved = true;\n        hasNext = false;\n        while (iterator.hasNext() && !hasNext) {\n            next = iterator.next();\n            hasNext = condition(next);\n        }\n    };\n    \n    return new LazyIterator({\n        hasNext: function() {\n            moveIfNecessary();\n            return hasNext;\n        },\n        next: function() {\n            moveIfNecessary();\n            var toReturn = next;\n            moved = false;\n            return toReturn;\n        }\n    });\n};\n\nLazyIterator.prototype.first = function() {\n    var iterator = this._iterator;\n    if (this._iterator.hasNext()) {\n        return iterator.next();\n    } else {\n        return null;\n    }\n};\n\nLazyIterator.prototype.toArray = function() {\n    var result = [];\n    while (this._iterator.hasNext()) {\n        result.push(this._iterator.next());\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/lazy-iterators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/parser.js":
/*!****************************************!*\
  !*** ./node_modules/lop/lib/parser.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var TokenIterator = __webpack_require__(/*! ./TokenIterator */ \"(rsc)/./node_modules/lop/lib/TokenIterator.js\");\n\nexports.Parser = function(options) {\n    var parseTokens = function(parser, tokens) {\n        return parser(new TokenIterator(tokens));\n    };\n    \n    return {\n        parseTokens: parseTokens\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbG9wL2xpYi9wYXJzZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsb0JBQW9CLG1CQUFPLENBQUMsc0VBQWlCOztBQUU3QyxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxCbGlua0ZpbmRcXEJsaW5rRmluZC1XZWJcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGxvcFxcbGliXFxwYXJzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIFRva2VuSXRlcmF0b3IgPSByZXF1aXJlKFwiLi9Ub2tlbkl0ZXJhdG9yXCIpO1xuXG5leHBvcnRzLlBhcnNlciA9IGZ1bmN0aW9uKG9wdGlvbnMpIHtcbiAgICB2YXIgcGFyc2VUb2tlbnMgPSBmdW5jdGlvbihwYXJzZXIsIHRva2Vucykge1xuICAgICAgICByZXR1cm4gcGFyc2VyKG5ldyBUb2tlbkl0ZXJhdG9yKHRva2VucykpO1xuICAgIH07XG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgICAgcGFyc2VUb2tlbnM6IHBhcnNlVG9rZW5zXG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/parsing-results.js":
/*!*************************************************!*\
  !*** ./node_modules/lop/lib/parsing-results.js ***!
  \*************************************************/
/***/ ((module) => {

eval("module.exports = {\n    failure: function(errors, remaining) {\n        if (errors.length < 1) {\n            throw new Error(\"Failure must have errors\");\n        }\n        return new Result({\n            status: \"failure\",\n            remaining: remaining,\n            errors: errors\n        });\n    },\n    error: function(errors, remaining) {\n        if (errors.length < 1) {\n            throw new Error(\"Failure must have errors\");\n        }\n        return new Result({\n            status: \"error\",\n            remaining: remaining,\n            errors: errors\n        });\n    },\n    success: function(value, remaining, source) {\n        return new Result({\n            status: \"success\",\n            value: value,\n            source: source,\n            remaining: remaining,\n            errors: []\n        });\n    },\n    cut: function(remaining) {\n        return new Result({\n            status: \"cut\",\n            remaining: remaining,\n            errors: []\n        });\n    }\n};\n\nvar Result = function(options) {\n    this._value = options.value;\n    this._status = options.status;\n    this._hasValue = options.value !== undefined;\n    this._remaining = options.remaining;\n    this._source = options.source;\n    this._errors = options.errors;\n};\n\nResult.prototype.map = function(func) {\n    if (this._hasValue) {\n        return new Result({\n            value: func(this._value, this._source),\n            status: this._status,\n            remaining: this._remaining,\n            source: this._source,\n            errors: this._errors\n        });\n    } else {\n        return this;\n    }\n};\n\nResult.prototype.changeRemaining = function(remaining) {\n    return new Result({\n        value: this._value,\n        status: this._status,\n        remaining: remaining,\n        source: this._source,\n        errors: this._errors\n    });\n};\n\nResult.prototype.isSuccess = function() {\n    return this._status === \"success\" || this._status === \"cut\";\n};\n\nResult.prototype.isFailure = function() {\n    return this._status === \"failure\";\n};\n\nResult.prototype.isError = function() {\n    return this._status === \"error\";\n};\n\nResult.prototype.isCut = function() {\n    return this._status === \"cut\";\n};\n\nResult.prototype.value = function() {\n    return this._value;\n};\n\nResult.prototype.remaining = function() {\n    return this._remaining;\n};\n\nResult.prototype.source = function() {\n    return this._source;\n};\n\nResult.prototype.errors = function() {\n    return this._errors;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/parsing-results.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/regex-tokeniser.js":
/*!*************************************************!*\
  !*** ./node_modules/lop/lib/regex-tokeniser.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var Token = __webpack_require__(/*! ./Token */ \"(rsc)/./node_modules/lop/lib/Token.js\");\nvar StringSource = __webpack_require__(/*! ./StringSource */ \"(rsc)/./node_modules/lop/lib/StringSource.js\");\n\nexports.RegexTokeniser = RegexTokeniser;\n\nfunction RegexTokeniser(rules) {\n    rules = rules.map(function(rule) {\n        return {\n            name: rule.name,\n            regex: new RegExp(rule.regex.source, \"g\")\n        };\n    });\n    \n    function tokenise(input, description) {\n        var source = new StringSource(input, description);\n        var index = 0;\n        var tokens = [];\n    \n        while (index < input.length) {\n            var result = readNextToken(input, index, source);\n            index = result.endIndex;\n            tokens.push(result.token);\n        }\n        \n        tokens.push(endToken(input, source));\n        return tokens;\n    }\n\n    function readNextToken(string, startIndex, source) {\n        for (var i = 0; i < rules.length; i++) {\n            var regex = rules[i].regex;\n            regex.lastIndex = startIndex;\n            var result = regex.exec(string);\n            \n            if (result) {\n                var endIndex = startIndex + result[0].length;\n                if (result.index === startIndex && endIndex > startIndex) {\n                    var value = result[1];\n                    var token = new Token(\n                        rules[i].name,\n                        value,\n                        source.range(startIndex, endIndex)\n                    );\n                    return {token: token, endIndex: endIndex};\n                }\n            }\n        }\n        var endIndex = startIndex + 1;\n        var token = new Token(\n            \"unrecognisedCharacter\",\n            string.substring(startIndex, endIndex),\n            source.range(startIndex, endIndex)\n        );\n        return {token: token, endIndex: endIndex};\n    }\n    \n    function endToken(input, source) {\n        return new Token(\n            \"end\",\n            null,\n            source.range(input.length, input.length)\n        );\n    }\n    \n    return {\n        tokenise: tokenise\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/regex-tokeniser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/lop/lib/rules.js":
/*!***************************************!*\
  !*** ./node_modules/lop/lib/rules.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(rsc)/./node_modules/underscore/modules/index-all.js\");\nvar options = __webpack_require__(/*! option */ \"(rsc)/./node_modules/option/index.js\");\nvar results = __webpack_require__(/*! ./parsing-results */ \"(rsc)/./node_modules/lop/lib/parsing-results.js\");\nvar errors = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/lop/lib/errors.js\");\nvar lazyIterators = __webpack_require__(/*! ./lazy-iterators */ \"(rsc)/./node_modules/lop/lib/lazy-iterators.js\");\n\nexports.token = function(tokenType, value) {\n    var matchValue = value !== undefined;\n    return function(input) {\n        var token = input.head();\n        if (token && token.name === tokenType && (!matchValue || token.value === value)) {\n            return results.success(token.value, input.tail(), token.source);\n        } else {\n            var expected = describeToken({name: tokenType, value: value});\n            return describeTokenMismatch(input, expected);\n        }\n    };\n};\n\nexports.tokenOfType = function(tokenType) {\n    return exports.token(tokenType);\n};\n\nexports.firstOf = function(name, parsers) {\n    if (!_.isArray(parsers)) {\n        parsers = Array.prototype.slice.call(arguments, 1);\n    }\n    return function(input) {\n        return lazyIterators\n            .fromArray(parsers)\n            .map(function(parser) {\n                return parser(input);\n            })\n            .filter(function(result) {\n                return result.isSuccess() || result.isError();\n            })\n            .first() || describeTokenMismatch(input, name);\n    };\n};\n\nexports.then = function(parser, func) {\n    return function(input) {\n        var result = parser(input);\n        if (!result.map) {\n            console.log(result);\n        }\n        return result.map(func);\n    };\n};\n\nexports.sequence = function() {\n    var parsers = Array.prototype.slice.call(arguments, 0);\n    var rule = function(input) {\n        var result = _.foldl(parsers, function(memo, parser) {\n            var result = memo.result;\n            var hasCut = memo.hasCut;\n            if (!result.isSuccess()) {\n                return {result: result, hasCut: hasCut};\n            }\n            var subResult = parser(result.remaining());\n            if (subResult.isCut()) {\n                return {result: result, hasCut: true};\n            } else if (subResult.isSuccess()) {\n                var values;\n                if (parser.isCaptured) {\n                    values = result.value().withValue(parser, subResult.value());\n                } else {\n                    values = result.value();\n                }\n                var remaining = subResult.remaining();\n                var source = input.to(remaining);\n                return {\n                    result: results.success(values, remaining, source),\n                    hasCut: hasCut\n                };\n            } else if (hasCut) {\n                return {result: results.error(subResult.errors(), subResult.remaining()), hasCut: hasCut};\n            } else {\n                return {result: subResult, hasCut: hasCut};\n            }\n        }, {result: results.success(new SequenceValues(), input), hasCut: false}).result;\n        var source = input.to(result.remaining());\n        return result.map(function(values) {\n            return values.withValue(exports.sequence.source, source);\n        });\n    };\n    rule.head = function() {\n        var firstCapture = _.find(parsers, isCapturedRule);\n        return exports.then(\n            rule,\n            exports.sequence.extract(firstCapture)\n        );\n    };\n    rule.map = function(func) {\n        return exports.then(\n            rule,\n            function(result) {\n                return func.apply(this, result.toArray());\n            }\n        );\n    };\n    \n    function isCapturedRule(subRule) {\n        return subRule.isCaptured;\n    }\n    \n    return rule;\n};\n\nvar SequenceValues = function(values, valuesArray) {\n    this._values = values || {};\n    this._valuesArray = valuesArray || [];\n};\n\nSequenceValues.prototype.withValue = function(rule, value) {\n    if (rule.captureName && rule.captureName in this._values) {\n        throw new Error(\"Cannot add second value for capture \\\"\" + rule.captureName + \"\\\"\");\n    } else {\n        var newValues = _.clone(this._values);\n        newValues[rule.captureName] = value;\n        var newValuesArray = this._valuesArray.concat([value]);\n        return new SequenceValues(newValues, newValuesArray);\n    }\n};\n\nSequenceValues.prototype.get = function(rule) {\n    if (rule.captureName in this._values) {\n        return this._values[rule.captureName];\n    } else {\n        throw new Error(\"No value for capture \\\"\" + rule.captureName + \"\\\"\");\n    }\n};\n\nSequenceValues.prototype.toArray = function() {\n    return this._valuesArray;\n};\n\nexports.sequence.capture = function(rule, name) {\n    var captureRule = function() {\n        return rule.apply(this, arguments);\n    };\n    captureRule.captureName = name;\n    captureRule.isCaptured = true;\n    return captureRule;\n};\n\nexports.sequence.extract = function(rule) {\n    return function(result) {\n        return result.get(rule);\n    };\n};\n\nexports.sequence.applyValues = function(func) {\n    // TODO: check captureName doesn't conflict with source or other captures\n    var rules = Array.prototype.slice.call(arguments, 1);\n    return function(result) {\n        var values = rules.map(function(rule) {\n            return result.get(rule);\n        });\n        return func.apply(this, values);\n    };\n};\n\nexports.sequence.source = {\n    captureName: \"☃source☃\"\n};\n\nexports.sequence.cut = function() {\n    return function(input) {\n        return results.cut(input);\n    };\n};\n\nexports.optional = function(rule) {\n    return function(input) {\n        var result = rule(input);\n        if (result.isSuccess()) {\n            return result.map(options.some);\n        } else if (result.isFailure()) {\n            return results.success(options.none, input);\n        } else {\n            return result;\n        }\n    };\n};\n\nexports.zeroOrMoreWithSeparator = function(rule, separator) {\n    return repeatedWithSeparator(rule, separator, false);\n};\n\nexports.oneOrMoreWithSeparator = function(rule, separator) {\n    return repeatedWithSeparator(rule, separator, true);\n};\n\nvar zeroOrMore = exports.zeroOrMore = function(rule) {\n    return function(input) {\n        var values = [];\n        var result;\n        while ((result = rule(input)) && result.isSuccess()) {\n            input = result.remaining();\n            values.push(result.value());\n        }\n        if (result.isError()) {\n            return result;\n        } else {\n            return results.success(values, input);\n        }\n    };\n};\n\nexports.oneOrMore = function(rule) {\n    return exports.oneOrMoreWithSeparator(rule, noOpRule);\n};\n\nfunction noOpRule(input) {\n    return results.success(null, input);\n}\n\nvar repeatedWithSeparator = function(rule, separator, isOneOrMore) {\n    return function(input) {\n        var result = rule(input);\n        if (result.isSuccess()) {\n            var mainRule = exports.sequence.capture(rule, \"main\");\n            var remainingRule = zeroOrMore(exports.then(\n                exports.sequence(separator, mainRule),\n                exports.sequence.extract(mainRule)\n            ));\n            var remainingResult = remainingRule(result.remaining());\n            return results.success([result.value()].concat(remainingResult.value()), remainingResult.remaining());\n        } else if (isOneOrMore || result.isError()) {\n            return result;\n        } else {\n            return results.success([], input);\n        }\n    };\n};\n\nexports.leftAssociative = function(leftRule, rightRule, func) {\n    var rights;\n    if (func) {\n        rights = [{func: func, rule: rightRule}];\n    } else {\n        rights = rightRule;\n    }\n    rights = rights.map(function(right) {\n        return exports.then(right.rule, function(rightValue) {\n            return function(leftValue, source) {\n                return right.func(leftValue, rightValue, source);\n            };\n        });\n    });\n    var repeatedRule = exports.firstOf.apply(null, [\"rules\"].concat(rights));\n    \n    return function(input) {\n        var start = input;\n        var leftResult = leftRule(input);\n        if (!leftResult.isSuccess()) {\n            return leftResult;\n        }\n        var repeatedResult = repeatedRule(leftResult.remaining());\n        while (repeatedResult.isSuccess()) {\n            var remaining = repeatedResult.remaining();\n            var source = start.to(repeatedResult.remaining());\n            var right = repeatedResult.value();\n            leftResult = results.success(\n                right(leftResult.value(), source),\n                remaining,\n                source\n            );\n            repeatedResult = repeatedRule(leftResult.remaining());\n        }\n        if (repeatedResult.isError()) {\n            return repeatedResult;\n        }\n        return leftResult;\n    };\n};\n\nexports.leftAssociative.firstOf = function() {\n    return Array.prototype.slice.call(arguments, 0);\n};\n\nexports.nonConsuming = function(rule) {\n    return function(input) {\n        return rule(input).changeRemaining(input);\n    };\n};\n\nvar describeToken = function(token) {\n    if (token.value) {\n        return token.name + \" \\\"\" + token.value + \"\\\"\";\n    } else {\n        return token.name;\n    }\n};\n\nfunction describeTokenMismatch(input, expected) {\n    var error;\n    var token = input.head();\n    if (token) {\n        error = errors.error({\n            expected: expected,\n            actual: describeToken(token),\n            location: token.source\n        });\n    } else {\n        error = errors.error({\n            expected: expected,\n            actual: \"end of tokens\"\n        });\n    }\n    return results.failure([error], input);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lop/lib/rules.js\n");

/***/ })

};
;