"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { 
  PlusCircle, 
  Upload, 
  Zap, 
  FileText,
  <PERSON>rk<PERSON>,
  Target
} from 'lucide-react';

const ResumeBuilderModeToggle = ({ currentMode, onModeChange }) => {
  const modes = [
    {
      id: 'create',
      title: 'Create New Resume',
      description: 'Build a professional resume from scratch with AI assistance',
      icon: PlusCircle,
      color: 'from-neural-purple to-neural-pink',
      features: ['Step-by-step form', 'AI content enhancement', 'ATS optimization', 'Professional templates']
    },
    {
      id: 'upload',
      title: 'Upload & Enhance',
      description: 'Upload your existing resume and get AI-powered improvements',
      icon: Upload,
      color: 'from-neural-blue to-neural-purple',
      features: ['Text extraction', 'AI enhancement', 'Auto-fill forms', 'Before/after comparison']
    },
    {
      id: 'analyze',
      title: 'Quick ATS Check',
      description: 'Get instant ATS compatibility score and recommendations',
      icon: Zap,
      color: 'from-green-500 to-neural-blue',
      features: ['Instant analysis', 'ATS score breakdown', 'Keyword analysis', 'Quick recommendations']
    }
  ];

  return (
    <div className="mb-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
          Choose Your Resume Building Method
        </h2>
        <p className="text-gray-300 max-w-2xl mx-auto">
          Select the approach that works best for you - create from scratch, enhance an existing resume, or get a quick ATS analysis
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {modes.map((mode, index) => {
          const Icon = mode.icon;
          const isActive = currentMode === mode.id;
          
          return (
            <motion.div
              key={mode.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`relative cursor-pointer group ${
                isActive ? 'ring-2 ring-neural-purple' : ''
              }`}
              onClick={() => onModeChange(mode.id)}
            >
              <div className={`
                relative overflow-hidden rounded-2xl p-6 border transition-all duration-300
                ${isActive 
                  ? 'bg-gray-800/80 border-neural-purple shadow-lg shadow-neural-purple/25' 
                  : 'bg-gray-900/50 border-gray-700 hover:border-gray-600 hover:bg-gray-800/60'
                }
              `}>
                {/* Background gradient */}
                <div className={`
                  absolute inset-0 bg-gradient-to-br ${mode.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300
                `} />
                
                {/* Active indicator */}
                {isActive && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute top-4 right-4 w-3 h-3 bg-neural-purple rounded-full"
                  />
                )}

                <div className="relative z-10">
                  {/* Icon */}
                  <div className={`
                    inline-flex items-center justify-center w-12 h-12 rounded-xl mb-4
                    ${isActive 
                      ? `bg-gradient-to-br ${mode.color}` 
                      : 'bg-gray-800 group-hover:bg-gray-700'
                    }
                  `}>
                    <Icon className={`h-6 w-6 ${isActive ? 'text-white' : 'text-gray-400'}`} />
                  </div>

                  {/* Title */}
                  <h3 className={`
                    text-lg font-bold mb-2 transition-colors duration-300
                    ${isActive ? 'text-white' : 'text-gray-200 group-hover:text-white'}
                  `}>
                    {mode.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-400 text-sm mb-4 leading-relaxed">
                    {mode.description}
                  </p>

                  {/* Features */}
                  <ul className="space-y-2">
                    {mode.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-2 text-xs text-gray-500">
                        <div className={`
                          w-1.5 h-1.5 rounded-full
                          ${isActive ? 'bg-neural-purple' : 'bg-gray-600'}
                        `} />
                        {feature}
                      </li>
                    ))}
                  </ul>

                  {/* Action indicator */}
                  <motion.div
                    className={`
                      mt-4 text-xs font-semibold transition-colors duration-300
                      ${isActive 
                        ? 'text-neural-purple' 
                        : 'text-gray-600 group-hover:text-gray-400'
                      }
                    `}
                    whileHover={{ x: 2 }}
                  >
                    {isActive ? 'Selected' : 'Click to select'} →
                  </motion.div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Mode-specific info */}
      <motion.div
        key={currentMode}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mt-6 text-center"
      >
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-gray-800/50 rounded-full border border-gray-700">
          {currentMode === 'create' && (
            <>
              <Sparkles className="h-4 w-4 text-neural-purple" />
              <span className="text-sm text-gray-300">AI-powered resume creation</span>
            </>
          )}
          {currentMode === 'upload' && (
            <>
              <FileText className="h-4 w-4 text-neural-blue" />
              <span className="text-sm text-gray-300">Smart resume enhancement</span>
            </>
          )}
          {currentMode === 'analyze' && (
            <>
              <Target className="h-4 w-4 text-green-400" />
              <span className="text-sm text-gray-300">Instant ATS analysis</span>
            </>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default ResumeBuilderModeToggle;
