'use client'
import { motion } from 'framer-motion'
import { SparklesIcon } from '@heroicons/react/24/solid'
import  Link  from 'next/link'

const Hero = () => {
  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A]">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      {/* Floating AI nodes */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-neural-purple to-neural-pink opacity-30 blur-2xl"
            style={{
              width: Math.random() * 300 + 200,
              height: Math.random() * 300 + 200,
              left: Math.random() * 100 + '%',
              top: Math.random() * 100 + '%',
            }}
            animate={{
              x: [0, Math.random() * 100 - 50, 0],
              y: [0, Math.random() * 100 - 50, 0],
              scale: [1, 1.1, 1],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{
              duration: Math.random() * 20 + 15,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="relative mx-auto max-w-7xl px-6 py-20 sm:py-28 lg:flex lg:items-center lg:gap-x-12 lg:px-8 lg:py-32">
        <div className="mx-auto max-w-3xl lg:mx-0 lg:flex-auto lg:w-[65%]">
          {/* Enhanced Badge */}
          <motion.div
            className="flex items-center gap-x-3 mb-8"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="relative">
              <SparklesIcon className="h-7 w-7 text-neural-pink animate-pulse" />
              <div className="absolute inset-0 h-7 w-7 bg-neural-pink opacity-20 rounded-full blur-md"></div>
            </div>
            <span className="inline-flex items-center rounded-full bg-gradient-to-r from-neural-purple/20 to-neural-pink/20 px-4 py-2 text-sm font-semibold text-neural-blue border border-neural-purple/30 backdrop-blur-sm">
              ✨ AI-Powered Resume Builder
            </span>
          </motion.div>

          {/* Modern Headline */}
          <motion.h1
            className="text-5xl font-extrabold tracking-tight text-white sm:text-7xl lg:text-8xl leading-tight"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <span className="block">Land Your</span>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-neural-purple via-neural-pink to-neural-blue animate-gradient-x">
              Dream Job
            </span>
            <span className="block text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-300 mt-2">
              with AI-Crafted Resumes
            </span>
          </motion.h1>

          {/* Enhanced Description */}
          <motion.div
            className="mt-8 space-y-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <p className="text-xl leading-relaxed text-gray-200 max-w-2xl">
              Transform your career with our intelligent resume builder. Get past ATS systems,
              impress recruiters, and land more interviews with professionally optimized resumes.
            </p>

            {/* Feature highlights */}
            <div className="flex flex-wrap gap-4 mt-6">
              {[
                "🎯 ATS-Optimized",
                "⚡ Built in Minutes",
                "🧠 AI-Enhanced Content",
                "📊 Success Analytics"
              ].map((feature, index) => (
                <motion.span
                  key={feature}
                  className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-white/10 text-gray-200 border border-white/20 backdrop-blur-sm"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                >
                  {feature}
                </motion.span>
              ))}
            </div>
          </motion.div>

          {/* Enhanced CTA Section */}
          <motion.div
            className="mt-12 flex flex-col sm:flex-row items-start sm:items-center gap-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <Link href="/resume-builder">
              <motion.button
                whileHover={{ scale: 1.02, boxShadow: "0 20px 40px rgba(139, 92, 246, 0.3)" }}
                whileTap={{ scale: 0.98 }}
                className="group relative inline-flex items-center gap-3 rounded-2xl bg-gradient-to-r from-neural-purple to-neural-pink px-8 py-4 text-lg font-bold text-white shadow-2xl transition-all duration-300 hover:shadow-neural-purple/25 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-neural-purple"
              >
                <span className="relative z-10">Start Building Now</span>
                <motion.div
                  className="relative z-10"
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  →
                </motion.div>
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-neural-purple to-neural-pink opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300"></div>
              </motion.button>
            </Link>

            <div className="flex items-center gap-4">
              <motion.a
                href="#examples"
                className="group flex items-center gap-2 text-lg font-semibold text-gray-300 hover:text-white transition-colors duration-300"
                whileHover={{ x: 5 }}
              >
                <span>View Examples</span>
                <motion.span
                  className="text-neural-purple group-hover:text-neural-pink transition-colors duration-300"
                  animate={{ x: [0, 3, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  →
                </motion.span>
              </motion.a>

              <div className="hidden sm:block w-px h-6 bg-gray-600"></div>

              <div className="flex items-center gap-2 text-sm text-gray-400">
                <div className="flex -space-x-2">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="w-8 h-8 rounded-full bg-gradient-to-r from-neural-purple to-neural-pink border-2 border-gray-900"></div>
                  ))}
                </div>
                <span>10,000+ resumes created</span>
              </div>
            </div>
          </motion.div>
        </div>
        <div className="mt-16 sm:mt-24 lg:mt-0 lg:w-[35%]">
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            {/* Enhanced glow effect */}
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-tr from-neural-purple via-neural-pink to-neural-blue opacity-40 blur-2xl animate-pulse" />

            {/* Modern preview container */}
            <div className="relative rounded-3xl bg-gray-900/20 p-3 ring-1 ring-white/20 backdrop-blur-xl border border-white/10">
              <div className="h-[520px] w-full rounded-2xl bg-gradient-to-br from-gray-900/80 to-black/80 p-5 overflow-hidden relative">

                {/* Header with enhanced styling */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="flex gap-1.5">
                      <div className="h-3 w-3 rounded-full bg-red-500/80"></div>
                      <div className="h-3 w-3 rounded-full bg-yellow-500/80"></div>
                      <div className="h-3 w-3 rounded-full bg-green-500/80"></div>
                    </div>
                    <div className="text-sm font-medium text-gray-300">AI Resume Builder</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-neural-pink animate-pulse"></div>
                    <span className="text-xs text-gray-400">Live Preview</span>
                  </div>
                </div>

                {/* Enhanced content sections */}
                <div className="space-y-4 overflow-y-auto h-[420px] pr-2 scrollbar-thin scrollbar-thumb-neural-purple/50">

                  {/* AI Enhancement Badge */}
                  <motion.div
                    className="bg-gradient-to-r from-neural-purple/20 to-neural-pink/20 p-4 rounded-xl border border-neural-purple/30 backdrop-blur-sm"
                    animate={{ scale: [1, 1.02, 1] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <SparklesIcon className="h-4 w-4 text-neural-pink" />
                      <div className="text-xs font-semibold text-neural-blue">AI Enhancement Active</div>
                    </div>
                    <div className="text-xs text-gray-200 leading-relaxed">
                      "Optimizing keywords for Software Engineer role... Adding quantified achievements..."
                    </div>
                  </motion.div>

                  {/* Professional Summary */}
                  <div className="bg-gray-800/60 p-4 rounded-xl border-l-4 border-neural-blue backdrop-blur-sm">
                    <div className="text-xs font-semibold text-neural-blue mb-2 flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-neural-blue"></div>
                      Professional Summary
                    </div>
                    <div className="text-xs text-gray-200 leading-relaxed">
                      <p className="font-medium text-white">Rahul Sharma</p>
                      <p className="text-gray-300 mt-1">Senior Software Engineer with 5+ years of experience in full-stack development...</p>
                    </div>
                  </div>

                  {/* Experience Section */}
                  <div className="bg-gray-800/60 p-4 rounded-xl border-l-4 border-neural-purple backdrop-blur-sm">
                    <div className="text-xs font-semibold text-neural-purple mb-2 flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-neural-purple"></div>
                      Experience
                    </div>
                    <div className="text-xs text-gray-200">
                      <p className="font-medium text-white">Senior Software Developer</p>
                      <p className="text-gray-400 text-[0.65rem]">Tata Consultancy Services | 2020-Present</p>
                      <ul className="mt-2 space-y-1 text-[0.65rem] text-gray-300">
                        <li className="flex items-start gap-2">
                          <span className="text-neural-purple mt-0.5">•</span>
                          <span>Led development of 3 high-traffic applications serving 10,000+ users</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="text-neural-purple mt-0.5">•</span>
                          <span>Improved system performance by 60% through optimization</span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* ATS Score */}
                  <div className="bg-gradient-to-r from-green-900/30 to-neural-blue/30 p-4 rounded-xl border border-green-500/30 backdrop-blur-sm">
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-xs font-semibold text-green-400 flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-400"></div>
                        ATS Compatibility Score
                      </div>
                      <span className="text-xs font-bold text-green-400">94%</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 bg-gray-700 rounded-full h-2">
                        <motion.div
                          className="bg-gradient-to-r from-green-400 to-neural-blue h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: '94%' }}
                          transition={{ duration: 2, delay: 1 }}
                        />
                      </div>
                      <span className="text-xs text-green-400 font-medium">Excellent</span>
                    </div>
                  </div>

                  {/* Skills Section */}
                  <div className="bg-gray-800/60 p-4 rounded-xl border-l-4 border-neural-pink backdrop-blur-sm">
                    <div className="text-xs font-semibold text-neural-pink mb-2 flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-neural-pink"></div>
                      Core Competencies
                    </div>
                    <div className="flex flex-wrap gap-1.5">
                      {['React', 'Node.js', 'Python', 'AWS', 'MongoDB'].map((skill, index) => (
                        <motion.span
                          key={skill}
                          className="px-2 py-1 bg-neural-pink/20 text-neural-pink text-[0.65rem] rounded-full border border-neural-pink/30"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3, delay: 1.2 + index * 0.1 }}
                        >
                          {skill}
                        </motion.span>
                      ))}
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default Hero