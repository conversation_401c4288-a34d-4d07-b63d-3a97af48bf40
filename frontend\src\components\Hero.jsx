'use client'
import { motion } from 'framer-motion'
import { SparklesIcon } from '@heroicons/react/24/solid'
import  Link  from 'next/link'

const Hero = () => {
  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A]">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      {/* Floating AI nodes */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-neural-purple to-neural-pink opacity-30 blur-2xl"
            style={{
              width: Math.random() * 300 + 200,
              height: Math.random() * 300 + 200,
              left: Math.random() * 100 + '%',
              top: Math.random() * 100 + '%',
            }}
            animate={{
              x: [0, Math.random() * 100 - 50, 0],
              y: [0, Math.random() * 100 - 50, 0],
              scale: [1, 1.1, 1],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{
              duration: Math.random() * 20 + 15,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="relative mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:flex lg:items-center lg:gap-x-10 lg:px-8 lg:py-40">
        <div className="mx-auto max-w-2xl lg:mx-0 lg:flex-auto lg:w-[70%]">
          <div className="flex items-center gap-x-2">
            <SparklesIcon className="h-6 w-6 text-neural-pink" />
            <span className="text-sm font-semibold leading-6 text-neural-blue">
              AI-Powered Resume Builder
            </span>
          </div>
          <motion.h1
            className="mt-10 max-w-lg text-4xl font-bold tracking-tight text-white sm:text-6xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            Craft Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-neural-purple to-neural-pink">Perfect Resume</span> with AI
          </motion.h1>
          <p className="mt-6 text-lg leading-8 text-gray-300">
            Create professional, ATS-friendly resumes in minutes. Our AI analyzes job descriptions
            and optimizes your resume to help you land more interviews.
          </p>
          <div className="mt-10 flex items-center gap-x-6">
            <Link  href="/resume-builder">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="rounded-md bg-gradient-to-r from-neural-purple to-neural-pink px-6 py-3.5 text-sm font-semibold text-white shadow-sm hover:opacity-90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              Build Your Resume
            </motion.button>
            </Link>
            <a href="#" className="text-sm font-semibold leading-6 text-white flex items-center gap-2">
              <span>See examples</span>
              <span aria-hidden="true" className="text-neural-purple">→</span>
            </a>
          </div>
        </div>
        <div className="mt-16 sm:mt-24 lg:mt-0 lg:w-[30%]">
          <div className="relative">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-tr from-neural-purple to-neural-blue opacity-30 blur-xl" />
            <div className="relative rounded-3xl bg-gray-900/5 p-2 ring-1 ring-white/10 backdrop-blur-md">
              <div className="h-[480px] w-full rounded-2xl bg-gradient-to-br from-black to-gray-900 p-4 overflow-y-auto">
                {/* Resume builder mockup */}
                <div className="h-full w-full rounded-lg bg-gray-900 p-4">
                  <div className="flex items-center gap-2 text-gray-300 mb-4">
                    <div className="h-3 w-3 rounded-full bg-green-500" />
                    <div className="text-sm">Resume Preview</div>
                  </div>
                  <div className="space-y-4 p-2">
                    <div className="bg-gray-800/50 p-3 rounded-lg">
                      <div className="text-xs text-neural-blue mb-1">AI Suggestions</div>
                      <div className="text-xs text-gray-200">
                        "Highlight leadership in React migration project..."
                      </div>
                    </div>
                    <div className="bg-gray-800 p-3 rounded-lg border-l-4 border-neural-purple">
                      <div className="text-xs text-neural-pink mb-1">Experience</div>
                      <div className="text-xs text-gray-200">
                        <p className="font-medium">Senior Software Engineer</p>
                        <p className="text-[0.65rem] text-gray-400">XYZ Corp | 2020-Present</p>
                        <ul className="mt-2 space-y-1 text-[0.65rem]">
                          <li className="flex items-start">
                            <span className="mr-1">•</span>
                            <span>Led team of 5 engineers in React migration</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-1">•</span>
                            <span>Improved performance by 40%</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                    <div className="bg-gray-800/50 p-3 rounded-lg">
                      <div className="text-xs text-neural-blue mb-1">ATS Score</div>
                      <div className="flex items-center">
                        <div className="w-full bg-gray-700 rounded-full h-2.5">
                          <div className="bg-neural-purple h-2.5 rounded-full" style={{width: '92%'}}></div>
                        </div>
                        <span className="ml-2 text-xs text-gray-300">92% Match</span>
                      </div>
                    </div>
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <div className="text-xs text-neural-blue mb-1">Education</div>
                      <div className="text-xs text-gray-200">
                        <p className="font-medium">B.S. Computer Science</p>
                        <p className="text-[0.65rem] text-gray-400">University of Tech | 2016-2020</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Hero