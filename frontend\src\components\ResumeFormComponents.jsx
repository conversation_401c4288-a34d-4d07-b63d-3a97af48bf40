import { motion } from "framer-motion";
import { 
  Briefcase, 
  Award, 
  FileText,
  Plus,
  Trash2,
  MapPin,
  Calendar,
  CheckCircle
} from "lucide-react";

// Experience Form Component
export const ExperienceForm = ({ formData, updateFormData, addArrayItem, removeArrayItem }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <Briefcase className="h-6 w-6 text-neural-purple" />
        <h2 className="text-2xl font-bold">Work Experience</h2>
      </div>
      <button
        onClick={() => addArrayItem('experience', {
          title: "",
          company: "",
          location: "",
          startDate: "",
          endDate: "",
          current: false,
          description: ""
        })}
        className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors"
      >
        <Plus className="h-4 w-4" />
        Add Experience
      </button>
    </div>

    <div className="space-y-6">
      {formData.experience.map((exp, index) => (
        <div key={exp.id} className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Experience {index + 1}</h3>
            {formData.experience.length > 1 && (
              <button
                onClick={() => removeArrayItem('experience', exp.id)}
                className="text-red-400 hover:text-red-300 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Job Title *
              </label>
              <input
                type="text"
                value={exp.title}
                onChange={(e) => updateFormData('experience', 'title', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="Software Engineer"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Company *
              </label>
              <input
                type="text"
                value={exp.company}
                onChange={(e) => updateFormData('experience', 'company', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="Tech Corp"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Location
              </label>
              <input
                type="text"
                value={exp.location}
                onChange={(e) => updateFormData('experience', 'location', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="San Francisco, CA"
              />
            </div>

            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exp.current}
                  onChange={(e) => updateFormData('experience', 'current', e.target.checked, index)}
                  className="w-4 h-4 text-neural-purple bg-gray-800 border-gray-600 rounded focus:ring-neural-purple focus:ring-2"
                />
                <span className="text-sm text-gray-300">Currently working here</span>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Start Date
              </label>
              <input
                type="month"
                value={exp.startDate}
                onChange={(e) => updateFormData('experience', 'startDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                End Date
              </label>
              <input
                type="month"
                value={exp.endDate}
                onChange={(e) => updateFormData('experience', 'endDate', e.target.value, index)}
                disabled={exp.current}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white disabled:opacity-50 disabled:cursor-not-allowed"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Job Description & Achievements
              </label>
              <textarea
                value={exp.description}
                onChange={(e) => updateFormData('experience', 'description', e.target.value, index)}
                rows={4}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
                placeholder="• Developed and maintained web applications using React and Node.js&#10;• Led a team of 5 developers in implementing new features&#10;• Improved application performance by 40%"
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  </motion.div>
);

// Skills & Projects Form Component
export const SkillsProjectsForm = ({ formData, updateFormData, addArrayItem, removeArrayItem }) => {
  const addSkill = (category, skill) => {
    if (skill.trim() && !formData.skills[category].includes(skill.trim())) {
      const newSkills = [...formData.skills[category], skill.trim()];
      updateFormData('skills', category, newSkills);
    }
  };

  const removeSkill = (category, skillToRemove) => {
    const newSkills = formData.skills[category].filter(skill => skill !== skillToRemove);
    updateFormData('skills', category, newSkills);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-8"
    >
      <div className="flex items-center gap-3 mb-6">
        <Award className="h-6 w-6 text-neural-purple" />
        <h2 className="text-2xl font-bold">Skills & Projects</h2>
      </div>

      {/* Skills Section */}
      <div className="space-y-6">
        <h3 className="text-xl font-semibold">Skills</h3>
        
        {/* Technical Skills */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Technical Skills
          </label>
          <SkillInput 
            skills={formData.skills.technical}
            onAdd={(skill) => addSkill('technical', skill)}
            onRemove={(skill) => removeSkill('technical', skill)}
            placeholder="JavaScript, React, Node.js, Python..."
          />
        </div>

        {/* Languages */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Languages
          </label>
          <SkillInput 
            skills={formData.skills.languages}
            onAdd={(skill) => addSkill('languages', skill)}
            onRemove={(skill) => removeSkill('languages', skill)}
            placeholder="English (Native), Spanish (Fluent)..."
          />
        </div>

        {/* Certifications */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Certifications
          </label>
          <SkillInput 
            skills={formData.skills.certifications}
            onAdd={(skill) => addSkill('certifications', skill)}
            onRemove={(skill) => removeSkill('certifications', skill)}
            placeholder="AWS Certified, Google Cloud Professional..."
          />
        </div>
      </div>

      {/* Projects Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">Projects</h3>
          <button
            onClick={() => addArrayItem('projects', {
              name: "",
              description: "",
              technologies: "",
              link: ""
            })}
            className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Project
          </button>
        </div>

        <div className="space-y-4">
          {formData.projects.map((project, index) => (
            <div key={project.id} className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-lg font-semibold">Project {index + 1}</h4>
                {formData.projects.length > 1 && (
                  <button
                    onClick={() => removeArrayItem('projects', project.id)}
                    className="text-red-400 hover:text-red-300 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Project Name *
                  </label>
                  <input
                    type="text"
                    value={project.name}
                    onChange={(e) => updateFormData('projects', 'name', e.target.value, index)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                    placeholder="E-commerce Platform"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Project Link
                  </label>
                  <input
                    type="url"
                    value={project.link}
                    onChange={(e) => updateFormData('projects', 'link', e.target.value, index)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                    placeholder="https://github.com/username/project"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Technologies Used
                  </label>
                  <input
                    type="text"
                    value={project.technologies}
                    onChange={(e) => updateFormData('projects', 'technologies', e.target.value, index)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                    placeholder="React, Node.js, MongoDB"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Project Description
                  </label>
                  <textarea
                    value={project.description}
                    onChange={(e) => updateFormData('projects', 'description', e.target.value, index)}
                    rows={3}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
                    placeholder="Brief description of the project, your role, and key achievements..."
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

// Skill Input Component
const SkillInput = ({ skills, onAdd, onRemove, placeholder }) => {
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.target.value.trim()) {
      onAdd(e.target.value);
      e.target.value = '';
    }
  };

  return (
    <div className="space-y-3">
      <input
        type="text"
        onKeyPress={handleKeyPress}
        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
        placeholder={`${placeholder} (Press Enter to add)`}
      />
      <div className="flex flex-wrap gap-2">
        {skills.map((skill, index) => (
          <span
            key={index}
            className="inline-flex items-center gap-1 px-3 py-1 bg-neural-purple/20 border border-neural-purple/50 rounded-full text-sm"
          >
            {skill}
            <button
              onClick={() => onRemove(skill)}
              className="text-neural-pink hover:text-red-400 transition-colors"
            >
              ×
            </button>
          </span>
        ))}
      </div>
    </div>
  );
};

// Review Form Component
export const ReviewForm = ({ formData }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="flex items-center gap-3 mb-6">
      <FileText className="h-6 w-6 text-neural-purple" />
      <h2 className="text-2xl font-bold">Review Your Information</h2>
    </div>

    <div className="space-y-6">
      {/* Personal Info Summary */}
      <div className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-500" />
          Personal Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <p><span className="text-gray-400">Name:</span> {formData.personal.firstName} {formData.personal.lastName}</p>
          <p><span className="text-gray-400">Email:</span> {formData.personal.email}</p>
          <p><span className="text-gray-400">Phone:</span> {formData.personal.phone || 'Not provided'}</p>
          <p><span className="text-gray-400">Location:</span> {formData.personal.location || 'Not provided'}</p>
        </div>
      </div>

      {/* Education Summary */}
      <div className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-500" />
          Education ({formData.education.length})
        </h3>
        {formData.education.map((edu, index) => (
          <div key={edu.id} className="mb-3 last:mb-0">
            <p className="font-medium">{edu.degree} - {edu.institution}</p>
            <p className="text-sm text-gray-400">{edu.startDate} - {edu.endDate}</p>
          </div>
        ))}
      </div>

      {/* Experience Summary */}
      <div className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-500" />
          Experience ({formData.experience.length})
        </h3>
        {formData.experience.map((exp, index) => (
          <div key={exp.id} className="mb-3 last:mb-0">
            <p className="font-medium">{exp.title} - {exp.company}</p>
            <p className="text-sm text-gray-400">{exp.startDate} - {exp.current ? 'Present' : exp.endDate}</p>
          </div>
        ))}
      </div>

      {/* Skills Summary */}
      <div className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-500" />
          Skills & Projects
        </h3>
        <div className="space-y-2 text-sm">
          <p><span className="text-gray-400">Technical Skills:</span> {formData.skills.technical.join(', ') || 'None added'}</p>
          <p><span className="text-gray-400">Languages:</span> {formData.skills.languages.join(', ') || 'None added'}</p>
          <p><span className="text-gray-400">Projects:</span> {formData.projects.length} project(s)</p>
        </div>
      </div>
    </div>

    <div className="bg-neural-purple/10 border border-neural-purple/30 rounded-lg p-6">
      <h4 className="text-lg font-semibold mb-2 text-neural-purple">Ready to Generate!</h4>
      <p className="text-gray-300">
        Your resume information looks complete. Click "Generate Resume" to create your professional resume using AI.
      </p>
    </div>
  </motion.div>
);
