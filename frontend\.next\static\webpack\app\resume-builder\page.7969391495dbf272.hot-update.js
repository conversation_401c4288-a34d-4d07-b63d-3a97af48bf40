"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* harmony import */ var _components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ATSScoreCircle */ \"(app-pages-browser)/./src/components/ATSScoreCircle.jsx\");\n/* harmony import */ var _components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/EnhancedResumePreview */ \"(app-pages-browser)/./src/components/EnhancedResumePreview.jsx\");\n/* harmony import */ var _components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/PDFDownload */ \"(app-pages-browser)/./src/components/PDFDownload.jsx\");\n/* harmony import */ var _components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ResumeBuilderModeToggle */ \"(app-pages-browser)/./src/components/ResumeBuilderModeToggle.jsx\");\n/* harmony import */ var _components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ResumeUpload */ \"(app-pages-browser)/./src/components/ResumeUpload.jsx\");\n/* harmony import */ var _components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ATSAnalysisDisplay */ \"(app-pages-browser)/./src/components/ATSAnalysisDisplay.jsx\");\n/* harmony import */ var _components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/JobDescriptionInput */ \"(app-pages-browser)/./src/components/JobDescriptionInput.jsx\");\n/* harmony import */ var _components_BeforeAfterComparison__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/BeforeAfterComparison */ \"(app-pages-browser)/./src/components/BeforeAfterComparison.jsx\");\n/* harmony import */ var _components_AIContentSuggestions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AIContentSuggestions */ \"(app-pages-browser)/./src/components/AIContentSuggestions.jsx\");\n/* harmony import */ var _components_ATSOptimizationPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ATSOptimizationPanel */ \"(app-pages-browser)/./src/components/ATSOptimizationPanel.jsx\");\n/* harmony import */ var _components_TemplateSelector__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/TemplateSelector */ \"(app-pages-browser)/./src/components/TemplateSelector.jsx\");\n/* harmony import */ var _components_UploadEnhancementWorkflow__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/UploadEnhancementWorkflow */ \"(app-pages-browser)/./src/components/UploadEnhancementWorkflow.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* harmony import */ var _components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ATSFieldIndicator */ \"(app-pages-browser)/./src/components/ATSFieldIndicator.jsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./src/components/ClientOnly.jsx\");\n/* harmony import */ var _components_ATSTooltip__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ATSTooltip */ \"(app-pages-browser)/./src/components/ATSTooltip.jsx\");\n/* harmony import */ var _components_StepIndicator__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/StepIndicator */ \"(app-pages-browser)/./src/components/StepIndicator.jsx\");\n/* harmony import */ var _components_StickyNavigation__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/StickyNavigation */ \"(app-pages-browser)/./src/components/StickyNavigation.jsx\");\n/* harmony import */ var _components_FormHeader__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/FormHeader */ \"(app-pages-browser)/./src/components/FormHeader.jsx\");\n/* harmony import */ var _components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ResumeFormComponents */ \"(app-pages-browser)/./src/components/ResumeFormComponents.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import SuccessScreen from \"@/components/SuccessScreen\";\n\nconst ResumeBuilder = ()=>{\n    _s();\n    // Main state management\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for upload functionality\n    const [builderMode, setBuilderMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create'); // 'create', 'upload', 'analyze'\n    const [uploadAnalysis, setUploadAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAnalysis, setShowAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUploadWorkflow, setShowUploadWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced upload workflow state\n    const [showJobDescriptionInput, setShowJobDescriptionInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingTargeted, setIsGeneratingTargeted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetedResumeData, setTargetedResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Template selection state\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ]\n    });\n    // Real-time ATS analysis\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(formData);\n    // Steps configuration\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            description: \"Tell us about yourself\"\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            description: \"Your academic background\"\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            description: \"Your work experience\"\n        },\n        {\n            id: 3,\n            title: \"Skills & Projects\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            description: \"Showcase your abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            description: \"Finalize your resume\"\n        }\n    ];\n    // Update form data\n    const updateFormData = function(section, field, value) {\n        let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        setFormData((prev)=>{\n            if (index !== null && Array.isArray(prev[section])) {\n                const newArray = [\n                    ...prev[section]\n                ];\n                newArray[index] = {\n                    ...newArray[index],\n                    [field]: value\n                };\n                return {\n                    ...prev,\n                    [section]: newArray\n                };\n            } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                return {\n                    ...prev,\n                    [section]: {\n                        ...prev[section],\n                        [field]: value\n                    }\n                };\n            }\n            return prev;\n        });\n    };\n    // Add new item to array sections\n    const addArrayItem = (section, template)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: [\n                    ...prev[section],\n                    {\n                        ...template,\n                        id: Math.random().toString(36).substring(2, 11)\n                    }\n                ]\n            }));\n    };\n    // Remove item from array sections\n    const removeArrayItem = (section, id)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: prev[section].filter((item)=>item.id !== id)\n            }));\n    };\n    // Navigation functions\n    const nextStep = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    // Generate resume with AI\n    const generateResume = async ()=>{\n        try {\n            var _data_resumeData_atsScore, _data_resumeData, _data_resumeData_atsScore1, _data_resumeData1;\n            console.log('🚀 Starting resume generation...');\n            console.log('📝 Form data:', formData);\n            setIsGenerating(true);\n            setShowProgressBar(true);\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    templateId: selectedTemplate\n                })\n            });\n            console.log('📡 API response status:', response.status);\n            console.log('📡 API response headers:', Object.fromEntries(response.headers.entries()));\n            // Check if response is actually JSON\n            const contentType = response.headers.get('content-type');\n            if (!contentType || !contentType.includes('application/json')) {\n                const textResponse = await response.text();\n                console.error('❌ Non-JSON response received:', textResponse);\n                throw new Error('Server returned non-JSON response');\n            }\n            const data = await response.json();\n            console.log('📊 API response data:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            // Store the enhanced resume data and ATS information\n            console.log('✅ Setting resume data...');\n            setResumeUrl(data.downloadUrl);\n            setResumeData(data.resumeData);\n            setAtsScore(data.atsScore || ((_data_resumeData = data.resumeData) === null || _data_resumeData === void 0 ? void 0 : (_data_resumeData_atsScore = _data_resumeData.atsScore) === null || _data_resumeData_atsScore === void 0 ? void 0 : _data_resumeData_atsScore.overall) || 75);\n            setSuggestions(data.suggestions || ((_data_resumeData1 = data.resumeData) === null || _data_resumeData1 === void 0 ? void 0 : (_data_resumeData_atsScore1 = _data_resumeData1.atsScore) === null || _data_resumeData_atsScore1 === void 0 ? void 0 : _data_resumeData_atsScore1.improvements) || []);\n            console.log('🎉 Resume generation completed successfully');\n        } catch (error) {\n            console.error(\"💥 Error generating resume:\", error);\n            console.error(\"Error details:\", {\n                message: error.message,\n                stack: error.stack,\n                name: error.name\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || \"Failed to generate resume\");\n            setIsGenerating(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsGenerating(false);\n        // Show success message with ATS score\n        const scoreMessage = atsScore ? \"Resume generated! ATS Score: \".concat(atsScore, \"%\") : \"Your resume has been generated successfully!\";\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(scoreMessage);\n    };\n    // Handle upload analysis completion\n    const handleUploadAnalysis = (analysisData)=>{\n        console.log('📊 Upload analysis received:', analysisData);\n        setUploadAnalysis(analysisData);\n        // For upload & enhance mode, show job description input\n        if (builderMode === 'upload') {\n            setShowJobDescriptionInput(true);\n        } else {\n            // For quick analysis, show results immediately\n            setShowAnalysis(true);\n        }\n        // If it's a full analysis, populate form data (even with minimal data)\n        if (analysisData.analysisType === 'full' && analysisData.extractedData) {\n            const extracted = analysisData.extractedData;\n            console.log('📋 Extracted data for form population:', extracted);\n            // Update form data with extracted information\n            setFormData((prevData)=>{\n                var _extracted_personal, _extracted_personal1, _extracted_personal2, _extracted_personal3, _extracted_personal4, _extracted_personal5, _extracted_personal6, _extracted_personal7, _extracted_education, _extracted_experience, _extracted_skills_technical, _extracted_skills, _extracted_skills_languages, _extracted_skills1, _extracted_skills_certifications, _extracted_skills2, _extracted_projects;\n                return {\n                    ...prevData,\n                    personal: {\n                        ...prevData.personal,\n                        firstName: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.firstName) || prevData.personal.firstName,\n                        lastName: ((_extracted_personal1 = extracted.personal) === null || _extracted_personal1 === void 0 ? void 0 : _extracted_personal1.lastName) || prevData.personal.lastName,\n                        email: ((_extracted_personal2 = extracted.personal) === null || _extracted_personal2 === void 0 ? void 0 : _extracted_personal2.email) || prevData.personal.email,\n                        phone: ((_extracted_personal3 = extracted.personal) === null || _extracted_personal3 === void 0 ? void 0 : _extracted_personal3.phone) || prevData.personal.phone,\n                        location: ((_extracted_personal4 = extracted.personal) === null || _extracted_personal4 === void 0 ? void 0 : _extracted_personal4.location) || prevData.personal.location,\n                        linkedin: ((_extracted_personal5 = extracted.personal) === null || _extracted_personal5 === void 0 ? void 0 : _extracted_personal5.linkedin) || prevData.personal.linkedin,\n                        portfolio: ((_extracted_personal6 = extracted.personal) === null || _extracted_personal6 === void 0 ? void 0 : _extracted_personal6.portfolio) || prevData.personal.portfolio,\n                        summary: ((_extracted_personal7 = extracted.personal) === null || _extracted_personal7 === void 0 ? void 0 : _extracted_personal7.summary) || prevData.personal.summary\n                    },\n                    education: ((_extracted_education = extracted.education) === null || _extracted_education === void 0 ? void 0 : _extracted_education.length) > 0 ? extracted.education.map((edu)=>({\n                            ...edu,\n                            id: edu.id || Date.now() + Math.random()\n                        })) : prevData.education,\n                    experience: ((_extracted_experience = extracted.experience) === null || _extracted_experience === void 0 ? void 0 : _extracted_experience.length) > 0 ? extracted.experience.map((exp)=>({\n                            ...exp,\n                            id: exp.id || Date.now() + Math.random()\n                        })) : prevData.experience,\n                    skills: {\n                        technical: ((_extracted_skills = extracted.skills) === null || _extracted_skills === void 0 ? void 0 : (_extracted_skills_technical = _extracted_skills.technical) === null || _extracted_skills_technical === void 0 ? void 0 : _extracted_skills_technical.length) > 0 ? extracted.skills.technical : prevData.skills.technical,\n                        languages: ((_extracted_skills1 = extracted.skills) === null || _extracted_skills1 === void 0 ? void 0 : (_extracted_skills_languages = _extracted_skills1.languages) === null || _extracted_skills_languages === void 0 ? void 0 : _extracted_skills_languages.length) > 0 ? extracted.skills.languages : prevData.skills.languages,\n                        certifications: ((_extracted_skills2 = extracted.skills) === null || _extracted_skills2 === void 0 ? void 0 : (_extracted_skills_certifications = _extracted_skills2.certifications) === null || _extracted_skills_certifications === void 0 ? void 0 : _extracted_skills_certifications.length) > 0 ? extracted.skills.certifications : prevData.skills.certifications\n                    },\n                    projects: ((_extracted_projects = extracted.projects) === null || _extracted_projects === void 0 ? void 0 : _extracted_projects.length) > 0 ? extracted.projects.map((proj)=>({\n                            ...proj,\n                            id: proj.id || Date.now() + Math.random()\n                        })) : prevData.projects\n                };\n            });\n            // Set ATS score and suggestions\n            if (analysisData.atsScore) {\n                var _analysisData_enhancements, _analysisData_analysis;\n                setAtsScore(analysisData.atsScore.overall);\n                setSuggestions(((_analysisData_enhancements = analysisData.enhancements) === null || _analysisData_enhancements === void 0 ? void 0 : _analysisData_enhancements.suggestions) || ((_analysisData_analysis = analysisData.analysis) === null || _analysisData_analysis === void 0 ? void 0 : _analysisData_analysis.recommendations) || []);\n            }\n            console.log('✅ Form data updated with extracted information');\n        } else if (analysisData.fallback) {\n            console.log('⚠️ Using fallback data - minimal extraction');\n            // Even with fallback, try to extract any available information\n            if (analysisData.extractedData) {\n                const extracted = analysisData.extractedData;\n                setFormData((prevData)=>{\n                    var _extracted_personal;\n                    return {\n                        ...prevData,\n                        personal: {\n                            ...prevData.personal,\n                            summary: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.summary) || 'Please add your professional summary here.'\n                        }\n                    };\n                });\n            }\n        }\n    };\n    // Handle job description submission for targeted resume generation\n    const handleJobDescriptionSubmit = async (jobData)=>{\n        if (!(uploadAnalysis === null || uploadAnalysis === void 0 ? void 0 : uploadAnalysis.extractedData)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('No resume data available. Please upload a resume first.');\n            return;\n        }\n        try {\n            setIsGeneratingTargeted(true);\n            console.log('🎯 Generating targeted resume with job data:', jobData);\n            const response = await fetch('/api/generate-targeted-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    extractedResumeData: uploadAnalysis.extractedData,\n                    jobDescription: jobData.description,\n                    jobTitle: jobData.jobTitle,\n                    company: jobData.company\n                })\n            });\n            const data = await response.json();\n            console.log('📊 Targeted resume response:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate targeted resume');\n            }\n            // Update form data with enhanced resume\n            if (data.enhancedResume) {\n                var _data_enhancedResume_personal, _data_enhancedResume_personal1, _data_enhancedResume_personal2, _data_enhancedResume_personal3, _data_enhancedResume_personal4, _data_enhancedResume_personal5, _data_enhancedResume_personal6, _data_enhancedResume_personal7, _data_enhancedResume_education, _data_enhancedResume_experience, _data_enhancedResume_skills, _data_enhancedResume_skills1, _data_enhancedResume_skills2, _data_enhancedResume_projects, _data_atsScore;\n                // Properly structure the enhanced resume data to match form data structure\n                const enhancedFormData = {\n                    personal: {\n                        firstName: ((_data_enhancedResume_personal = data.enhancedResume.personal) === null || _data_enhancedResume_personal === void 0 ? void 0 : _data_enhancedResume_personal.firstName) || '',\n                        lastName: ((_data_enhancedResume_personal1 = data.enhancedResume.personal) === null || _data_enhancedResume_personal1 === void 0 ? void 0 : _data_enhancedResume_personal1.lastName) || '',\n                        email: ((_data_enhancedResume_personal2 = data.enhancedResume.personal) === null || _data_enhancedResume_personal2 === void 0 ? void 0 : _data_enhancedResume_personal2.email) || '',\n                        phone: ((_data_enhancedResume_personal3 = data.enhancedResume.personal) === null || _data_enhancedResume_personal3 === void 0 ? void 0 : _data_enhancedResume_personal3.phone) || '',\n                        location: ((_data_enhancedResume_personal4 = data.enhancedResume.personal) === null || _data_enhancedResume_personal4 === void 0 ? void 0 : _data_enhancedResume_personal4.location) || '',\n                        linkedin: ((_data_enhancedResume_personal5 = data.enhancedResume.personal) === null || _data_enhancedResume_personal5 === void 0 ? void 0 : _data_enhancedResume_personal5.linkedin) || '',\n                        portfolio: ((_data_enhancedResume_personal6 = data.enhancedResume.personal) === null || _data_enhancedResume_personal6 === void 0 ? void 0 : _data_enhancedResume_personal6.portfolio) || '',\n                        summary: ((_data_enhancedResume_personal7 = data.enhancedResume.personal) === null || _data_enhancedResume_personal7 === void 0 ? void 0 : _data_enhancedResume_personal7.summary) || ''\n                    },\n                    education: ((_data_enhancedResume_education = data.enhancedResume.education) === null || _data_enhancedResume_education === void 0 ? void 0 : _data_enhancedResume_education.length) > 0 ? data.enhancedResume.education.map((edu)=>({\n                            id: edu.id || Date.now() + Math.random(),\n                            degree: edu.degree || '',\n                            institution: edu.institution || '',\n                            location: edu.location || '',\n                            startDate: edu.startDate || '',\n                            endDate: edu.endDate || '',\n                            gpa: edu.gpa || '',\n                            relevant: edu.relevant || ''\n                        })) : [\n                        {\n                            id: 1,\n                            degree: \"\",\n                            institution: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            gpa: \"\",\n                            relevant: \"\"\n                        }\n                    ],\n                    experience: ((_data_enhancedResume_experience = data.enhancedResume.experience) === null || _data_enhancedResume_experience === void 0 ? void 0 : _data_enhancedResume_experience.length) > 0 ? data.enhancedResume.experience.map((exp)=>({\n                            id: exp.id || Date.now() + Math.random(),\n                            title: exp.title || '',\n                            company: exp.company || '',\n                            location: exp.location || '',\n                            startDate: exp.startDate || '',\n                            endDate: exp.endDate || '',\n                            current: exp.current || false,\n                            description: exp.description || (exp.achievements ? exp.achievements.join('\\n') : '')\n                        })) : [\n                        {\n                            id: 1,\n                            title: \"\",\n                            company: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            current: false,\n                            description: \"\"\n                        }\n                    ],\n                    skills: {\n                        technical: ((_data_enhancedResume_skills = data.enhancedResume.skills) === null || _data_enhancedResume_skills === void 0 ? void 0 : _data_enhancedResume_skills.technical) || [],\n                        languages: ((_data_enhancedResume_skills1 = data.enhancedResume.skills) === null || _data_enhancedResume_skills1 === void 0 ? void 0 : _data_enhancedResume_skills1.languages) || [],\n                        certifications: ((_data_enhancedResume_skills2 = data.enhancedResume.skills) === null || _data_enhancedResume_skills2 === void 0 ? void 0 : _data_enhancedResume_skills2.certifications) || []\n                    },\n                    projects: ((_data_enhancedResume_projects = data.enhancedResume.projects) === null || _data_enhancedResume_projects === void 0 ? void 0 : _data_enhancedResume_projects.length) > 0 ? data.enhancedResume.projects.map((proj)=>({\n                            id: proj.id || Date.now() + Math.random(),\n                            name: proj.name || '',\n                            description: proj.description || '',\n                            technologies: proj.technologies || '',\n                            link: proj.link || ''\n                        })) : [\n                        {\n                            id: 1,\n                            name: \"\",\n                            description: \"\",\n                            technologies: \"\",\n                            link: \"\"\n                        }\n                    ]\n                };\n                setFormData(enhancedFormData);\n                setTargetedResumeData(data);\n                setAtsScore(((_data_atsScore = data.atsScore) === null || _data_atsScore === void 0 ? void 0 : _data_atsScore.overall) || 85);\n                setSuggestions(data.recommendations || []);\n                // Show success and move to form editing\n                setShowJobDescriptionInput(false);\n                setCurrentStep(0);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume optimized for the target job!');\n            }\n        } catch (error) {\n            console.error('Targeted resume generation error:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate targeted resume');\n        } finally{\n            setIsGeneratingTargeted(false);\n        }\n    };\n    // Handle mode change\n    const handleModeChange = (mode)=>{\n        setBuilderMode(mode);\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setShowJobDescriptionInput(false);\n        setIsGeneratingTargeted(false);\n        setTargetedResumeData(null);\n        // Show enhanced workflow for upload mode\n        if (mode === 'upload') {\n            setShowUploadWorkflow(true);\n        } else {\n            setShowUploadWorkflow(false);\n        }\n    };\n    // Handle upload workflow completion\n    const handleUploadWorkflowComplete = (enhancedData)=>{\n        console.log('🎉 Upload workflow completed with data:', enhancedData);\n        // Set the enhanced data\n        if (enhancedData.enhancedResume) {\n            var _enhanced_personal, _enhanced_personal1, _enhanced_personal2, _enhanced_personal3, _enhanced_personal4, _enhanced_personal5, _enhanced_personal6, _enhanced_personal7, _enhanced_education, _enhanced_experience, _enhanced_skills, _enhanced_skills1, _enhanced_skills2, _enhanced_projects, _enhancedData_atsScore;\n            // Use enhanced resume data\n            const enhanced = enhancedData.enhancedResume;\n            setFormData({\n                personal: {\n                    firstName: ((_enhanced_personal = enhanced.personal) === null || _enhanced_personal === void 0 ? void 0 : _enhanced_personal.firstName) || '',\n                    lastName: ((_enhanced_personal1 = enhanced.personal) === null || _enhanced_personal1 === void 0 ? void 0 : _enhanced_personal1.lastName) || '',\n                    email: ((_enhanced_personal2 = enhanced.personal) === null || _enhanced_personal2 === void 0 ? void 0 : _enhanced_personal2.email) || '',\n                    phone: ((_enhanced_personal3 = enhanced.personal) === null || _enhanced_personal3 === void 0 ? void 0 : _enhanced_personal3.phone) || '',\n                    location: ((_enhanced_personal4 = enhanced.personal) === null || _enhanced_personal4 === void 0 ? void 0 : _enhanced_personal4.location) || '',\n                    linkedin: ((_enhanced_personal5 = enhanced.personal) === null || _enhanced_personal5 === void 0 ? void 0 : _enhanced_personal5.linkedin) || '',\n                    portfolio: ((_enhanced_personal6 = enhanced.personal) === null || _enhanced_personal6 === void 0 ? void 0 : _enhanced_personal6.portfolio) || '',\n                    summary: ((_enhanced_personal7 = enhanced.personal) === null || _enhanced_personal7 === void 0 ? void 0 : _enhanced_personal7.summary) || ''\n                },\n                education: ((_enhanced_education = enhanced.education) === null || _enhanced_education === void 0 ? void 0 : _enhanced_education.length) > 0 ? enhanced.education.map((edu)=>({\n                        id: edu.id || Date.now() + Math.random(),\n                        degree: edu.degree || '',\n                        institution: edu.institution || '',\n                        location: edu.location || '',\n                        startDate: edu.startDate || '',\n                        endDate: edu.endDate || '',\n                        gpa: edu.gpa || '',\n                        relevant: edu.relevant || ''\n                    })) : [\n                    {\n                        id: 1,\n                        degree: \"\",\n                        institution: \"\",\n                        location: \"\",\n                        startDate: \"\",\n                        endDate: \"\",\n                        gpa: \"\",\n                        relevant: \"\"\n                    }\n                ],\n                experience: ((_enhanced_experience = enhanced.experience) === null || _enhanced_experience === void 0 ? void 0 : _enhanced_experience.length) > 0 ? enhanced.experience.map((exp)=>({\n                        id: exp.id || Date.now() + Math.random(),\n                        title: exp.title || '',\n                        company: exp.company || '',\n                        location: exp.location || '',\n                        startDate: exp.startDate || '',\n                        endDate: exp.endDate || '',\n                        current: exp.current || false,\n                        description: exp.description || ''\n                    })) : [\n                    {\n                        id: 1,\n                        title: \"\",\n                        company: \"\",\n                        location: \"\",\n                        startDate: \"\",\n                        endDate: \"\",\n                        current: false,\n                        description: \"\"\n                    }\n                ],\n                skills: {\n                    technical: ((_enhanced_skills = enhanced.skills) === null || _enhanced_skills === void 0 ? void 0 : _enhanced_skills.technical) || [],\n                    languages: ((_enhanced_skills1 = enhanced.skills) === null || _enhanced_skills1 === void 0 ? void 0 : _enhanced_skills1.languages) || [],\n                    certifications: ((_enhanced_skills2 = enhanced.skills) === null || _enhanced_skills2 === void 0 ? void 0 : _enhanced_skills2.certifications) || []\n                },\n                projects: ((_enhanced_projects = enhanced.projects) === null || _enhanced_projects === void 0 ? void 0 : _enhanced_projects.length) > 0 ? enhanced.projects.map((proj)=>({\n                        id: proj.id || Date.now() + Math.random(),\n                        name: proj.name || '',\n                        description: proj.description || '',\n                        technologies: proj.technologies || '',\n                        link: proj.link || ''\n                    })) : [\n                    {\n                        id: 1,\n                        name: \"\",\n                        description: \"\",\n                        technologies: \"\",\n                        link: \"\"\n                    }\n                ]\n            });\n            setAtsScore(((_enhancedData_atsScore = enhancedData.atsScore) === null || _enhancedData_atsScore === void 0 ? void 0 : _enhancedData_atsScore.overall) || 85);\n            setSuggestions(enhancedData.recommendations || []);\n        } else if (enhancedData.extractedData) {\n            var _extracted_personal, _extracted_personal1, _extracted_personal2, _extracted_personal3, _extracted_personal4, _extracted_personal5, _extracted_personal6, _extracted_personal7, _extracted_education, _extracted_experience, _extracted_skills, _extracted_skills1, _extracted_skills2, _extracted_projects, _enhancedData_atsScore1, _enhancedData_enhancements;\n            // Use extracted data from upload\n            const extracted = enhancedData.extractedData;\n            setFormData({\n                personal: {\n                    firstName: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.firstName) || '',\n                    lastName: ((_extracted_personal1 = extracted.personal) === null || _extracted_personal1 === void 0 ? void 0 : _extracted_personal1.lastName) || '',\n                    email: ((_extracted_personal2 = extracted.personal) === null || _extracted_personal2 === void 0 ? void 0 : _extracted_personal2.email) || '',\n                    phone: ((_extracted_personal3 = extracted.personal) === null || _extracted_personal3 === void 0 ? void 0 : _extracted_personal3.phone) || '',\n                    location: ((_extracted_personal4 = extracted.personal) === null || _extracted_personal4 === void 0 ? void 0 : _extracted_personal4.location) || '',\n                    linkedin: ((_extracted_personal5 = extracted.personal) === null || _extracted_personal5 === void 0 ? void 0 : _extracted_personal5.linkedin) || '',\n                    portfolio: ((_extracted_personal6 = extracted.personal) === null || _extracted_personal6 === void 0 ? void 0 : _extracted_personal6.portfolio) || '',\n                    summary: ((_extracted_personal7 = extracted.personal) === null || _extracted_personal7 === void 0 ? void 0 : _extracted_personal7.summary) || ''\n                },\n                education: ((_extracted_education = extracted.education) === null || _extracted_education === void 0 ? void 0 : _extracted_education.length) > 0 ? extracted.education.map((edu)=>({\n                        id: edu.id || Date.now() + Math.random(),\n                        degree: edu.degree || '',\n                        institution: edu.institution || '',\n                        location: edu.location || '',\n                        startDate: edu.startDate || '',\n                        endDate: edu.endDate || '',\n                        gpa: edu.gpa || '',\n                        relevant: edu.relevant || ''\n                    })) : [\n                    {\n                        id: 1,\n                        degree: \"\",\n                        institution: \"\",\n                        location: \"\",\n                        startDate: \"\",\n                        endDate: \"\",\n                        gpa: \"\",\n                        relevant: \"\"\n                    }\n                ],\n                experience: ((_extracted_experience = extracted.experience) === null || _extracted_experience === void 0 ? void 0 : _extracted_experience.length) > 0 ? extracted.experience.map((exp)=>({\n                        id: exp.id || Date.now() + Math.random(),\n                        title: exp.title || '',\n                        company: exp.company || '',\n                        location: exp.location || '',\n                        startDate: exp.startDate || '',\n                        endDate: exp.endDate || '',\n                        current: exp.current || false,\n                        description: exp.description || ''\n                    })) : [\n                    {\n                        id: 1,\n                        title: \"\",\n                        company: \"\",\n                        location: \"\",\n                        startDate: \"\",\n                        endDate: \"\",\n                        current: false,\n                        description: \"\"\n                    }\n                ],\n                skills: {\n                    technical: ((_extracted_skills = extracted.skills) === null || _extracted_skills === void 0 ? void 0 : _extracted_skills.technical) || [],\n                    languages: ((_extracted_skills1 = extracted.skills) === null || _extracted_skills1 === void 0 ? void 0 : _extracted_skills1.languages) || [],\n                    certifications: ((_extracted_skills2 = extracted.skills) === null || _extracted_skills2 === void 0 ? void 0 : _extracted_skills2.certifications) || []\n                },\n                projects: ((_extracted_projects = extracted.projects) === null || _extracted_projects === void 0 ? void 0 : _extracted_projects.length) > 0 ? extracted.projects.map((proj)=>({\n                        id: proj.id || Date.now() + Math.random(),\n                        name: proj.name || '',\n                        description: proj.description || '',\n                        technologies: proj.technologies || '',\n                        link: proj.link || ''\n                    })) : [\n                    {\n                        id: 1,\n                        name: \"\",\n                        description: \"\",\n                        technologies: \"\",\n                        link: \"\"\n                    }\n                ]\n            });\n            setAtsScore(((_enhancedData_atsScore1 = enhancedData.atsScore) === null || _enhancedData_atsScore1 === void 0 ? void 0 : _enhancedData_atsScore1.overall) || 75);\n            setSuggestions(((_enhancedData_enhancements = enhancedData.enhancements) === null || _enhancedData_enhancements === void 0 ? void 0 : _enhancedData_enhancements.suggestions) || []);\n        }\n        // Hide workflow and show form\n        setShowUploadWorkflow(false);\n        setCurrentStep(0);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume data loaded! You can now edit and enhance it further.');\n    };\n    // Reset to create mode\n    const resetToCreateMode = ()=>{\n        setBuilderMode('create');\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setShowUploadWorkflow(false);\n        setFormData({\n            personal: {\n                firstName: \"\",\n                lastName: \"\",\n                email: \"\",\n                phone: \"\",\n                location: \"\",\n                linkedin: \"\",\n                portfolio: \"\",\n                summary: \"\"\n            },\n            education: [\n                {\n                    id: 1,\n                    degree: \"\",\n                    institution: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    gpa: \"\",\n                    relevant: \"\"\n                }\n            ],\n            experience: [\n                {\n                    id: 1,\n                    title: \"\",\n                    company: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    current: false,\n                    description: \"\"\n                }\n            ],\n            skills: {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: [\n                {\n                    id: 1,\n                    name: \"\",\n                    description: \"\",\n                    technologies: \"\",\n                    link: \"\"\n                }\n            ]\n        });\n    };\n    // Floating background elements with deterministic values to avoid hydration mismatch\n    const FloatingElements = ()=>{\n        // Use deterministic values to avoid hydration mismatch\n        const elements = [\n            {\n                width: 180,\n                height: 160,\n                left: 10,\n                top: 20,\n                duration: 15,\n                x: 30,\n                y: 40\n            },\n            {\n                width: 220,\n                height: 190,\n                left: 80,\n                top: 60,\n                duration: 18,\n                x: -25,\n                y: -30\n            },\n            {\n                width: 150,\n                height: 140,\n                left: 60,\n                top: 80,\n                duration: 12,\n                x: 35,\n                y: 25\n            },\n            {\n                width: 200,\n                height: 170,\n                left: 30,\n                top: 40,\n                duration: 20,\n                x: -40,\n                y: 35\n            },\n            {\n                width: 170,\n                height: 200,\n                left: 90,\n                top: 10,\n                duration: 16,\n                x: 20,\n                y: -45\n            },\n            {\n                width: 190,\n                height: 150,\n                left: 20,\n                top: 70,\n                duration: 14,\n                x: -30,\n                y: 20\n            },\n            {\n                width: 160,\n                height: 180,\n                left: 70,\n                top: 30,\n                duration: 22,\n                x: 45,\n                y: -25\n            },\n            {\n                width: 210,\n                height: 160,\n                left: 50,\n                top: 90,\n                duration: 17,\n                x: -20,\n                y: 30\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: elements.map((element, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                    className: \"absolute rounded-full opacity-10 blur-xl\",\n                    style: {\n                        backgroundColor: '#832ED3',\n                        width: element.width,\n                        height: element.height,\n                        left: element.left + '%',\n                        top: element.top + '%'\n                    },\n                    animate: {\n                        x: [\n                            0,\n                            element.x,\n                            0\n                        ],\n                        y: [\n                            0,\n                            element.y,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: element.duration,\n                        repeat: Infinity,\n                        repeatType: 'reverse',\n                        ease: \"easeInOut\"\n                    }\n                }, i, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 640,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 638,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Show upload analysis results for quick ATS check\n    if (builderMode === 'analyze' && showAnalysis && uploadAnalysis) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 671,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 670,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 674,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                        children: \"ATS Analysis Results\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Here's your comprehensive resume analysis and recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 682,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"quick\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 691,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: ()=>handleModeChange('upload'),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Enhance This Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: resetToCreateMode,\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Start Fresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 677,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 676,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 669,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show success screen if resume is generated\n    if (resumeGenerated && resumeData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 729,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 728,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 732,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                        initial: {\n                                            scale: 0\n                                        },\n                                        animate: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                            className: \"h-10 w-10 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                        children: \"Resume Generated Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300\",\n                                        children: \"Your AI-optimized resume is ready for download\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 742,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6 text-center\",\n                                                children: \"ATS Compatibility Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    score: atsScore || 75,\n                                                    size: 150\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6\",\n                                                children: \"AI Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-left\",\n                                                children: suggestions.length > 0 ? suggestions.slice(0, 4).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                        className: \"flex items-start gap-3 p-3 bg-neural-purple/10 rounded-lg border border-neural-purple/20\",\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 10\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            delay: 0.8 + index * 0.1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-neural-purple rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: typeof suggestion === 'string' ? suggestion : suggestion.message || suggestion\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 786,\n                                                        columnNumber: 23\n                                                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-center\",\n                                                    children: \"No specific recommendations at this time.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 761,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mb-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        formData: formData,\n                                        resumeData: resumeData,\n                                        templateId: selectedTemplate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__.ViewResumeButton, {\n                                        formData: formData,\n                                        resumeData: resumeData,\n                                        templateId: selectedTemplate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: ()=>setShowPreview(!showPreview),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 text-neural-blue hover:text-white font-medium rounded-xl transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 831,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? 'Hide Preview' : 'View Preview'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: ()=>{\n                                            setResumeGenerated(false);\n                                            setResumeData(null);\n                                            setAtsScore(null);\n                                            setSuggestions([]);\n                                            setCurrentStep(0);\n                                        },\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create Another Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 807,\n                                columnNumber: 13\n                            }, undefined),\n                            showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        formData: formData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 854,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 735,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 734,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 726,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormHeader__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                steps: steps,\n                onBack: ()=>window.history.back(),\n                onPreview: ()=>setShowPreview(!showPreview),\n                onSave: ()=>console.log('Save draft'),\n                showPreview: showPreview\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 874,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 886,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 885,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 889,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 891,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        className: \"text-center mb-8 md:mb-12 relative z-20\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-4 md:mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                className: \"h-8 w-8 md:h-10 md:w-10 text-neural-pink animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 906,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl lg:text-5xl font-bold text-white\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 909,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 904,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-4\",\n                                children: \"Create professional, ATS-friendly resumes in minutes with our AI-powered builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 913,\n                                columnNumber: 11\n                            }, undefined),\n                            builderMode === 'create' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            \"Step \",\n                                            currentStep + 1,\n                                            \" of \",\n                                            steps.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 919,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 898,\n                        columnNumber: 9\n                    }, undefined),\n                    (builderMode !== 'create' || currentStep === 0) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        currentMode: builderMode,\n                        onModeChange: handleModeChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 929,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'upload' || builderMode === 'analyze') && !showAnalysis && !showJobDescriptionInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            onAnalysisComplete: handleUploadAnalysis,\n                            analysisType: builderMode === 'analyze' ? 'quick' : 'full'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 942,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 937,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showJobDescriptionInput && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Uploaded Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Now provide the job description to create a targeted, ATS-optimized resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 956,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                onJobDescriptionSubmit: handleJobDescriptionSubmit,\n                                isLoading: isGeneratingTargeted\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 961,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                    onClick: ()=>{\n                                        setShowJobDescriptionInput(false);\n                                        setCurrentStep(0);\n                                    },\n                                    className: \"inline-flex items-center gap-2 px-6 py-3 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: \"Skip Job Targeting\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 966,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 951,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showAnalysis && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Analysis Complete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 990,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Review the extracted information and continue to enhance your resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 991,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 989,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 994,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                    onClick: ()=>setCurrentStep(0),\n                                    className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 1006,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Continue to Form Editor\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1000,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 999,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 984,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'create' || builderMode === 'upload' && currentStep >= 0 && !showJobDescriptionInput) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                className: \"mb-8 md:mb-12\",\n                                initial: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StepIndicator__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    currentStep: currentStep,\n                                    totalSteps: steps.length,\n                                    steps: steps,\n                                    onStepClick: (stepIndex)=>{\n                                        if (stepIndex <= currentStep) {\n                                            setCurrentStep(stepIndex);\n                                        }\n                                    },\n                                    allowClickNavigation: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1017,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/10 shadow-2xl\",\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 md:mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(steps[currentStep].icon, {\n                                                                    className: \"h-6 w-6 md:h-7 md:w-7 text-neural-purple\"\n                                                                }),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl md:text-2xl font-bold text-white\",\n                                                                    children: steps[currentStep].title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 1054,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 1050,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm md:text-base\",\n                                                            children: steps[currentStep].description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 1058,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1049,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_37__.AnimatePresence, {\n                                                    mode: \"wait\",\n                                                    children: [\n                                                        currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonalInfoForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EducationForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 1065,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__.ExperienceForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 1066,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__.SkillsProjectsForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 1067,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__.ReviewForm, {\n                                                            formData: formData,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 39\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 1042,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-4 md:p-6 border border-white/10 sticky top-4 md:top-8 shadow-2xl\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 0.4\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4 md:mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-base md:text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                        className: \"h-4 w-4 md:h-5 md:w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 1083,\n                                                                        columnNumber: 19\n                                                                    }, undefined),\n                                                                    \"Live Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 1082,\n                                                                columnNumber: 17\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                                                onClick: ()=>setShowPreview(!showPreview),\n                                                                className: \"px-3 py-1.5 md:px-4 md:py-2 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 rounded-lg text-neural-blue hover:text-white transition-all duration-300 text-sm font-medium\",\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                children: showPreview ? 'Hide' : 'Show'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 1086,\n                                                                columnNumber: 17\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-[600px] md:max-h-[700px] overflow-y-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            formData: formData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 1098,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1097,\n                                                        columnNumber: 17\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12 md:py-16 text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                            initial: {\n                                                                scale: 0.8,\n                                                                opacity: 0\n                                                            },\n                                                            animate: {\n                                                                scale: 1,\n                                                                opacity: 1\n                                                            },\n                                                            transition: {\n                                                                duration: 0.5\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                    className: \"h-16 w-16 md:h-20 md:w-20 mx-auto mb-4 opacity-30\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 1107,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm md:text-base mb-2\",\n                                                                    children: \"Preview your resume\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 1108,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs md:text-sm text-gray-500\",\n                                                                    children: 'Click \"Show\" to see live updates'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 1109,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 1102,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1101,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1075,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                className: \"mt-6\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 0.6\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSOptimizationPanel__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        formData: formData,\n                                                        atsScore: atsAnalysis.overallScore,\n                                                        suggestions: atsAnalysis.recommendations,\n                                                        realTimeAnalysis: atsAnalysis\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1122,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1039,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pb-24\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1135,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TemplateSelector__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        selectedTemplate: selectedTemplate,\n                        onTemplateSelect: setSelectedTemplate,\n                        onClose: ()=>setShowTemplateSelector(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1141,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StickyNavigation__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        currentStep: currentStep,\n                        totalSteps: steps.length,\n                        onPrevious: ()=>{\n                            if (currentStep > 0) {\n                                setCurrentStep(currentStep - 1);\n                            }\n                        },\n                        onNext: ()=>{\n                            if (currentStep < steps.length - 1) {\n                                setCurrentStep(currentStep + 1);\n                            }\n                        },\n                        onGenerate: generateResume,\n                        isGenerating: isGenerating,\n                        canProceed: true,\n                        steps: steps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1149,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 896,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 872,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"ov9cNX+p3PbbnqwGct0srf01HjQ=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    ];\n});\n_c = ResumeBuilder;\n// Personal Information Form Component\nconst PersonalInfoForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis } = param;\n    var _atsAnalysis_fieldAnalysis, _atsAnalysis_fieldAnalysis1, _atsAnalysis_fieldAnalysis2, _atsAnalysis_fieldAnalysis3;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6 md:space-y-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                \"First Name *\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSTooltip__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    fieldType: \"firstName\",\n                                    className: \"ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1184,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1182,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.firstName,\n                            onChange: (e)=>updateFormData('personal', 'firstName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Rahul\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1186,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"firstName\",\n                                value: formData.personal.firstName,\n                                analysis: (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis.firstName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1194,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1193,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1181,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Last Name *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1203,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.lastName,\n                            onChange: (e)=>updateFormData('personal', 'lastName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Sharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1206,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"lastName\",\n                                value: formData.personal.lastName,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis1 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis1 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis1.lastName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1214,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1213,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1202,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1224,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Email *\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1223,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            value: formData.personal.email,\n                            onChange: (e)=>updateFormData('personal', 'email', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"<EMAIL>\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1227,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"email\",\n                                value: formData.personal.email,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis2 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis2 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis2.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1235,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1234,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1222,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1245,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Phone\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1244,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"tel\",\n                            value: formData.personal.phone,\n                            onChange: (e)=>updateFormData('personal', 'phone', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"+91 98765 43210\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1248,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1243,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Location\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1258,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.location,\n                            onChange: (e)=>updateFormData('personal', 'location', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Mumbai, Maharashtra\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1262,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1257,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1273,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"LinkedIn\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1272,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.linkedin,\n                            onChange: (e)=>updateFormData('personal', 'linkedin', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://linkedin.com/in/rahulsharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1276,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1271,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1287,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Portfolio/Website\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1286,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.portfolio,\n                            onChange: (e)=>updateFormData('personal', 'portfolio', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://rahulsharma.dev\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1290,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1285,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Professional Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1300,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: formData.personal.summary,\n                            onChange: (e)=>updateFormData('personal', 'summary', e.target.value),\n                            rows: 4,\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 resize-none transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Experienced software engineer with 5+ years in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable solutions and leading cross-functional teams...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1303,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"summary\",\n                                value: formData.personal.summary,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis3 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis3 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis3.summary,\n                                showDetails: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1311,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1310,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIContentSuggestions__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                fieldType: \"summary\",\n                                currentValue: formData.personal.summary,\n                                onSuggestionApply: (suggestion)=>updateFormData('personal', 'summary', suggestion),\n                                context: {\n                                    firstName: formData.personal.firstName,\n                                    lastName: formData.personal.lastName,\n                                    experience: formData.experience,\n                                    skills: formData.skills\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1321,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1320,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1299,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 1180,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1174,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PersonalInfoForm;\n// Education Form Component\nconst EducationForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem, atsAnalysis } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"h-6 w-6 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1348,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1349,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1347,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addArrayItem('education', {\n                                degree: \"\",\n                                institution: \"\",\n                                location: \"\",\n                                startDate: \"\",\n                                endDate: \"\",\n                                gpa: \"\",\n                                relevant: \"\"\n                            }),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1363,\n                                columnNumber: 9\n                            }, undefined),\n                            \"Add Education\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1351,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1346,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: formData.education.map((edu, index)=>{\n                    var _atsAnalysis_fieldAnalysis, _atsAnalysis_fieldAnalysis1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 p-6 rounded-lg border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Education \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1372,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeArrayItem('education', edu.id),\n                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 1378,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1374,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1371,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Degree *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1385,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.degree,\n                                                onChange: (e)=>updateFormData('education', 'degree', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"Bachelor of Science\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1388,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    fieldName: \"degree\",\n                                                    value: edu.degree,\n                                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis[\"education_degree_\".concat(index)]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1396,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1395,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Institution *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.institution,\n                                                onChange: (e)=>updateFormData('education', 'institution', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"University of Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1408,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    fieldName: \"institution\",\n                                                    value: edu.institution,\n                                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis1 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis1 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis1[\"education_institution_\".concat(index)]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1416,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1415,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1404,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Location\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1425,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.location,\n                                                onChange: (e)=>updateFormData('education', 'location', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"New York, NY\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1429,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1424,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"GPA (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1439,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.gpa,\n                                                onChange: (e)=>updateFormData('education', 'gpa', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"3.8/4.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1442,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1438,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1453,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Start Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1452,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.startDate,\n                                                onChange: (e)=>updateFormData('education', 'startDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1451,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1466,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"End Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1465,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.endDate,\n                                                onChange: (e)=>updateFormData('education', 'endDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1469,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1464,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Relevant Coursework/Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1478,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: edu.relevant,\n                                                onChange: (e)=>updateFormData('education', 'relevant', e.target.value, index),\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"Relevant coursework, honors, achievements...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1481,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1477,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1383,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, edu.id, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1370,\n                        columnNumber: 9\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1368,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1340,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EducationForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ResumeBuilder\");\n$RefreshReg$(_c1, \"PersonalInfoForm\");\n$RefreshReg$(_c2, \"EducationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ })

});