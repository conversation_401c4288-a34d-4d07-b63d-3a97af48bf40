# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Инники сирэй
previous_label=Иннинээҕи
next.title=Аныгыскы сирэй
next_label=Аныгыскы

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=Куччат
zoom_out_label=Куччат
zoom_in.title=Улаатыннар
zoom_in_label=Улаатыннар
zoom.title=Улаатыннар
presentation_mode.title=Көрдөрөр эрэсиимҥэ
presentation_mode_label=Көрдөрөр эрэсиим
open_file.title=Билэни арый
open_file_label=Ас
print.title=Бэчээт
print_label=Бэчээт
download.title=Хачайдааһын
download_label=Хачайдааһын
bookmark.title=Билиҥҥи көстүүтэ (хатылаа эбэтэр саҥа түннүккэ арый)
bookmark_label=Билиҥҥи көстүүтэ

# Secondary toolbar and context menu
tools.title=Тэриллэр
tools_label=Тэриллэр
first_page.title=Бастакы сирэйгэ көс
first_page.label=Бастакы сирэйгэ көс
first_page_label=Бастакы сирэйгэ көс
last_page.title=Тиһэх сирэйгэ көс
last_page.label=Тиһэх сирэйгэ көс
last_page_label=Тиһэх сирэйгэ көс
page_rotate_cw.title=Чаһы хоту эргит
page_rotate_cw.label=Чаһы хоту эргит
page_rotate_cw_label=Чаһы хоту эргит
page_rotate_ccw.title=Чаһы утары эргит
page_rotate_ccw.label=Чаһы утары эргит
page_rotate_ccw_label=Чаһы утары эргит


# Document properties dialog box
document_properties.title=Докумуон туруоруулара...
document_properties_label=Докумуон туруоруулара...\u0020
document_properties_file_name=Билэ аата:
document_properties_file_size=Билэ кээмэйэ:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} КБ ({{size_b}} баайт)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} МБ ({{size_b}} баайт)
document_properties_title=Баһа:
document_properties_author=Ааптар:
document_properties_subject=Тиэмэ:
document_properties_keywords=Күлүүс тыл:
document_properties_creation_date=Оҥоһуллубут кэмэ:
document_properties_modification_date=Уларытыллыбыт кэмэ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_producer=PDF оҥорооччу:
document_properties_version=PDF барыла:
document_properties_page_count=Сирэй ахсаана:
document_properties_close=Сап

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Ойоҕос хапталы арый/сап
toggle_sidebar_label=Ойоҕос хапталы арый/сап
document_outline_label=Дөкүмүөн иһинээҕитэ
attachments.title=Кыбытыктары көрдөр
attachments_label=Кыбытык
thumbs.title=Ойуучааннары көрдөр
thumbs_label=Ойуучааннар
findbar.title=Дөкүмүөнтэн бул

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Сирэй {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Сирэй ойуучаана {{page}}

# Find panel button title and messages
find_previous.title=Этии тиэкискэ бу иннинээҕи киириитин бул
find_previous_label=Иннинээҕи
find_next.title=Этии тиэкискэ бу кэннинээҕи киириитин бул
find_next_label=Аныгыскы
find_highlight=Барытын сырдатан көрдөр
find_match_case_label=Буукуба улаханын-кыратын араар
find_reached_top=Сирэй үрдүгэр тиийдиҥ, салгыыта аллара
find_reached_bottom=Сирэй бүттэ, үөһэ салҕанна
find_not_found=Этии көстүбэтэ

# Error panel labels
error_more_info=Сиһилии
error_less_info=Сиһилиитин кистээ
error_close=Сап
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (хомуйуута: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Этии: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Стeк: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Билэ: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Устуруока: {{line}}
rendering_error=Сирэйи айарга алҕас таҕыста.

# Predefined zoom values
page_scale_width=Сирэй кэтитинэн
page_scale_fit=Сирэй кээмэйинэн
page_scale_auto=Аптамаатынан
page_scale_actual=Дьиҥнээх кээмэйэ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error_indicator=Алҕас
loading_error=PDF-билэни хачайдыырга алҕас таҕыста.
invalid_file_error=Туох эрэ алҕастаах эбэтэр алдьаммыт PDF-билэ.
missing_file_error=PDF-билэ суох.
unexpected_response_error=Сиэрбэр хоруйдаабат.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} туһунан]
password_label=Бу PDF-билэни арыйарга көмүскэл тылы киллэриэхтээхин.
password_invalid=Киирии тыл алҕастаах. Бука диэн, хатылаан көр.
password_ok=СӨП

printing_not_supported=Сэрэтии: Бу браузер бэчээттиири толору өйөөбөт.
printing_not_ready=Сэрэтии: PDF бэчээттииргэ толору хачайдана илик.
web_fonts_disabled=Ситим-бичиктэр араарыллыахтара: PDF бичиктэрэ кыайан көстүбэттэр.
document_colors_not_allowed=PDF-дөкүмүөүннэргэ бэйэлэрин өҥнөрүн туттар көҥүллэммэтэ: "Ситим-сирдэр бэйэлэрин өҥнөрүн тутталларын көҥүллүүргэ" диэн браузерга арахса сылдьар эбит.
