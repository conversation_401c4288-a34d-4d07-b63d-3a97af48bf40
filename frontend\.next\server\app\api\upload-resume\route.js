/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/upload-resume/route";
exports.ids = ["app/api/upload-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/upload-resume/route.js */ \"(rsc)/./src/app/api/upload-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/upload-resume/route\",\n        pathname: \"/api/upload-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/upload-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\upload-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$":
/*!**************************************************************************!*\
  !*** ./node_modules/pdf-parse/lib/pdf.js/ sync ^\.\/.*\/build\/pdf\.js$ ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./v1.10.100/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js",
	"./v1.10.88/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.88/build/pdf.js",
	"./v1.9.426/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.9.426/build/pdf.js",
	"./v2.0.550/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v2.0.550/build/pdf.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$";

/***/ }),

/***/ "(rsc)/./src/app/api/upload-resume/route.js":
/*!********************************************!*\
  !*** ./src/app/api/upload-resume/route.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        console.log('📄 Resume upload API called');\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const analysisType = formData.get('analysisType') || 'full'; // 'full' or 'quick'\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No file uploaded'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📁 File details:', {\n            name: file.name,\n            size: file.size,\n            type: file.type\n        });\n        // Validate file type and size\n        const allowedTypes = [\n            'application/pdf',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid file type. Please upload PDF, DOC, or DOCX files only.'\n            }, {\n                status: 400\n            });\n        }\n        // 10MB file size limit\n        if (file.size > 10 * 1024 * 1024) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'File size too large. Please upload files smaller than 10MB.'\n            }, {\n                status: 400\n            });\n        }\n        // Extract text from the uploaded file\n        const buffer = Buffer.from(await file.arrayBuffer());\n        let extractedText = '';\n        try {\n            if (file.type === 'application/pdf') {\n                console.log('📄 Processing PDF file...');\n                // Try to extract text from PDF\n                try {\n                    const pdfParse = __webpack_require__(/*! pdf-parse */ \"(rsc)/./node_modules/pdf-parse/index.js\");\n                    const pdfData = await pdfParse(buffer);\n                    extractedText = pdfData.text;\n                } catch (pdfError) {\n                    console.log('⚠️ PDF parsing failed, using fallback text extraction');\n                    // Fallback: extract basic text information\n                    extractedText = `PDF file uploaded: ${file.name}. Please provide resume details manually.`;\n                }\n            } else if (file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\n                console.log('📄 Processing Word document...');\n                try {\n                    const mammoth = __webpack_require__(/*! mammoth */ \"(rsc)/./node_modules/mammoth/lib/index.js\");\n                    const result = await mammoth.extractRawText({\n                        buffer\n                    });\n                    extractedText = result.value;\n                } catch (docError) {\n                    console.log('⚠️ Word document parsing failed, using fallback text extraction');\n                    // Fallback: extract basic text information\n                    extractedText = `Word document uploaded: ${file.name}. Please provide resume details manually.`;\n                }\n            }\n        } catch (extractionError) {\n            console.error('Text extraction error:', extractionError);\n            // Use fallback text instead of failing\n            extractedText = `Resume file uploaded: ${file.name}. File processing encountered an issue, but you can still proceed with manual entry.`;\n        }\n        if (!extractedText || extractedText.trim().length < 20) {\n            console.log('⚠️ Minimal text extracted, using fallback analysis');\n            extractedText = `Resume file: ${file.name}. File uploaded successfully but text extraction was limited. Please review and edit the form manually.`;\n        }\n        console.log('✅ Text extracted, length:', extractedText.length);\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback analysis');\n            return createFallbackAnalysis(extractedText, analysisType);\n        }\n        let analysisResult;\n        try {\n            // Get the generative model\n            const model = genAI.getGenerativeModel({\n                model: 'gemini-1.5-flash-latest',\n                generationConfig: {\n                    temperature: 0.7,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 3000\n                }\n            });\n            let prompt;\n            if (analysisType === 'quick') {\n                prompt = createQuickAnalysisPrompt(extractedText);\n            } else {\n                prompt = createFullAnalysisPrompt(extractedText);\n            }\n            console.log('🤖 Calling Gemini AI for analysis...');\n            const result = await model.generateContent(prompt);\n            const response = await result.response;\n            const generatedContent = response.text();\n            console.log('✅ Gemini analysis completed, length:', generatedContent?.length || 0);\n            // Parse the AI response\n            analysisResult = parseAIResponse(generatedContent, analysisType);\n        } catch (aiError) {\n            console.error('AI analysis error:', aiError);\n            console.log('⚠️ AI analysis failed, using fallback analysis');\n            return createFallbackAnalysis(extractedText, analysisType);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType,\n            extractedText: extractedText.substring(0, 1000) + '...',\n            ...analysisResult,\n            fileName: file.name,\n            fileSize: file.size,\n            processedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('💥 Resume upload error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process resume',\n            details:  true ? error.message : 0\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createQuickAnalysisPrompt(resumeText) {\n    return `\nYou are an expert ATS (Applicant Tracking System) analyzer. Analyze the following resume text and provide a comprehensive ATS compatibility score and recommendations.\n\nRESUME TEXT:\n${resumeText}\n\nPlease analyze the resume and respond in the following JSON format:\n{\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"analysis\": {\n    \"strengths\": [\n      \"Strong technical skills section\",\n      \"Quantified achievements in experience\"\n    ],\n    \"weaknesses\": [\n      \"Missing industry keywords\",\n      \"Inconsistent formatting\"\n    ],\n    \"recommendations\": [\n      \"Add more industry-specific keywords\",\n      \"Quantify more achievements with numbers\",\n      \"Improve section formatting consistency\"\n    ]\n  },\n  \"keywordAnalysis\": {\n    \"found\": [\"JavaScript\", \"React\", \"Node.js\"],\n    \"missing\": [\"AWS\", \"Docker\", \"Kubernetes\"],\n    \"suggestions\": [\"Add cloud technologies\", \"Include DevOps tools\"]\n  }\n}\n\nProvide detailed, actionable feedback for ATS optimization.\n`;\n}\nfunction createFullAnalysisPrompt(resumeText) {\n    return `\nYou are an expert resume writer and ATS optimization specialist. Analyze the following resume text and extract structured information while providing enhancement suggestions.\n\nRESUME TEXT:\n${resumeText}\n\nPlease extract and enhance the resume information, then respond in the following JSON format:\n{\n  \"extractedData\": {\n    \"personal\": {\n      \"firstName\": \"John\",\n      \"lastName\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"location\": \"City, State\",\n      \"linkedin\": \"linkedin.com/in/johndoe\",\n      \"portfolio\": \"johndoe.com\",\n      \"summary\": \"Professional summary extracted from resume\"\n    },\n    \"experience\": [\n      {\n        \"title\": \"Software Engineer\",\n        \"company\": \"Tech Company\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2020-01\",\n        \"endDate\": \"2023-12\",\n        \"current\": false,\n        \"description\": \"Enhanced description with quantified achievements\"\n      }\n    ],\n    \"education\": [\n      {\n        \"degree\": \"Bachelor of Science in Computer Science\",\n        \"institution\": \"University Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2016-09\",\n        \"endDate\": \"2020-05\",\n        \"gpa\": \"3.8\",\n        \"relevant\": \"Relevant coursework\"\n      }\n    ],\n    \"skills\": {\n      \"technical\": [\"JavaScript\", \"React\", \"Node.js\"],\n      \"languages\": [\"English\", \"Spanish\"],\n      \"certifications\": [\"AWS Certified\", \"Google Cloud\"]\n    },\n    \"projects\": [\n      {\n        \"name\": \"Project Name\",\n        \"description\": \"Enhanced project description\",\n        \"technologies\": \"React, Node.js, MongoDB\",\n        \"link\": \"github.com/project\"\n      }\n    ]\n  },\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"enhancements\": {\n    \"summary\": \"AI-enhanced professional summary\",\n    \"experience\": [\n      {\n        \"original\": \"Original job description\",\n        \"enhanced\": \"Enhanced description with action verbs and quantified results\"\n      }\n    ],\n    \"suggestions\": [\n      \"Add more quantified achievements\",\n      \"Include industry keywords\",\n      \"Improve formatting consistency\"\n    ]\n  }\n}\n\nExtract all available information and provide enhanced, ATS-optimized versions.\n`;\n}\nfunction parseAIResponse(generatedContent, analysisType) {\n    try {\n        // Try to extract JSON from the response\n        const jsonMatch = generatedContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            const parsedJson = JSON.parse(jsonMatch[0]);\n            return parsedJson;\n        }\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n    }\n    // Fallback response\n    if (analysisType === 'quick') {\n        return {\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            analysis: {\n                strengths: [\n                    'Professional experience listed',\n                    'Education section present'\n                ],\n                weaknesses: [\n                    'Limited quantified achievements',\n                    'Missing keywords'\n                ],\n                recommendations: [\n                    'Add more metrics and numbers',\n                    'Include industry-specific terms'\n                ]\n            }\n        };\n    } else {\n        return {\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    summary: ''\n                },\n                experience: [],\n                education: [],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: []\n            },\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            enhancements: {\n                suggestions: [\n                    'Unable to parse AI response',\n                    'Please try again'\n                ]\n            }\n        };\n    }\n}\nfunction createFallbackAnalysis(extractedText, analysisType) {\n    const basicScore = {\n        overall: 75,\n        breakdown: {\n            keywords: 70,\n            formatting: 80,\n            structure: 75,\n            achievements: 70,\n            skills: 75\n        }\n    };\n    if (analysisType === 'quick') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'quick',\n            atsScore: basicScore,\n            analysis: {\n                strengths: [\n                    'Resume file uploaded successfully',\n                    'Ready for manual review'\n                ],\n                weaknesses: [\n                    'Text extraction limited',\n                    'Manual optimization needed'\n                ],\n                recommendations: [\n                    'Review and fill out the form manually',\n                    'Add quantified achievements',\n                    'Include relevant keywords for your industry',\n                    'Ensure consistent formatting'\n                ]\n            },\n            keywordAnalysis: {\n                found: [],\n                missing: [\n                    'Industry-specific keywords',\n                    'Technical skills',\n                    'Action verbs'\n                ],\n                suggestions: [\n                    'Add relevant technical skills',\n                    'Include measurable achievements',\n                    'Use industry terminology'\n                ]\n            },\n            fallback: true\n        });\n    } else {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'full',\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    linkedin: '',\n                    portfolio: '',\n                    summary: extractedText.includes('uploaded successfully') ? '' : extractedText.substring(0, 200)\n                },\n                experience: [\n                    {\n                        id: 1,\n                        title: '',\n                        company: '',\n                        location: '',\n                        startDate: '',\n                        endDate: '',\n                        current: false,\n                        description: ''\n                    }\n                ],\n                education: [\n                    {\n                        id: 1,\n                        degree: '',\n                        institution: '',\n                        location: '',\n                        startDate: '',\n                        endDate: '',\n                        gpa: '',\n                        relevant: ''\n                    }\n                ],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: [\n                    {\n                        id: 1,\n                        name: '',\n                        description: '',\n                        technologies: '',\n                        link: ''\n                    }\n                ]\n            },\n            atsScore: basicScore,\n            enhancements: {\n                suggestions: [\n                    'File uploaded successfully - please review and edit the form',\n                    'Add your personal information',\n                    'Fill in your work experience with quantified achievements',\n                    'Include relevant technical skills',\n                    'Add education details',\n                    'Consider adding projects or certifications'\n                ]\n            },\n            fallback: true\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/upload-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/pdf-parse","vendor-chunks/next","vendor-chunks/underscore","vendor-chunks/mammoth","vendor-chunks/bluebird","vendor-chunks/jszip","vendor-chunks/xmlbuilder","vendor-chunks/pako","vendor-chunks/readable-stream","vendor-chunks/lop","vendor-chunks/@xmldom","vendor-chunks/string_decoder","vendor-chunks/inherits","vendor-chunks/dingbat-to-unicode","vendor-chunks/@google","vendor-chunks/util-deprecate","vendor-chunks/process-nextick-args","vendor-chunks/path-is-absolute","vendor-chunks/option","vendor-chunks/lie","vendor-chunks/immediate","vendor-chunks/core-util-is","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();