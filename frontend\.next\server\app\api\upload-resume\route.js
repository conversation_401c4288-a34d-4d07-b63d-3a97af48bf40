/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/upload-resume/route";
exports.ids = ["app/api/upload-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/upload-resume/route.js */ \"(rsc)/./src/app/api/upload-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/upload-resume/route\",\n        pathname: \"/api/upload-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/upload-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\upload-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ1cGxvYWQtcmVzdW1lJTJGcm91dGUmcGFnZT0lMkZhcGklMkZ1cGxvYWQtcmVzdW1lJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGdXBsb2FkLXJlc3VtZSUyRnJvdXRlLmpzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNCbGlua0ZpbmQlNUNCbGlua0ZpbmQtV2ViJTVDZnJvbnRlbmQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9anMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPXRzeCZyb290RGlyPUMlM0ElNUNQcm9qZWN0cyU1Q0JsaW5rRmluZCU1Q0JsaW5rRmluZC1XZWIlNUNmcm9udGVuZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDd0M7QUFDckg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFByb2plY3RzXFxcXEJsaW5rRmluZFxcXFxCbGlua0ZpbmQtV2ViXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHVwbG9hZC1yZXN1bWVcXFxccm91dGUuanNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3VwbG9hZC1yZXN1bWUvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS91cGxvYWQtcmVzdW1lXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS91cGxvYWQtcmVzdW1lL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcUHJvamVjdHNcXFxcQmxpbmtGaW5kXFxcXEJsaW5rRmluZC1XZWJcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcdXBsb2FkLXJlc3VtZVxcXFxyb3V0ZS5qc1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$":
/*!**************************************************************************!*\
  !*** ./node_modules/pdf-parse/lib/pdf.js/ sync ^\.\/.*\/build\/pdf\.js$ ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./v1.10.100/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js",
	"./v1.10.88/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.88/build/pdf.js",
	"./v1.9.426/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.9.426/build/pdf.js",
	"./v2.0.550/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v2.0.550/build/pdf.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$";

/***/ }),

/***/ "(rsc)/./src/app/api/upload-resume/route.js":
/*!********************************************!*\
  !*** ./src/app/api/upload-resume/route.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        console.log('📄 Resume upload API called');\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const analysisType = formData.get('analysisType') || 'full'; // 'full' or 'quick'\n        const jobDescription = formData.get('jobDescription');\n        const jobTitle = formData.get('jobTitle');\n        const company = formData.get('company');\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No file uploaded'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📁 File details:', {\n            name: file.name,\n            size: file.size,\n            type: file.type\n        });\n        // Validate file type and size\n        const allowedTypes = [\n            'application/pdf',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid file type. Please upload PDF, DOC, or DOCX files only.'\n            }, {\n                status: 400\n            });\n        }\n        // 10MB file size limit\n        if (file.size > 10 * 1024 * 1024) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'File size too large. Please upload files smaller than 10MB.'\n            }, {\n                status: 400\n            });\n        }\n        // Extract text from the uploaded file\n        const buffer = Buffer.from(await file.arrayBuffer());\n        let extractedText = '';\n        try {\n            if (file.type === 'application/pdf') {\n                console.log('📄 Processing PDF file...');\n                // Try to extract text from PDF\n                try {\n                    const pdfParse = __webpack_require__(/*! pdf-parse */ \"(rsc)/./node_modules/pdf-parse/index.js\");\n                    console.log('📄 PDF-parse module loaded successfully');\n                    const pdfData = await pdfParse(buffer, {\n                        // PDF parsing options\n                        max: 0,\n                        version: 'v1.10.100'\n                    });\n                    extractedText = pdfData.text;\n                    console.log('📄 PDF text extracted successfully, length:', extractedText.length);\n                    if (extractedText.length < 50) {\n                        console.log('⚠️ PDF text extraction yielded minimal content');\n                        // Try alternative extraction method\n                        extractedText = `PDF Content: ${pdfData.text}\\n\\nFile: ${file.name}\\nPages: ${pdfData.numpages}\\nInfo: ${JSON.stringify(pdfData.info)}`;\n                    }\n                } catch (pdfError) {\n                    console.error('⚠️ PDF parsing failed:', pdfError.message);\n                    console.log('🔄 Attempting alternative PDF processing...');\n                    // Alternative: Try to extract metadata and basic info\n                    try {\n                        const pdfParse = __webpack_require__(/*! pdf-parse */ \"(rsc)/./node_modules/pdf-parse/index.js\");\n                        const basicData = await pdfParse(buffer, {\n                            max: 1\n                        }); // Just first page\n                        extractedText = basicData.text || `PDF file uploaded: ${file.name}. Contains ${basicData.numpages || 'unknown'} pages. Please provide resume details manually.`;\n                    } catch (altError) {\n                        console.error('⚠️ Alternative PDF parsing also failed:', altError.message);\n                        extractedText = `PDF file uploaded: ${file.name}. Text extraction encountered issues. Please provide resume details manually.`;\n                    }\n                }\n            } else if (file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\n                console.log('📄 Processing Word document...');\n                try {\n                    const mammoth = __webpack_require__(/*! mammoth */ \"(rsc)/./node_modules/mammoth/lib/index.js\");\n                    console.log('📄 Mammoth module loaded successfully');\n                    // Try extracting raw text first\n                    const result = await mammoth.extractRawText({\n                        buffer\n                    });\n                    extractedText = result.value;\n                    console.log('📄 Word text extracted successfully, length:', extractedText.length);\n                    // If raw text is minimal, try HTML extraction and convert\n                    if (extractedText.length < 50) {\n                        console.log('🔄 Attempting HTML extraction from Word document...');\n                        const htmlResult = await mammoth.convertToHtml({\n                            buffer\n                        });\n                        // Strip HTML tags to get plain text\n                        extractedText = htmlResult.value.replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                        console.log('📄 HTML-to-text extraction completed, length:', extractedText.length);\n                    }\n                } catch (docError) {\n                    console.error('⚠️ Word document parsing failed:', docError.message);\n                    extractedText = `Word document uploaded: ${file.name}. Text extraction encountered issues. Please provide resume details manually.`;\n                }\n            }\n        } catch (extractionError) {\n            console.error('Text extraction error:', extractionError);\n            // Use fallback text instead of failing\n            extractedText = `Resume file uploaded: ${file.name}. File processing encountered an issue, but you can still proceed with manual entry.`;\n        }\n        if (!extractedText || extractedText.trim().length < 20) {\n            console.log('⚠️ Minimal text extracted, using fallback analysis');\n            extractedText = `Resume file: ${file.name}. File uploaded successfully but text extraction was limited. Please review and edit the form manually.`;\n        }\n        console.log('✅ Text extracted, length:', extractedText.length);\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback analysis');\n            return createFallbackAnalysis(extractedText, analysisType);\n        }\n        let analysisResult;\n        try {\n            // Get the generative model - Using Gemini 2.0 Flash (latest and most efficient)\n            const model = genAI.getGenerativeModel({\n                model: 'gemini-2.0-flash-exp',\n                generationConfig: {\n                    temperature: 0.3,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 8000\n                }\n            });\n            let prompt;\n            if (analysisType === 'quick') {\n                prompt = createQuickAnalysisPrompt(extractedText, jobDescription);\n            } else {\n                prompt = createFullAnalysisPrompt(extractedText, jobDescription, jobTitle, company);\n            }\n            console.log('🤖 Calling Gemini AI for analysis...');\n            const result = await model.generateContent(prompt);\n            const response = await result.response;\n            const generatedContent = response.text();\n            console.log('✅ Gemini analysis completed, length:', generatedContent?.length || 0);\n            // Parse the AI response\n            analysisResult = parseAIResponse(generatedContent, analysisType);\n        } catch (aiError) {\n            console.error('AI analysis error:', aiError);\n            console.log('⚠️ AI analysis failed, using fallback analysis');\n            return createFallbackAnalysis(extractedText, analysisType);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType,\n            extractedText: extractedText.substring(0, 1000) + '...',\n            ...analysisResult,\n            fileName: file.name,\n            fileSize: file.size,\n            processedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('💥 Resume upload error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process resume',\n            details:  true ? error.message : 0\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createQuickAnalysisPrompt(resumeText, jobDescription) {\n    const jobContext = jobDescription ? `\n\nJOB DESCRIPTION FOR TARGETING:\n${jobDescription}\n\nPlease analyze the resume against this specific job description for better ATS compatibility.` : '';\n    return `\nYou are an expert ATS (Applicant Tracking System) analyzer. Analyze the following resume text and provide a comprehensive ATS compatibility score and recommendations.\n\nRESUME TEXT:\n${resumeText}${jobContext}\n\nPlease analyze the resume and respond in the following JSON format:\n{\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"analysis\": {\n    \"strengths\": [\n      \"Strong technical skills section\",\n      \"Quantified achievements in experience\"\n    ],\n    \"weaknesses\": [\n      \"Missing industry keywords\",\n      \"Inconsistent formatting\"\n    ],\n    \"recommendations\": [\n      \"Add more industry-specific keywords\",\n      \"Quantify more achievements with numbers\",\n      \"Improve section formatting consistency\"\n    ]\n  },\n  \"keywordAnalysis\": {\n    \"found\": [\"JavaScript\", \"React\", \"Node.js\"],\n    \"missing\": [\"AWS\", \"Docker\", \"Kubernetes\"],\n    \"suggestions\": [\"Add cloud technologies\", \"Include DevOps tools\"]\n  }\n}\n\nProvide detailed, actionable feedback for ATS optimization.\n`;\n}\nfunction createFullAnalysisPrompt(resumeText, jobDescription, jobTitle, company) {\n    const jobContext = jobDescription ? `\n\nTARGET JOB INFORMATION:\nJob Title: ${jobTitle || 'Not specified'}\nCompany: ${company || 'Not specified'}\nJob Description: ${jobDescription}\n\nIMPORTANT: Please optimize the resume specifically for this job by:\n1. Highlighting relevant experience and skills that match the job requirements\n2. Using keywords from the job description naturally throughout the resume\n3. Emphasizing achievements that align with the job responsibilities\n4. Adjusting the professional summary to target this specific role\n5. Prioritizing skills and experiences most relevant to this position` : '';\n    return `\nYou are an expert resume writer and ATS optimization specialist. Analyze the following resume text and extract structured information while providing enhancement suggestions.\n\nRESUME TEXT:\n${resumeText}${jobContext}\n\nPlease extract and enhance the resume information, then respond in the following JSON format:\n{\n  \"extractedData\": {\n    \"personal\": {\n      \"firstName\": \"John\",\n      \"lastName\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"location\": \"City, State\",\n      \"linkedin\": \"linkedin.com/in/johndoe\",\n      \"portfolio\": \"johndoe.com\",\n      \"summary\": \"Professional summary extracted from resume\"\n    },\n    \"experience\": [\n      {\n        \"title\": \"Software Engineer\",\n        \"company\": \"Tech Company\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2020-01\",\n        \"endDate\": \"2023-12\",\n        \"current\": false,\n        \"description\": \"Enhanced description with quantified achievements\"\n      }\n    ],\n    \"education\": [\n      {\n        \"degree\": \"Bachelor of Science in Computer Science\",\n        \"institution\": \"University Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2016-09\",\n        \"endDate\": \"2020-05\",\n        \"gpa\": \"3.8\",\n        \"relevant\": \"Relevant coursework\"\n      }\n    ],\n    \"skills\": {\n      \"technical\": [\"JavaScript\", \"React\", \"Node.js\"],\n      \"languages\": [\"English\", \"Spanish\"],\n      \"certifications\": [\"AWS Certified\", \"Google Cloud\"]\n    },\n    \"projects\": [\n      {\n        \"name\": \"Project Name\",\n        \"description\": \"Enhanced project description\",\n        \"technologies\": \"React, Node.js, MongoDB\",\n        \"link\": \"github.com/project\"\n      }\n    ]\n  },\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"enhancements\": {\n    \"summary\": \"AI-enhanced professional summary\",\n    \"experience\": [\n      {\n        \"original\": \"Original job description\",\n        \"enhanced\": \"Enhanced description with action verbs and quantified results\"\n      }\n    ],\n    \"suggestions\": [\n      \"Add more quantified achievements\",\n      \"Include industry keywords\",\n      \"Improve formatting consistency\"\n    ]\n  }\n}\n\nExtract all available information and provide enhanced, ATS-optimized versions.\n`;\n}\nfunction parseAIResponse(generatedContent, analysisType) {\n    try {\n        // Try to extract JSON from the response\n        const jsonMatch = generatedContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            const parsedJson = JSON.parse(jsonMatch[0]);\n            return parsedJson;\n        }\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n    }\n    // Fallback response\n    if (analysisType === 'quick') {\n        return {\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            analysis: {\n                strengths: [\n                    'Professional experience listed',\n                    'Education section present'\n                ],\n                weaknesses: [\n                    'Limited quantified achievements',\n                    'Missing keywords'\n                ],\n                recommendations: [\n                    'Add more metrics and numbers',\n                    'Include industry-specific terms'\n                ]\n            }\n        };\n    } else {\n        return {\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    summary: ''\n                },\n                experience: [],\n                education: [],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: []\n            },\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            enhancements: {\n                suggestions: [\n                    'Unable to parse AI response',\n                    'Please try again'\n                ]\n            }\n        };\n    }\n}\nfunction createFallbackAnalysis(extractedText, analysisType) {\n    const basicScore = {\n        overall: 75,\n        breakdown: {\n            keywords: 70,\n            formatting: 80,\n            structure: 75,\n            achievements: 70,\n            skills: 75\n        }\n    };\n    if (analysisType === 'quick') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'quick',\n            atsScore: basicScore,\n            analysis: {\n                strengths: [\n                    'Resume file uploaded successfully',\n                    'Ready for manual review'\n                ],\n                weaknesses: [\n                    'Text extraction limited',\n                    'Manual optimization needed'\n                ],\n                recommendations: [\n                    'Review and fill out the form manually',\n                    'Add quantified achievements',\n                    'Include relevant keywords for your industry',\n                    'Ensure consistent formatting'\n                ]\n            },\n            keywordAnalysis: {\n                found: [],\n                missing: [\n                    'Industry-specific keywords',\n                    'Technical skills',\n                    'Action verbs'\n                ],\n                suggestions: [\n                    'Add relevant technical skills',\n                    'Include measurable achievements',\n                    'Use industry terminology'\n                ]\n            },\n            fallback: true\n        });\n    } else {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'full',\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    linkedin: '',\n                    portfolio: '',\n                    summary: extractedText.includes('uploaded successfully') ? '' : extractedText.substring(0, 200)\n                },\n                experience: [\n                    {\n                        id: 1,\n                        title: '',\n                        company: '',\n                        location: '',\n                        startDate: '',\n                        endDate: '',\n                        current: false,\n                        description: ''\n                    }\n                ],\n                education: [\n                    {\n                        id: 1,\n                        degree: '',\n                        institution: '',\n                        location: '',\n                        startDate: '',\n                        endDate: '',\n                        gpa: '',\n                        relevant: ''\n                    }\n                ],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: [\n                    {\n                        id: 1,\n                        name: '',\n                        description: '',\n                        technologies: '',\n                        link: ''\n                    }\n                ]\n            },\n            atsScore: basicScore,\n            enhancements: {\n                suggestions: [\n                    'File uploaded successfully - please review and edit the form',\n                    'Add your personal information',\n                    'Fill in your work experience with quantified achievements',\n                    'Include relevant technical skills',\n                    'Add education details',\n                    'Consider adding projects or certifications'\n                ]\n            },\n            fallback: true\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/upload-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/pdf-parse","vendor-chunks/next","vendor-chunks/underscore","vendor-chunks/mammoth","vendor-chunks/bluebird","vendor-chunks/jszip","vendor-chunks/xmlbuilder","vendor-chunks/pako","vendor-chunks/readable-stream","vendor-chunks/lop","vendor-chunks/@xmldom","vendor-chunks/string_decoder","vendor-chunks/inherits","vendor-chunks/dingbat-to-unicode","vendor-chunks/@google","vendor-chunks/util-deprecate","vendor-chunks/process-nextick-args","vendor-chunks/path-is-absolute","vendor-chunks/option","vendor-chunks/lie","vendor-chunks/immediate","vendor-chunks/core-util-is","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();