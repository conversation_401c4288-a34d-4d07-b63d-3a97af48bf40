/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/upload-resume/route";
exports.ids = ["app/api/upload-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/upload-resume/route.js */ \"(rsc)/./src/app/api/upload-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/upload-resume/route\",\n        pathname: \"/api/upload-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/upload-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\upload-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$":
/*!**************************************************************************!*\
  !*** ./node_modules/pdf-parse/lib/pdf.js/ sync ^\.\/.*\/build\/pdf\.js$ ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./v1.10.100/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js",
	"./v1.10.88/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.88/build/pdf.js",
	"./v1.9.426/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.9.426/build/pdf.js",
	"./v2.0.550/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v2.0.550/build/pdf.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$";

/***/ }),

/***/ "(rsc)/./src/app/api/upload-resume/route.js":
/*!********************************************!*\
  !*** ./src/app/api/upload-resume/route.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        console.log('📄 Resume upload API called');\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const analysisType = formData.get('analysisType') || 'full'; // 'full' or 'quick'\n        const jobDescription = formData.get('jobDescription');\n        const jobTitle = formData.get('jobTitle');\n        const company = formData.get('company');\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No file uploaded'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📁 File details:', {\n            name: file.name,\n            size: file.size,\n            type: file.type\n        });\n        // Validate file type and size\n        const allowedTypes = [\n            'application/pdf',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid file type. Please upload PDF, DOC, or DOCX files only.'\n            }, {\n                status: 400\n            });\n        }\n        // 10MB file size limit\n        if (file.size > 10 * 1024 * 1024) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'File size too large. Please upload files smaller than 10MB.'\n            }, {\n                status: 400\n            });\n        }\n        // Extract text from the uploaded file\n        const buffer = Buffer.from(await file.arrayBuffer());\n        let extractedText = '';\n        try {\n            if (file.type === 'application/pdf') {\n                console.log('📄 Processing PDF file...');\n                // Try to extract text from PDF\n                try {\n                    const pdfParse = __webpack_require__(/*! pdf-parse */ \"(rsc)/./node_modules/pdf-parse/index.js\");\n                    const pdfData = await pdfParse(buffer);\n                    extractedText = pdfData.text;\n                } catch (pdfError) {\n                    console.log('⚠️ PDF parsing failed, using fallback text extraction');\n                    // Fallback: extract basic text information\n                    extractedText = `PDF file uploaded: ${file.name}. Please provide resume details manually.`;\n                }\n            } else if (file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\n                console.log('📄 Processing Word document...');\n                try {\n                    const mammoth = __webpack_require__(/*! mammoth */ \"(rsc)/./node_modules/mammoth/lib/index.js\");\n                    const result = await mammoth.extractRawText({\n                        buffer\n                    });\n                    extractedText = result.value;\n                } catch (docError) {\n                    console.log('⚠️ Word document parsing failed, using fallback text extraction');\n                    // Fallback: extract basic text information\n                    extractedText = `Word document uploaded: ${file.name}. Please provide resume details manually.`;\n                }\n            }\n        } catch (extractionError) {\n            console.error('Text extraction error:', extractionError);\n            // Use fallback text instead of failing\n            extractedText = `Resume file uploaded: ${file.name}. File processing encountered an issue, but you can still proceed with manual entry.`;\n        }\n        if (!extractedText || extractedText.trim().length < 20) {\n            console.log('⚠️ Minimal text extracted, using fallback analysis');\n            extractedText = `Resume file: ${file.name}. File uploaded successfully but text extraction was limited. Please review and edit the form manually.`;\n        }\n        console.log('✅ Text extracted, length:', extractedText.length);\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback analysis');\n            return createFallbackAnalysis(extractedText, analysisType);\n        }\n        let analysisResult;\n        try {\n            // Get the generative model - Using Gemini 2.0 Flash (latest and most efficient)\n            const model = genAI.getGenerativeModel({\n                model: 'gemini-2.0-flash-exp',\n                generationConfig: {\n                    temperature: 0.3,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 8000\n                }\n            });\n            let prompt;\n            if (analysisType === 'quick') {\n                prompt = createQuickAnalysisPrompt(extractedText, jobDescription);\n            } else {\n                prompt = createFullAnalysisPrompt(extractedText, jobDescription, jobTitle, company);\n            }\n            console.log('🤖 Calling Gemini AI for analysis...');\n            const result = await model.generateContent(prompt);\n            const response = await result.response;\n            const generatedContent = response.text();\n            console.log('✅ Gemini analysis completed, length:', generatedContent?.length || 0);\n            // Parse the AI response\n            analysisResult = parseAIResponse(generatedContent, analysisType);\n        } catch (aiError) {\n            console.error('AI analysis error:', aiError);\n            console.log('⚠️ AI analysis failed, using fallback analysis');\n            return createFallbackAnalysis(extractedText, analysisType);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType,\n            extractedText: extractedText.substring(0, 1000) + '...',\n            ...analysisResult,\n            fileName: file.name,\n            fileSize: file.size,\n            processedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('💥 Resume upload error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process resume',\n            details:  true ? error.message : 0\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createQuickAnalysisPrompt(resumeText, jobDescription) {\n    const jobContext = jobDescription ? `\n\nJOB DESCRIPTION FOR TARGETING:\n${jobDescription}\n\nPlease analyze the resume against this specific job description for better ATS compatibility.` : '';\n    return `\nYou are an expert ATS (Applicant Tracking System) analyzer. Analyze the following resume text and provide a comprehensive ATS compatibility score and recommendations.\n\nRESUME TEXT:\n${resumeText}${jobContext}\n\nPlease analyze the resume and respond in the following JSON format:\n{\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"analysis\": {\n    \"strengths\": [\n      \"Strong technical skills section\",\n      \"Quantified achievements in experience\"\n    ],\n    \"weaknesses\": [\n      \"Missing industry keywords\",\n      \"Inconsistent formatting\"\n    ],\n    \"recommendations\": [\n      \"Add more industry-specific keywords\",\n      \"Quantify more achievements with numbers\",\n      \"Improve section formatting consistency\"\n    ]\n  },\n  \"keywordAnalysis\": {\n    \"found\": [\"JavaScript\", \"React\", \"Node.js\"],\n    \"missing\": [\"AWS\", \"Docker\", \"Kubernetes\"],\n    \"suggestions\": [\"Add cloud technologies\", \"Include DevOps tools\"]\n  }\n}\n\nProvide detailed, actionable feedback for ATS optimization.\n`;\n}\nfunction createFullAnalysisPrompt(resumeText, jobDescription, jobTitle, company) {\n    const jobContext = jobDescription ? `\n\nTARGET JOB INFORMATION:\nJob Title: ${jobTitle || 'Not specified'}\nCompany: ${company || 'Not specified'}\nJob Description: ${jobDescription}\n\nIMPORTANT: Please optimize the resume specifically for this job by:\n1. Highlighting relevant experience and skills that match the job requirements\n2. Using keywords from the job description naturally throughout the resume\n3. Emphasizing achievements that align with the job responsibilities\n4. Adjusting the professional summary to target this specific role\n5. Prioritizing skills and experiences most relevant to this position` : '';\n    return `\nYou are an expert resume writer and ATS optimization specialist. Analyze the following resume text and extract structured information while providing enhancement suggestions.\n\nRESUME TEXT:\n${resumeText}${jobContext}\n\nPlease extract and enhance the resume information, then respond in the following JSON format:\n{\n  \"extractedData\": {\n    \"personal\": {\n      \"firstName\": \"John\",\n      \"lastName\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"location\": \"City, State\",\n      \"linkedin\": \"linkedin.com/in/johndoe\",\n      \"portfolio\": \"johndoe.com\",\n      \"summary\": \"Professional summary extracted from resume\"\n    },\n    \"experience\": [\n      {\n        \"title\": \"Software Engineer\",\n        \"company\": \"Tech Company\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2020-01\",\n        \"endDate\": \"2023-12\",\n        \"current\": false,\n        \"description\": \"Enhanced description with quantified achievements\"\n      }\n    ],\n    \"education\": [\n      {\n        \"degree\": \"Bachelor of Science in Computer Science\",\n        \"institution\": \"University Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2016-09\",\n        \"endDate\": \"2020-05\",\n        \"gpa\": \"3.8\",\n        \"relevant\": \"Relevant coursework\"\n      }\n    ],\n    \"skills\": {\n      \"technical\": [\"JavaScript\", \"React\", \"Node.js\"],\n      \"languages\": [\"English\", \"Spanish\"],\n      \"certifications\": [\"AWS Certified\", \"Google Cloud\"]\n    },\n    \"projects\": [\n      {\n        \"name\": \"Project Name\",\n        \"description\": \"Enhanced project description\",\n        \"technologies\": \"React, Node.js, MongoDB\",\n        \"link\": \"github.com/project\"\n      }\n    ]\n  },\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"enhancements\": {\n    \"summary\": \"AI-enhanced professional summary\",\n    \"experience\": [\n      {\n        \"original\": \"Original job description\",\n        \"enhanced\": \"Enhanced description with action verbs and quantified results\"\n      }\n    ],\n    \"suggestions\": [\n      \"Add more quantified achievements\",\n      \"Include industry keywords\",\n      \"Improve formatting consistency\"\n    ]\n  }\n}\n\nExtract all available information and provide enhanced, ATS-optimized versions.\n`;\n}\nfunction parseAIResponse(generatedContent, analysisType) {\n    try {\n        // Try to extract JSON from the response\n        const jsonMatch = generatedContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            const parsedJson = JSON.parse(jsonMatch[0]);\n            return parsedJson;\n        }\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n    }\n    // Fallback response\n    if (analysisType === 'quick') {\n        return {\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            analysis: {\n                strengths: [\n                    'Professional experience listed',\n                    'Education section present'\n                ],\n                weaknesses: [\n                    'Limited quantified achievements',\n                    'Missing keywords'\n                ],\n                recommendations: [\n                    'Add more metrics and numbers',\n                    'Include industry-specific terms'\n                ]\n            }\n        };\n    } else {\n        return {\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    summary: ''\n                },\n                experience: [],\n                education: [],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: []\n            },\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            enhancements: {\n                suggestions: [\n                    'Unable to parse AI response',\n                    'Please try again'\n                ]\n            }\n        };\n    }\n}\nfunction createFallbackAnalysis(extractedText, analysisType) {\n    const basicScore = {\n        overall: 75,\n        breakdown: {\n            keywords: 70,\n            formatting: 80,\n            structure: 75,\n            achievements: 70,\n            skills: 75\n        }\n    };\n    if (analysisType === 'quick') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'quick',\n            atsScore: basicScore,\n            analysis: {\n                strengths: [\n                    'Resume file uploaded successfully',\n                    'Ready for manual review'\n                ],\n                weaknesses: [\n                    'Text extraction limited',\n                    'Manual optimization needed'\n                ],\n                recommendations: [\n                    'Review and fill out the form manually',\n                    'Add quantified achievements',\n                    'Include relevant keywords for your industry',\n                    'Ensure consistent formatting'\n                ]\n            },\n            keywordAnalysis: {\n                found: [],\n                missing: [\n                    'Industry-specific keywords',\n                    'Technical skills',\n                    'Action verbs'\n                ],\n                suggestions: [\n                    'Add relevant technical skills',\n                    'Include measurable achievements',\n                    'Use industry terminology'\n                ]\n            },\n            fallback: true\n        });\n    } else {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'full',\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    linkedin: '',\n                    portfolio: '',\n                    summary: extractedText.includes('uploaded successfully') ? '' : extractedText.substring(0, 200)\n                },\n                experience: [\n                    {\n                        id: 1,\n                        title: '',\n                        company: '',\n                        location: '',\n                        startDate: '',\n                        endDate: '',\n                        current: false,\n                        description: ''\n                    }\n                ],\n                education: [\n                    {\n                        id: 1,\n                        degree: '',\n                        institution: '',\n                        location: '',\n                        startDate: '',\n                        endDate: '',\n                        gpa: '',\n                        relevant: ''\n                    }\n                ],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: [\n                    {\n                        id: 1,\n                        name: '',\n                        description: '',\n                        technologies: '',\n                        link: ''\n                    }\n                ]\n            },\n            atsScore: basicScore,\n            enhancements: {\n                suggestions: [\n                    'File uploaded successfully - please review and edit the form',\n                    'Add your personal information',\n                    'Fill in your work experience with quantified achievements',\n                    'Include relevant technical skills',\n                    'Add education details',\n                    'Consider adding projects or certifications'\n                ]\n            },\n            fallback: true\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/upload-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/pdf-parse","vendor-chunks/next","vendor-chunks/@google","vendor-chunks/underscore","vendor-chunks/mammoth","vendor-chunks/bluebird","vendor-chunks/jszip","vendor-chunks/xmlbuilder","vendor-chunks/pako","vendor-chunks/readable-stream","vendor-chunks/lop","vendor-chunks/@xmldom","vendor-chunks/string_decoder","vendor-chunks/inherits","vendor-chunks/dingbat-to-unicode","vendor-chunks/util-deprecate","vendor-chunks/process-nextick-args","vendor-chunks/path-is-absolute","vendor-chunks/option","vendor-chunks/lie","vendor-chunks/immediate","vendor-chunks/core-util-is","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();