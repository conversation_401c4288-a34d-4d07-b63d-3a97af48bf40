/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/upload-resume/route";
exports.ids = ["app/api/upload-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/upload-resume/route.js */ \"(rsc)/./src/app/api/upload-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/upload-resume/route\",\n        pathname: \"/api/upload-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/upload-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\upload-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$":
/*!**************************************************************************!*\
  !*** ./node_modules/pdf-parse/lib/pdf.js/ sync ^\.\/.*\/build\/pdf\.js$ ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./v1.10.100/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js",
	"./v1.10.88/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.88/build/pdf.js",
	"./v1.9.426/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.9.426/build/pdf.js",
	"./v2.0.550/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v2.0.550/build/pdf.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$";

/***/ }),

/***/ "(rsc)/./src/app/api/upload-resume/route.js":
/*!********************************************!*\
  !*** ./src/app/api/upload-resume/route.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        console.log('📄 Resume upload API called');\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const analysisType = formData.get('analysisType') || 'full'; // 'full' or 'quick'\n        const jobDescription = formData.get('jobDescription');\n        const jobTitle = formData.get('jobTitle');\n        const company = formData.get('company');\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No file uploaded'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📁 File details:', {\n            name: file.name,\n            size: file.size,\n            type: file.type\n        });\n        // Validate file type and size\n        const allowedTypes = [\n            'application/pdf',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid file type. Please upload PDF, DOC, or DOCX files only.'\n            }, {\n                status: 400\n            });\n        }\n        // 10MB file size limit\n        if (file.size > 10 * 1024 * 1024) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'File size too large. Please upload files smaller than 10MB.'\n            }, {\n                status: 400\n            });\n        }\n        // Extract text from the uploaded file\n        const buffer = Buffer.from(await file.arrayBuffer());\n        let extractedText = '';\n        try {\n            if (file.type === 'application/pdf') {\n                console.log('📄 Processing PDF file...');\n                // Try to extract text from PDF\n                try {\n                    const pdfParse = __webpack_require__(/*! pdf-parse */ \"(rsc)/./node_modules/pdf-parse/index.js\");\n                    const pdfData = await pdfParse(buffer);\n                    extractedText = pdfData.text;\n                } catch (pdfError) {\n                    console.log('⚠️ PDF parsing failed, using fallback text extraction');\n                    // Fallback: extract basic text information\n                    extractedText = `PDF file uploaded: ${file.name}. Please provide resume details manually.`;\n                }\n            } else if (file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\n                console.log('📄 Processing Word document...');\n                try {\n                    const mammoth = __webpack_require__(/*! mammoth */ \"(rsc)/./node_modules/mammoth/lib/index.js\");\n                    const result = await mammoth.extractRawText({\n                        buffer\n                    });\n                    extractedText = result.value;\n                } catch (docError) {\n                    console.log('⚠️ Word document parsing failed, using fallback text extraction');\n                    // Fallback: extract basic text information\n                    extractedText = `Word document uploaded: ${file.name}. Please provide resume details manually.`;\n                }\n            }\n        } catch (extractionError) {\n            console.error('Text extraction error:', extractionError);\n            // Use fallback text instead of failing\n            extractedText = `Resume file uploaded: ${file.name}. File processing encountered an issue, but you can still proceed with manual entry.`;\n        }\n        if (!extractedText || extractedText.trim().length < 20) {\n            console.log('⚠️ Minimal text extracted, using fallback analysis');\n            extractedText = `Resume file: ${file.name}. File uploaded successfully but text extraction was limited. Please review and edit the form manually.`;\n        }\n        console.log('✅ Text extracted, length:', extractedText.length);\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback analysis');\n            return createFallbackAnalysis(extractedText, analysisType);\n        }\n        let analysisResult;\n        try {\n            // Get the generative model\n            const model = genAI.getGenerativeModel({\n                model: 'gemini-1.5-flash-latest',\n                generationConfig: {\n                    temperature: 0.7,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 3000\n                }\n            });\n            let prompt;\n            if (analysisType === 'quick') {\n                prompt = createQuickAnalysisPrompt(extractedText, jobDescription);\n            } else {\n                prompt = createFullAnalysisPrompt(extractedText, jobDescription, jobTitle, company);\n            }\n            console.log('🤖 Calling Gemini AI for analysis...');\n            const result = await model.generateContent(prompt);\n            const response = await result.response;\n            const generatedContent = response.text();\n            console.log('✅ Gemini analysis completed, length:', generatedContent?.length || 0);\n            // Parse the AI response\n            analysisResult = parseAIResponse(generatedContent, analysisType);\n        } catch (aiError) {\n            console.error('AI analysis error:', aiError);\n            console.log('⚠️ AI analysis failed, using fallback analysis');\n            return createFallbackAnalysis(extractedText, analysisType);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType,\n            extractedText: extractedText.substring(0, 1000) + '...',\n            ...analysisResult,\n            fileName: file.name,\n            fileSize: file.size,\n            processedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('💥 Resume upload error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process resume',\n            details:  true ? error.message : 0\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createQuickAnalysisPrompt(resumeText, jobDescription) {\n    const jobContext = jobDescription ? `\n\nJOB DESCRIPTION FOR TARGETING:\n${jobDescription}\n\nPlease analyze the resume against this specific job description for better ATS compatibility.` : '';\n    return `\nYou are an expert ATS (Applicant Tracking System) analyzer. Analyze the following resume text and provide a comprehensive ATS compatibility score and recommendations.\n\nRESUME TEXT:\n${resumeText}${jobContext}\n\nPlease analyze the resume and respond in the following JSON format:\n{\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"analysis\": {\n    \"strengths\": [\n      \"Strong technical skills section\",\n      \"Quantified achievements in experience\"\n    ],\n    \"weaknesses\": [\n      \"Missing industry keywords\",\n      \"Inconsistent formatting\"\n    ],\n    \"recommendations\": [\n      \"Add more industry-specific keywords\",\n      \"Quantify more achievements with numbers\",\n      \"Improve section formatting consistency\"\n    ]\n  },\n  \"keywordAnalysis\": {\n    \"found\": [\"JavaScript\", \"React\", \"Node.js\"],\n    \"missing\": [\"AWS\", \"Docker\", \"Kubernetes\"],\n    \"suggestions\": [\"Add cloud technologies\", \"Include DevOps tools\"]\n  }\n}\n\nProvide detailed, actionable feedback for ATS optimization.\n`;\n}\nfunction createFullAnalysisPrompt(resumeText, jobDescription, jobTitle, company) {\n    const jobContext = jobDescription ? `\n\nTARGET JOB INFORMATION:\nJob Title: ${jobTitle || 'Not specified'}\nCompany: ${company || 'Not specified'}\nJob Description: ${jobDescription}\n\nIMPORTANT: Please optimize the resume specifically for this job by:\n1. Highlighting relevant experience and skills that match the job requirements\n2. Using keywords from the job description naturally throughout the resume\n3. Emphasizing achievements that align with the job responsibilities\n4. Adjusting the professional summary to target this specific role\n5. Prioritizing skills and experiences most relevant to this position` : '';\n    return `\nYou are an expert resume writer and ATS optimization specialist. Analyze the following resume text and extract structured information while providing enhancement suggestions.\n\nRESUME TEXT:\n${resumeText}${jobContext}\n\nPlease extract and enhance the resume information, then respond in the following JSON format:\n{\n  \"extractedData\": {\n    \"personal\": {\n      \"firstName\": \"John\",\n      \"lastName\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"location\": \"City, State\",\n      \"linkedin\": \"linkedin.com/in/johndoe\",\n      \"portfolio\": \"johndoe.com\",\n      \"summary\": \"Professional summary extracted from resume\"\n    },\n    \"experience\": [\n      {\n        \"title\": \"Software Engineer\",\n        \"company\": \"Tech Company\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2020-01\",\n        \"endDate\": \"2023-12\",\n        \"current\": false,\n        \"description\": \"Enhanced description with quantified achievements\"\n      }\n    ],\n    \"education\": [\n      {\n        \"degree\": \"Bachelor of Science in Computer Science\",\n        \"institution\": \"University Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2016-09\",\n        \"endDate\": \"2020-05\",\n        \"gpa\": \"3.8\",\n        \"relevant\": \"Relevant coursework\"\n      }\n    ],\n    \"skills\": {\n      \"technical\": [\"JavaScript\", \"React\", \"Node.js\"],\n      \"languages\": [\"English\", \"Spanish\"],\n      \"certifications\": [\"AWS Certified\", \"Google Cloud\"]\n    },\n    \"projects\": [\n      {\n        \"name\": \"Project Name\",\n        \"description\": \"Enhanced project description\",\n        \"technologies\": \"React, Node.js, MongoDB\",\n        \"link\": \"github.com/project\"\n      }\n    ]\n  },\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"enhancements\": {\n    \"summary\": \"AI-enhanced professional summary\",\n    \"experience\": [\n      {\n        \"original\": \"Original job description\",\n        \"enhanced\": \"Enhanced description with action verbs and quantified results\"\n      }\n    ],\n    \"suggestions\": [\n      \"Add more quantified achievements\",\n      \"Include industry keywords\",\n      \"Improve formatting consistency\"\n    ]\n  }\n}\n\nExtract all available information and provide enhanced, ATS-optimized versions.\n`;\n}\nfunction parseAIResponse(generatedContent, analysisType) {\n    try {\n        // Try to extract JSON from the response\n        const jsonMatch = generatedContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            const parsedJson = JSON.parse(jsonMatch[0]);\n            return parsedJson;\n        }\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n    }\n    // Fallback response\n    if (analysisType === 'quick') {\n        return {\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            analysis: {\n                strengths: [\n                    'Professional experience listed',\n                    'Education section present'\n                ],\n                weaknesses: [\n                    'Limited quantified achievements',\n                    'Missing keywords'\n                ],\n                recommendations: [\n                    'Add more metrics and numbers',\n                    'Include industry-specific terms'\n                ]\n            }\n        };\n    } else {\n        return {\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    summary: ''\n                },\n                experience: [],\n                education: [],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: []\n            },\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            enhancements: {\n                suggestions: [\n                    'Unable to parse AI response',\n                    'Please try again'\n                ]\n            }\n        };\n    }\n}\nfunction createFallbackAnalysis(extractedText, analysisType) {\n    const basicScore = {\n        overall: 75,\n        breakdown: {\n            keywords: 70,\n            formatting: 80,\n            structure: 75,\n            achievements: 70,\n            skills: 75\n        }\n    };\n    if (analysisType === 'quick') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'quick',\n            atsScore: basicScore,\n            analysis: {\n                strengths: [\n                    'Resume file uploaded successfully',\n                    'Ready for manual review'\n                ],\n                weaknesses: [\n                    'Text extraction limited',\n                    'Manual optimization needed'\n                ],\n                recommendations: [\n                    'Review and fill out the form manually',\n                    'Add quantified achievements',\n                    'Include relevant keywords for your industry',\n                    'Ensure consistent formatting'\n                ]\n            },\n            keywordAnalysis: {\n                found: [],\n                missing: [\n                    'Industry-specific keywords',\n                    'Technical skills',\n                    'Action verbs'\n                ],\n                suggestions: [\n                    'Add relevant technical skills',\n                    'Include measurable achievements',\n                    'Use industry terminology'\n                ]\n            },\n            fallback: true\n        });\n    } else {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'full',\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    linkedin: '',\n                    portfolio: '',\n                    summary: extractedText.includes('uploaded successfully') ? '' : extractedText.substring(0, 200)\n                },\n                experience: [\n                    {\n                        id: 1,\n                        title: '',\n                        company: '',\n                        location: '',\n                        startDate: '',\n                        endDate: '',\n                        current: false,\n                        description: ''\n                    }\n                ],\n                education: [\n                    {\n                        id: 1,\n                        degree: '',\n                        institution: '',\n                        location: '',\n                        startDate: '',\n                        endDate: '',\n                        gpa: '',\n                        relevant: ''\n                    }\n                ],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: [\n                    {\n                        id: 1,\n                        name: '',\n                        description: '',\n                        technologies: '',\n                        link: ''\n                    }\n                ]\n            },\n            atsScore: basicScore,\n            enhancements: {\n                suggestions: [\n                    'File uploaded successfully - please review and edit the form',\n                    'Add your personal information',\n                    'Fill in your work experience with quantified achievements',\n                    'Include relevant technical skills',\n                    'Add education details',\n                    'Consider adding projects or certifications'\n                ]\n            },\n            fallback: true\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/upload-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/pdf-parse","vendor-chunks/next","vendor-chunks/underscore","vendor-chunks/mammoth","vendor-chunks/bluebird","vendor-chunks/jszip","vendor-chunks/xmlbuilder","vendor-chunks/pako","vendor-chunks/readable-stream","vendor-chunks/lop","vendor-chunks/@xmldom","vendor-chunks/string_decoder","vendor-chunks/inherits","vendor-chunks/dingbat-to-unicode","vendor-chunks/@google","vendor-chunks/util-deprecate","vendor-chunks/process-nextick-args","vendor-chunks/path-is-absolute","vendor-chunks/option","vendor-chunks/lie","vendor-chunks/immediate","vendor-chunks/core-util-is","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();