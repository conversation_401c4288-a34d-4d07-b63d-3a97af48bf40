/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/upload-resume/route";
exports.ids = ["app/api/upload-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/upload-resume/route.js */ \"(rsc)/./src/app/api/upload-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/upload-resume/route\",\n        pathname: \"/api/upload-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/upload-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\upload-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_upload_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/upload-resume/route.js":
/*!********************************************!*\
  !*** ./src/app/api/upload-resume/route.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Dynamic imports to avoid build issues\nlet pdfParse, mammoth;\nasync function initializeParsers() {\n    if (!pdfParse) {\n        pdfParse = (await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/pdf-parse\"), __webpack_require__.e(\"_rsc_node_modules_pdf-parse_lib_pdf_js_sync_recursive_build_pdf_js_\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! pdf-parse */ \"(rsc)/./node_modules/pdf-parse/index.js\", 23))).default;\n    }\n    if (!mammoth) {\n        mammoth = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/underscore\"), __webpack_require__.e(\"vendor-chunks/mammoth\"), __webpack_require__.e(\"vendor-chunks/bluebird\"), __webpack_require__.e(\"vendor-chunks/jszip\"), __webpack_require__.e(\"vendor-chunks/xmlbuilder\"), __webpack_require__.e(\"vendor-chunks/pako\"), __webpack_require__.e(\"vendor-chunks/readable-stream\"), __webpack_require__.e(\"vendor-chunks/lop\"), __webpack_require__.e(\"vendor-chunks/@xmldom\"), __webpack_require__.e(\"vendor-chunks/string_decoder\"), __webpack_require__.e(\"vendor-chunks/inherits\"), __webpack_require__.e(\"vendor-chunks/dingbat-to-unicode\"), __webpack_require__.e(\"vendor-chunks/util-deprecate\"), __webpack_require__.e(\"vendor-chunks/process-nextick-args\"), __webpack_require__.e(\"vendor-chunks/path-is-absolute\"), __webpack_require__.e(\"vendor-chunks/option\"), __webpack_require__.e(\"vendor-chunks/lie\"), __webpack_require__.e(\"vendor-chunks/immediate\"), __webpack_require__.e(\"vendor-chunks/core-util-is\"), __webpack_require__.e(\"vendor-chunks/base64-js\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! mammoth */ \"(rsc)/./node_modules/mammoth/lib/index.js\", 19));\n    }\n}\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        console.log('📄 Resume upload API called');\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const analysisType = formData.get('analysisType') || 'full'; // 'full' or 'quick'\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No file uploaded'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📁 File details:', {\n            name: file.name,\n            size: file.size,\n            type: file.type\n        });\n        // Validate file type and size\n        const allowedTypes = [\n            'application/pdf',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid file type. Please upload PDF, DOC, or DOCX files only.'\n            }, {\n                status: 400\n            });\n        }\n        // 10MB file size limit\n        if (file.size > 10 * 1024 * 1024) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'File size too large. Please upload files smaller than 10MB.'\n            }, {\n                status: 400\n            });\n        }\n        // Initialize parsers\n        await initializeParsers();\n        // Extract text from the uploaded file\n        const buffer = Buffer.from(await file.arrayBuffer());\n        let extractedText = '';\n        try {\n            if (file.type === 'application/pdf') {\n                console.log('📄 Processing PDF file...');\n                const pdfData = await pdfParse(buffer);\n                extractedText = pdfData.text;\n            } else if (file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\n                console.log('📄 Processing Word document...');\n                const result = await mammoth.extractRawText({\n                    buffer\n                });\n                extractedText = result.value;\n            }\n        } catch (extractionError) {\n            console.error('Text extraction error:', extractionError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to extract text from the uploaded file. Please ensure the file is not corrupted.'\n            }, {\n                status: 400\n            });\n        }\n        if (!extractedText || extractedText.trim().length < 50) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unable to extract sufficient text from the file. Please ensure the file contains readable text.'\n            }, {\n                status: 400\n            });\n        }\n        console.log('✅ Text extracted, length:', extractedText.length);\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback analysis');\n            return createFallbackAnalysis(extractedText, analysisType);\n        }\n        // Get the generative model\n        const model = genAI.getGenerativeModel({\n            model: 'gemini-1.5-flash-latest',\n            generationConfig: {\n                temperature: 0.7,\n                topK: 40,\n                topP: 0.95,\n                maxOutputTokens: 3000\n            }\n        });\n        let prompt;\n        if (analysisType === 'quick') {\n            prompt = createQuickAnalysisPrompt(extractedText);\n        } else {\n            prompt = createFullAnalysisPrompt(extractedText);\n        }\n        console.log('🤖 Calling Gemini AI for analysis...');\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const generatedContent = response.text();\n        console.log('✅ Gemini analysis completed, length:', generatedContent?.length || 0);\n        // Parse the AI response\n        const analysisResult = parseAIResponse(generatedContent, analysisType);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType,\n            extractedText: extractedText.substring(0, 1000) + '...',\n            ...analysisResult,\n            fileName: file.name,\n            fileSize: file.size,\n            processedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('💥 Resume upload error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process resume',\n            details:  true ? error.message : 0\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createQuickAnalysisPrompt(resumeText) {\n    return `\nYou are an expert ATS (Applicant Tracking System) analyzer. Analyze the following resume text and provide a comprehensive ATS compatibility score and recommendations.\n\nRESUME TEXT:\n${resumeText}\n\nPlease analyze the resume and respond in the following JSON format:\n{\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"analysis\": {\n    \"strengths\": [\n      \"Strong technical skills section\",\n      \"Quantified achievements in experience\"\n    ],\n    \"weaknesses\": [\n      \"Missing industry keywords\",\n      \"Inconsistent formatting\"\n    ],\n    \"recommendations\": [\n      \"Add more industry-specific keywords\",\n      \"Quantify more achievements with numbers\",\n      \"Improve section formatting consistency\"\n    ]\n  },\n  \"keywordAnalysis\": {\n    \"found\": [\"JavaScript\", \"React\", \"Node.js\"],\n    \"missing\": [\"AWS\", \"Docker\", \"Kubernetes\"],\n    \"suggestions\": [\"Add cloud technologies\", \"Include DevOps tools\"]\n  }\n}\n\nProvide detailed, actionable feedback for ATS optimization.\n`;\n}\nfunction createFullAnalysisPrompt(resumeText) {\n    return `\nYou are an expert resume writer and ATS optimization specialist. Analyze the following resume text and extract structured information while providing enhancement suggestions.\n\nRESUME TEXT:\n${resumeText}\n\nPlease extract and enhance the resume information, then respond in the following JSON format:\n{\n  \"extractedData\": {\n    \"personal\": {\n      \"firstName\": \"John\",\n      \"lastName\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"location\": \"City, State\",\n      \"linkedin\": \"linkedin.com/in/johndoe\",\n      \"portfolio\": \"johndoe.com\",\n      \"summary\": \"Professional summary extracted from resume\"\n    },\n    \"experience\": [\n      {\n        \"title\": \"Software Engineer\",\n        \"company\": \"Tech Company\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2020-01\",\n        \"endDate\": \"2023-12\",\n        \"current\": false,\n        \"description\": \"Enhanced description with quantified achievements\"\n      }\n    ],\n    \"education\": [\n      {\n        \"degree\": \"Bachelor of Science in Computer Science\",\n        \"institution\": \"University Name\",\n        \"location\": \"City, State\",\n        \"startDate\": \"2016-09\",\n        \"endDate\": \"2020-05\",\n        \"gpa\": \"3.8\",\n        \"relevant\": \"Relevant coursework\"\n      }\n    ],\n    \"skills\": {\n      \"technical\": [\"JavaScript\", \"React\", \"Node.js\"],\n      \"languages\": [\"English\", \"Spanish\"],\n      \"certifications\": [\"AWS Certified\", \"Google Cloud\"]\n    },\n    \"projects\": [\n      {\n        \"name\": \"Project Name\",\n        \"description\": \"Enhanced project description\",\n        \"technologies\": \"React, Node.js, MongoDB\",\n        \"link\": \"github.com/project\"\n      }\n    ]\n  },\n  \"atsScore\": {\n    \"overall\": 85,\n    \"breakdown\": {\n      \"keywords\": 80,\n      \"formatting\": 90,\n      \"structure\": 85,\n      \"achievements\": 75,\n      \"skills\": 95\n    }\n  },\n  \"enhancements\": {\n    \"summary\": \"AI-enhanced professional summary\",\n    \"experience\": [\n      {\n        \"original\": \"Original job description\",\n        \"enhanced\": \"Enhanced description with action verbs and quantified results\"\n      }\n    ],\n    \"suggestions\": [\n      \"Add more quantified achievements\",\n      \"Include industry keywords\",\n      \"Improve formatting consistency\"\n    ]\n  }\n}\n\nExtract all available information and provide enhanced, ATS-optimized versions.\n`;\n}\nfunction parseAIResponse(generatedContent, analysisType) {\n    try {\n        // Try to extract JSON from the response\n        const jsonMatch = generatedContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            const parsedJson = JSON.parse(jsonMatch[0]);\n            return parsedJson;\n        }\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n    }\n    // Fallback response\n    if (analysisType === 'quick') {\n        return {\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            analysis: {\n                strengths: [\n                    'Professional experience listed',\n                    'Education section present'\n                ],\n                weaknesses: [\n                    'Limited quantified achievements',\n                    'Missing keywords'\n                ],\n                recommendations: [\n                    'Add more metrics and numbers',\n                    'Include industry-specific terms'\n                ]\n            }\n        };\n    } else {\n        return {\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    summary: ''\n                },\n                experience: [],\n                education: [],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: []\n            },\n            atsScore: {\n                overall: 75,\n                breakdown: {\n                    keywords: 70,\n                    formatting: 80,\n                    structure: 75,\n                    achievements: 70,\n                    skills: 80\n                }\n            },\n            enhancements: {\n                suggestions: [\n                    'Unable to parse AI response',\n                    'Please try again'\n                ]\n            }\n        };\n    }\n}\nfunction createFallbackAnalysis(extractedText, analysisType) {\n    const basicScore = {\n        overall: 70,\n        breakdown: {\n            keywords: 65,\n            formatting: 75,\n            structure: 70,\n            achievements: 65,\n            skills: 75\n        }\n    };\n    if (analysisType === 'quick') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'quick',\n            atsScore: basicScore,\n            analysis: {\n                strengths: [\n                    'Resume content detected',\n                    'Basic structure present'\n                ],\n                weaknesses: [\n                    'AI analysis unavailable',\n                    'Limited optimization'\n                ],\n                recommendations: [\n                    'Configure AI for detailed analysis',\n                    'Manual review recommended'\n                ]\n            },\n            fallback: true\n        });\n    } else {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            analysisType: 'full',\n            extractedData: {\n                personal: {\n                    firstName: '',\n                    lastName: '',\n                    email: '',\n                    phone: '',\n                    location: '',\n                    summary: extractedText.substring(0, 200)\n                },\n                experience: [],\n                education: [],\n                skills: {\n                    technical: [],\n                    languages: [],\n                    certifications: []\n                },\n                projects: []\n            },\n            atsScore: basicScore,\n            enhancements: {\n                suggestions: [\n                    'AI enhancement unavailable',\n                    'Manual editing required'\n                ]\n            },\n            fallback: true\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/upload-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload-resume%2Froute&page=%2Fapi%2Fupload-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload-resume%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();