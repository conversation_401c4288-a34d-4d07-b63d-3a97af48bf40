import { NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export async function POST(request) {
  try {
    console.log('📄 Resume upload API called');
    
    const formData = await request.formData();
    const file = formData.get('file');
    const analysisType = formData.get('analysisType') || 'full'; // 'full' or 'quick'
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      );
    }

    console.log('📁 File details:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    // Validate file type and size
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload PDF, DOC, or DOCX files only.' },
        { status: 400 }
      );
    }

    // 10MB file size limit
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size too large. Please upload files smaller than 10MB.' },
        { status: 400 }
      );
    }

    // Extract text from the uploaded file
    const buffer = Buffer.from(await file.arrayBuffer());
    let extractedText = '';

    try {
      if (file.type === 'application/pdf') {
        console.log('📄 Processing PDF file...');

        // Try to extract text from PDF
        try {
          const pdfParse = require('pdf-parse');
          const pdfData = await pdfParse(buffer);
          extractedText = pdfData.text;
        } catch (pdfError) {
          console.log('⚠️ PDF parsing failed, using fallback text extraction');
          // Fallback: extract basic text information
          extractedText = `PDF file uploaded: ${file.name}. Please provide resume details manually.`;
        }

      } else if (file.type === 'application/msword' ||
                 file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        console.log('📄 Processing Word document...');

        try {
          const mammoth = require('mammoth');
          const result = await mammoth.extractRawText({ buffer });
          extractedText = result.value;
        } catch (docError) {
          console.log('⚠️ Word document parsing failed, using fallback text extraction');
          // Fallback: extract basic text information
          extractedText = `Word document uploaded: ${file.name}. Please provide resume details manually.`;
        }
      }
    } catch (extractionError) {
      console.error('Text extraction error:', extractionError);
      // Use fallback text instead of failing
      extractedText = `Resume file uploaded: ${file.name}. File processing encountered an issue, but you can still proceed with manual entry.`;
    }

    if (!extractedText || extractedText.trim().length < 20) {
      console.log('⚠️ Minimal text extracted, using fallback analysis');
      extractedText = `Resume file: ${file.name}. File uploaded successfully but text extraction was limited. Please review and edit the form manually.`;
    }

    console.log('✅ Text extracted, length:', extractedText.length);

    // Check if API key is configured
    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {
      console.log('⚠️ Gemini API key not configured, using fallback analysis');
      return createFallbackAnalysis(extractedText, analysisType);
    }

    let analysisResult;

    try {

    // Get the generative model
    const model = genAI.getGenerativeModel({ 
      model: 'gemini-1.5-flash-latest',
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 3000,
      }
    });

    let prompt;
    if (analysisType === 'quick') {
      prompt = createQuickAnalysisPrompt(extractedText);
    } else {
      prompt = createFullAnalysisPrompt(extractedText);
    }

    console.log('🤖 Calling Gemini AI for analysis...');
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const generatedContent = response.text();

      console.log('✅ Gemini analysis completed, length:', generatedContent?.length || 0);

      // Parse the AI response
      analysisResult = parseAIResponse(generatedContent, analysisType);

    } catch (aiError) {
      console.error('AI analysis error:', aiError);
      console.log('⚠️ AI analysis failed, using fallback analysis');
      return createFallbackAnalysis(extractedText, analysisType);
    }

    return NextResponse.json({
      success: true,
      analysisType,
      extractedText: extractedText.substring(0, 1000) + '...', // Truncated for response
      ...analysisResult,
      fileName: file.name,
      fileSize: file.size,
      processedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Resume upload error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process resume', 
        details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

function createQuickAnalysisPrompt(resumeText) {
  return `
You are an expert ATS (Applicant Tracking System) analyzer. Analyze the following resume text and provide a comprehensive ATS compatibility score and recommendations.

RESUME TEXT:
${resumeText}

Please analyze the resume and respond in the following JSON format:
{
  "atsScore": {
    "overall": 85,
    "breakdown": {
      "keywords": 80,
      "formatting": 90,
      "structure": 85,
      "achievements": 75,
      "skills": 95
    }
  },
  "analysis": {
    "strengths": [
      "Strong technical skills section",
      "Quantified achievements in experience"
    ],
    "weaknesses": [
      "Missing industry keywords",
      "Inconsistent formatting"
    ],
    "recommendations": [
      "Add more industry-specific keywords",
      "Quantify more achievements with numbers",
      "Improve section formatting consistency"
    ]
  },
  "keywordAnalysis": {
    "found": ["JavaScript", "React", "Node.js"],
    "missing": ["AWS", "Docker", "Kubernetes"],
    "suggestions": ["Add cloud technologies", "Include DevOps tools"]
  }
}

Provide detailed, actionable feedback for ATS optimization.
`;
}

function createFullAnalysisPrompt(resumeText) {
  return `
You are an expert resume writer and ATS optimization specialist. Analyze the following resume text and extract structured information while providing enhancement suggestions.

RESUME TEXT:
${resumeText}

Please extract and enhance the resume information, then respond in the following JSON format:
{
  "extractedData": {
    "personal": {
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "location": "City, State",
      "linkedin": "linkedin.com/in/johndoe",
      "portfolio": "johndoe.com",
      "summary": "Professional summary extracted from resume"
    },
    "experience": [
      {
        "title": "Software Engineer",
        "company": "Tech Company",
        "location": "City, State",
        "startDate": "2020-01",
        "endDate": "2023-12",
        "current": false,
        "description": "Enhanced description with quantified achievements"
      }
    ],
    "education": [
      {
        "degree": "Bachelor of Science in Computer Science",
        "institution": "University Name",
        "location": "City, State",
        "startDate": "2016-09",
        "endDate": "2020-05",
        "gpa": "3.8",
        "relevant": "Relevant coursework"
      }
    ],
    "skills": {
      "technical": ["JavaScript", "React", "Node.js"],
      "languages": ["English", "Spanish"],
      "certifications": ["AWS Certified", "Google Cloud"]
    },
    "projects": [
      {
        "name": "Project Name",
        "description": "Enhanced project description",
        "technologies": "React, Node.js, MongoDB",
        "link": "github.com/project"
      }
    ]
  },
  "atsScore": {
    "overall": 85,
    "breakdown": {
      "keywords": 80,
      "formatting": 90,
      "structure": 85,
      "achievements": 75,
      "skills": 95
    }
  },
  "enhancements": {
    "summary": "AI-enhanced professional summary",
    "experience": [
      {
        "original": "Original job description",
        "enhanced": "Enhanced description with action verbs and quantified results"
      }
    ],
    "suggestions": [
      "Add more quantified achievements",
      "Include industry keywords",
      "Improve formatting consistency"
    ]
  }
}

Extract all available information and provide enhanced, ATS-optimized versions.
`;
}

function parseAIResponse(generatedContent, analysisType) {
  try {
    // Try to extract JSON from the response
    const jsonMatch = generatedContent.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsedJson = JSON.parse(jsonMatch[0]);
      return parsedJson;
    }
  } catch (error) {
    console.error('Error parsing AI response:', error);
  }

  // Fallback response
  if (analysisType === 'quick') {
    return {
      atsScore: {
        overall: 75,
        breakdown: {
          keywords: 70,
          formatting: 80,
          structure: 75,
          achievements: 70,
          skills: 80
        }
      },
      analysis: {
        strengths: ['Professional experience listed', 'Education section present'],
        weaknesses: ['Limited quantified achievements', 'Missing keywords'],
        recommendations: ['Add more metrics and numbers', 'Include industry-specific terms']
      }
    };
  } else {
    return {
      extractedData: {
        personal: { firstName: '', lastName: '', email: '', phone: '', location: '', summary: '' },
        experience: [],
        education: [],
        skills: { technical: [], languages: [], certifications: [] },
        projects: []
      },
      atsScore: { overall: 75, breakdown: { keywords: 70, formatting: 80, structure: 75, achievements: 70, skills: 80 } },
      enhancements: { suggestions: ['Unable to parse AI response', 'Please try again'] }
    };
  }
}

function createFallbackAnalysis(extractedText, analysisType) {
  const basicScore = {
    overall: 75,
    breakdown: {
      keywords: 70,
      formatting: 80,
      structure: 75,
      achievements: 70,
      skills: 75
    }
  };

  if (analysisType === 'quick') {
    return NextResponse.json({
      success: true,
      analysisType: 'quick',
      atsScore: basicScore,
      analysis: {
        strengths: ['Resume file uploaded successfully', 'Ready for manual review'],
        weaknesses: ['Text extraction limited', 'Manual optimization needed'],
        recommendations: [
          'Review and fill out the form manually',
          'Add quantified achievements',
          'Include relevant keywords for your industry',
          'Ensure consistent formatting'
        ]
      },
      keywordAnalysis: {
        found: [],
        missing: ['Industry-specific keywords', 'Technical skills', 'Action verbs'],
        suggestions: ['Add relevant technical skills', 'Include measurable achievements', 'Use industry terminology']
      },
      fallback: true
    });
  } else {
    return NextResponse.json({
      success: true,
      analysisType: 'full',
      extractedData: {
        personal: {
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          location: '',
          linkedin: '',
          portfolio: '',
          summary: extractedText.includes('uploaded successfully') ? '' : extractedText.substring(0, 200)
        },
        experience: [{
          id: 1,
          title: '',
          company: '',
          location: '',
          startDate: '',
          endDate: '',
          current: false,
          description: ''
        }],
        education: [{
          id: 1,
          degree: '',
          institution: '',
          location: '',
          startDate: '',
          endDate: '',
          gpa: '',
          relevant: ''
        }],
        skills: { technical: [], languages: [], certifications: [] },
        projects: [{
          id: 1,
          name: '',
          description: '',
          technologies: '',
          link: ''
        }]
      },
      atsScore: basicScore,
      enhancements: {
        suggestions: [
          'File uploaded successfully - please review and edit the form',
          'Add your personal information',
          'Fill in your work experience with quantified achievements',
          'Include relevant technical skills',
          'Add education details',
          'Consider adding projects or certifications'
        ]
      },
      fallback: true
    });
  }
}
