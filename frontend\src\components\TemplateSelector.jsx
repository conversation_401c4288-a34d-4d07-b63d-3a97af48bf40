"use client";
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Check, 
  Eye, 
  Palette, 
  FileText, 
  Sparkles,
  Zap,
  Minimize2,
  Crown
} from 'lucide-react';

const TemplateSelector = ({ selectedTemplate, onTemplateSelect, onClose }) => {
  const [previewTemplate, setPreviewTemplate] = useState(selectedTemplate);

  const templates = [
    {
      id: 'modern',
      name: 'Modern Professional',
      description: 'Clean, modern design with neural theme colors and gradient header',
      category: 'Professional',
      icon: Sparkles,
      color: 'from-neural-blue to-neural-purple',
      features: ['Gradient Header', 'Modern Typography', 'ATS-Friendly', 'Neural Theme'],
      preview: '/templates/modern-preview.png'
    },
    {
      id: 'classic',
      name: 'Classic Executive',
      description: 'Traditional professional layout with elegant serif typography',
      category: 'Traditional',
      icon: Crown,
      color: 'from-gray-600 to-gray-800',
      features: ['Serif Typography', 'Executive Style', 'Traditional Layout', 'Professional'],
      preview: '/templates/classic-preview.png'
    },
    {
      id: 'creative',
      name: 'Creative Tech',
      description: 'Contemporary two-column design for tech and creative professionals',
      category: 'Creative',
      icon: Palette,
      color: 'from-purple-500 to-indigo-600',
      features: ['Two-Column Layout', 'Sidebar Design', 'Creative Style', 'Tech-Focused'],
      preview: '/templates/creative-preview.png'
    },
    {
      id: 'minimal',
      name: 'Minimal Clean',
      description: 'Minimalist design focusing on content clarity and readability',
      category: 'Minimal',
      icon: Minimize2,
      color: 'from-slate-500 to-slate-700',
      features: ['Clean Design', 'Minimal Style', 'Content Focus', 'High Readability'],
      preview: '/templates/minimal-preview.png'
    }
  ];

  const handleTemplateSelect = (templateId) => {
    setPreviewTemplate(templateId);
  };

  const handleConfirmSelection = () => {
    onTemplateSelect(previewTemplate);
    onClose();
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-gray-900 rounded-2xl p-6 max-w-6xl w-full max-h-[90vh] overflow-y-auto border border-gray-700"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">Choose Your Resume Template</h2>
            <p className="text-gray-400">Select a professional template that matches your style and industry</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Template Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {templates.map((template) => {
            const Icon = template.icon;
            const isSelected = previewTemplate === template.id;
            
            return (
              <motion.div
                key={template.id}
                className={`relative bg-gray-800 rounded-xl p-6 border-2 cursor-pointer transition-all duration-300 ${
                  isSelected 
                    ? 'border-neural-purple shadow-lg shadow-neural-purple/20' 
                    : 'border-gray-700 hover:border-gray-600'
                }`}
                onClick={() => handleTemplateSelect(template.id)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {/* Selection Indicator */}
                {isSelected && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-neural-purple rounded-full flex items-center justify-center"
                  >
                    <Check className="w-4 h-4 text-white" />
                  </motion.div>
                )}

                {/* Template Icon */}
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${template.color} flex items-center justify-center mb-4`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>

                {/* Template Info */}
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-white mb-1">{template.name}</h3>
                  <span className="text-xs px-2 py-1 bg-gray-700 text-gray-300 rounded-full">
                    {template.category}
                  </span>
                </div>

                <p className="text-sm text-gray-400 mb-4 leading-relaxed">
                  {template.description}
                </p>

                {/* Features */}
                <div className="space-y-2">
                  {template.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-neural-purple rounded-full" />
                      <span className="text-xs text-gray-300">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Preview Button */}
                <motion.button
                  className="w-full mt-4 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Eye className="w-4 h-4" />
                  Preview
                </motion.button>
              </motion.div>
            );
          })}
        </div>

        {/* Template Preview */}
        <div className="bg-gray-800 rounded-xl p-6 mb-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <FileText className="w-5 h-5 text-neural-purple" />
            Template Preview: {templates.find(t => t.id === previewTemplate)?.name}
          </h3>
          
          <div className="bg-white rounded-lg p-8 min-h-[400px] flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <FileText className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-gray-600 mb-2">Resume Preview</p>
              <p className="text-sm text-gray-500">
                {templates.find(t => t.id === previewTemplate)?.description}
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-400">
            Selected: <span className="text-white font-medium">
              {templates.find(t => t.id === previewTemplate)?.name}
            </span>
          </div>
          
          <div className="flex gap-3">
            <motion.button
              onClick={onClose}
              className="px-6 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Cancel
            </motion.button>
            
            <motion.button
              onClick={handleConfirmSelection}
              className="px-6 py-2 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Use This Template
            </motion.button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default TemplateSelector;
