"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Circle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Circle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Circle\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ]\n]);\n //# sourceMappingURL=circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2lyY2xlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sZUFBUyxnRUFBZ0IsQ0FBQyxRQUFVO0lBQ3hDO1FBQUMsUUFBVTtRQUFBO1lBQUUsRUFBSTtZQUFNLENBQUksUUFBTTtZQUFBLENBQUc7WUFBTSxHQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQzFEIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXHNyY1xcaWNvbnNcXGNpcmNsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENpcmNsZVxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4WTJseVkyeGxJR040UFNJeE1pSWdZM2s5SWpFeUlpQnlQU0l4TUNJZ0x6NEtQQzl6ZG1jK0NnPT0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NpcmNsZVxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENpcmNsZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NpcmNsZScsIFtcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzEwJywga2V5OiAnMW1nbGF5JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBDaXJjbGU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/save.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Save)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Save = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Save\", [\n    [\n        \"path\",\n        {\n            d: \"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z\",\n            key: \"1c8476\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7\",\n            key: \"1ydtos\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 3v4a1 1 0 0 0 1 1h7\",\n            key: \"t51u73\"\n        }\n    ]\n]);\n //# sourceMappingURL=save.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* harmony import */ var _components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ATSScoreCircle */ \"(app-pages-browser)/./src/components/ATSScoreCircle.jsx\");\n/* harmony import */ var _components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/EnhancedResumePreview */ \"(app-pages-browser)/./src/components/EnhancedResumePreview.jsx\");\n/* harmony import */ var _components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/PDFDownload */ \"(app-pages-browser)/./src/components/PDFDownload.jsx\");\n/* harmony import */ var _components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ResumeBuilderModeToggle */ \"(app-pages-browser)/./src/components/ResumeBuilderModeToggle.jsx\");\n/* harmony import */ var _components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ResumeUpload */ \"(app-pages-browser)/./src/components/ResumeUpload.jsx\");\n/* harmony import */ var _components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ATSAnalysisDisplay */ \"(app-pages-browser)/./src/components/ATSAnalysisDisplay.jsx\");\n/* harmony import */ var _components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/JobDescriptionInput */ \"(app-pages-browser)/./src/components/JobDescriptionInput.jsx\");\n/* harmony import */ var _components_BeforeAfterComparison__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/BeforeAfterComparison */ \"(app-pages-browser)/./src/components/BeforeAfterComparison.jsx\");\n/* harmony import */ var _components_AIContentSuggestions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AIContentSuggestions */ \"(app-pages-browser)/./src/components/AIContentSuggestions.jsx\");\n/* harmony import */ var _components_ATSOptimizationPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ATSOptimizationPanel */ \"(app-pages-browser)/./src/components/ATSOptimizationPanel.jsx\");\n/* harmony import */ var _components_TemplateSelector__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/TemplateSelector */ \"(app-pages-browser)/./src/components/TemplateSelector.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* harmony import */ var _components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ATSFieldIndicator */ \"(app-pages-browser)/./src/components/ATSFieldIndicator.jsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./src/components/ClientOnly.jsx\");\n/* harmony import */ var _components_ATSTooltip__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ATSTooltip */ \"(app-pages-browser)/./src/components/ATSTooltip.jsx\");\n/* harmony import */ var _components_StepIndicator__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/StepIndicator */ \"(app-pages-browser)/./src/components/StepIndicator.jsx\");\n/* harmony import */ var _components_StickyNavigation__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/StickyNavigation */ \"(app-pages-browser)/./src/components/StickyNavigation.jsx\");\n/* harmony import */ var _components_FormHeader__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/FormHeader */ \"(app-pages-browser)/./src/components/FormHeader.jsx\");\n/* harmony import */ var _components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ResumeFormComponents */ \"(app-pages-browser)/./src/components/ResumeFormComponents.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import SuccessScreen from \"@/components/SuccessScreen\";\n\nconst ResumeBuilder = ()=>{\n    _s();\n    // Main state management\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for upload functionality\n    const [builderMode, setBuilderMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create'); // 'create', 'upload', 'analyze'\n    const [uploadAnalysis, setUploadAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAnalysis, setShowAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced upload workflow state\n    const [showJobDescriptionInput, setShowJobDescriptionInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingTargeted, setIsGeneratingTargeted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetedResumeData, setTargetedResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Template selection state\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ]\n    });\n    // Real-time ATS analysis\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(formData);\n    // Steps configuration\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            description: \"Tell us about yourself\"\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            description: \"Your academic background\"\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            description: \"Your work experience\"\n        },\n        {\n            id: 3,\n            title: \"Skills & Projects\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            description: \"Showcase your abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            description: \"Finalize your resume\"\n        }\n    ];\n    // Update form data\n    const updateFormData = function(section, field, value) {\n        let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        setFormData((prev)=>{\n            if (index !== null && Array.isArray(prev[section])) {\n                const newArray = [\n                    ...prev[section]\n                ];\n                newArray[index] = {\n                    ...newArray[index],\n                    [field]: value\n                };\n                return {\n                    ...prev,\n                    [section]: newArray\n                };\n            } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                return {\n                    ...prev,\n                    [section]: {\n                        ...prev[section],\n                        [field]: value\n                    }\n                };\n            }\n            return prev;\n        });\n    };\n    // Add new item to array sections\n    const addArrayItem = (section, template)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: [\n                    ...prev[section],\n                    {\n                        ...template,\n                        id: Math.random().toString(36).substring(2, 11)\n                    }\n                ]\n            }));\n    };\n    // Remove item from array sections\n    const removeArrayItem = (section, id)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: prev[section].filter((item)=>item.id !== id)\n            }));\n    };\n    // Navigation functions\n    const nextStep = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    // Generate resume with AI\n    const generateResume = async ()=>{\n        try {\n            var _data_resumeData_atsScore, _data_resumeData, _data_resumeData_atsScore1, _data_resumeData1;\n            console.log('🚀 Starting resume generation...');\n            console.log('📝 Form data:', formData);\n            setIsGenerating(true);\n            setShowProgressBar(true);\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    templateId: selectedTemplate\n                })\n            });\n            console.log('📡 API response status:', response.status);\n            console.log('📡 API response headers:', Object.fromEntries(response.headers.entries()));\n            // Check if response is actually JSON\n            const contentType = response.headers.get('content-type');\n            if (!contentType || !contentType.includes('application/json')) {\n                const textResponse = await response.text();\n                console.error('❌ Non-JSON response received:', textResponse);\n                throw new Error('Server returned non-JSON response');\n            }\n            const data = await response.json();\n            console.log('📊 API response data:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            // Store the enhanced resume data and ATS information\n            console.log('✅ Setting resume data...');\n            setResumeUrl(data.downloadUrl);\n            setResumeData(data.resumeData);\n            setAtsScore(data.atsScore || ((_data_resumeData = data.resumeData) === null || _data_resumeData === void 0 ? void 0 : (_data_resumeData_atsScore = _data_resumeData.atsScore) === null || _data_resumeData_atsScore === void 0 ? void 0 : _data_resumeData_atsScore.overall) || 75);\n            setSuggestions(data.suggestions || ((_data_resumeData1 = data.resumeData) === null || _data_resumeData1 === void 0 ? void 0 : (_data_resumeData_atsScore1 = _data_resumeData1.atsScore) === null || _data_resumeData_atsScore1 === void 0 ? void 0 : _data_resumeData_atsScore1.improvements) || []);\n            console.log('🎉 Resume generation completed successfully');\n        } catch (error) {\n            console.error(\"💥 Error generating resume:\", error);\n            console.error(\"Error details:\", {\n                message: error.message,\n                stack: error.stack,\n                name: error.name\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || \"Failed to generate resume\");\n            setIsGenerating(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsGenerating(false);\n        // Show success message with ATS score\n        const scoreMessage = atsScore ? \"Resume generated! ATS Score: \".concat(atsScore, \"%\") : \"Your resume has been generated successfully!\";\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(scoreMessage);\n    };\n    // Handle upload analysis completion\n    const handleUploadAnalysis = (analysisData)=>{\n        console.log('📊 Upload analysis received:', analysisData);\n        setUploadAnalysis(analysisData);\n        // For upload & enhance mode, show job description input\n        if (builderMode === 'upload') {\n            setShowJobDescriptionInput(true);\n        } else {\n            // For quick analysis, show results immediately\n            setShowAnalysis(true);\n        }\n        // If it's a full analysis, populate form data (even with minimal data)\n        if (analysisData.analysisType === 'full' && analysisData.extractedData) {\n            const extracted = analysisData.extractedData;\n            console.log('📋 Extracted data for form population:', extracted);\n            // Update form data with extracted information\n            setFormData((prevData)=>{\n                var _extracted_personal, _extracted_personal1, _extracted_personal2, _extracted_personal3, _extracted_personal4, _extracted_personal5, _extracted_personal6, _extracted_personal7, _extracted_education, _extracted_experience, _extracted_skills_technical, _extracted_skills, _extracted_skills_languages, _extracted_skills1, _extracted_skills_certifications, _extracted_skills2, _extracted_projects;\n                return {\n                    ...prevData,\n                    personal: {\n                        ...prevData.personal,\n                        firstName: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.firstName) || prevData.personal.firstName,\n                        lastName: ((_extracted_personal1 = extracted.personal) === null || _extracted_personal1 === void 0 ? void 0 : _extracted_personal1.lastName) || prevData.personal.lastName,\n                        email: ((_extracted_personal2 = extracted.personal) === null || _extracted_personal2 === void 0 ? void 0 : _extracted_personal2.email) || prevData.personal.email,\n                        phone: ((_extracted_personal3 = extracted.personal) === null || _extracted_personal3 === void 0 ? void 0 : _extracted_personal3.phone) || prevData.personal.phone,\n                        location: ((_extracted_personal4 = extracted.personal) === null || _extracted_personal4 === void 0 ? void 0 : _extracted_personal4.location) || prevData.personal.location,\n                        linkedin: ((_extracted_personal5 = extracted.personal) === null || _extracted_personal5 === void 0 ? void 0 : _extracted_personal5.linkedin) || prevData.personal.linkedin,\n                        portfolio: ((_extracted_personal6 = extracted.personal) === null || _extracted_personal6 === void 0 ? void 0 : _extracted_personal6.portfolio) || prevData.personal.portfolio,\n                        summary: ((_extracted_personal7 = extracted.personal) === null || _extracted_personal7 === void 0 ? void 0 : _extracted_personal7.summary) || prevData.personal.summary\n                    },\n                    education: ((_extracted_education = extracted.education) === null || _extracted_education === void 0 ? void 0 : _extracted_education.length) > 0 ? extracted.education.map((edu)=>({\n                            ...edu,\n                            id: edu.id || Date.now() + Math.random()\n                        })) : prevData.education,\n                    experience: ((_extracted_experience = extracted.experience) === null || _extracted_experience === void 0 ? void 0 : _extracted_experience.length) > 0 ? extracted.experience.map((exp)=>({\n                            ...exp,\n                            id: exp.id || Date.now() + Math.random()\n                        })) : prevData.experience,\n                    skills: {\n                        technical: ((_extracted_skills = extracted.skills) === null || _extracted_skills === void 0 ? void 0 : (_extracted_skills_technical = _extracted_skills.technical) === null || _extracted_skills_technical === void 0 ? void 0 : _extracted_skills_technical.length) > 0 ? extracted.skills.technical : prevData.skills.technical,\n                        languages: ((_extracted_skills1 = extracted.skills) === null || _extracted_skills1 === void 0 ? void 0 : (_extracted_skills_languages = _extracted_skills1.languages) === null || _extracted_skills_languages === void 0 ? void 0 : _extracted_skills_languages.length) > 0 ? extracted.skills.languages : prevData.skills.languages,\n                        certifications: ((_extracted_skills2 = extracted.skills) === null || _extracted_skills2 === void 0 ? void 0 : (_extracted_skills_certifications = _extracted_skills2.certifications) === null || _extracted_skills_certifications === void 0 ? void 0 : _extracted_skills_certifications.length) > 0 ? extracted.skills.certifications : prevData.skills.certifications\n                    },\n                    projects: ((_extracted_projects = extracted.projects) === null || _extracted_projects === void 0 ? void 0 : _extracted_projects.length) > 0 ? extracted.projects.map((proj)=>({\n                            ...proj,\n                            id: proj.id || Date.now() + Math.random()\n                        })) : prevData.projects\n                };\n            });\n            // Set ATS score and suggestions\n            if (analysisData.atsScore) {\n                var _analysisData_enhancements, _analysisData_analysis;\n                setAtsScore(analysisData.atsScore.overall);\n                setSuggestions(((_analysisData_enhancements = analysisData.enhancements) === null || _analysisData_enhancements === void 0 ? void 0 : _analysisData_enhancements.suggestions) || ((_analysisData_analysis = analysisData.analysis) === null || _analysisData_analysis === void 0 ? void 0 : _analysisData_analysis.recommendations) || []);\n            }\n            console.log('✅ Form data updated with extracted information');\n        } else if (analysisData.fallback) {\n            console.log('⚠️ Using fallback data - minimal extraction');\n            // Even with fallback, try to extract any available information\n            if (analysisData.extractedData) {\n                const extracted = analysisData.extractedData;\n                setFormData((prevData)=>{\n                    var _extracted_personal;\n                    return {\n                        ...prevData,\n                        personal: {\n                            ...prevData.personal,\n                            summary: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.summary) || 'Please add your professional summary here.'\n                        }\n                    };\n                });\n            }\n        }\n    };\n    // Handle job description submission for targeted resume generation\n    const handleJobDescriptionSubmit = async (jobData)=>{\n        if (!(uploadAnalysis === null || uploadAnalysis === void 0 ? void 0 : uploadAnalysis.extractedData)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('No resume data available. Please upload a resume first.');\n            return;\n        }\n        try {\n            setIsGeneratingTargeted(true);\n            console.log('🎯 Generating targeted resume with job data:', jobData);\n            const response = await fetch('/api/generate-targeted-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    extractedResumeData: uploadAnalysis.extractedData,\n                    jobDescription: jobData.description,\n                    jobTitle: jobData.jobTitle,\n                    company: jobData.company\n                })\n            });\n            const data = await response.json();\n            console.log('📊 Targeted resume response:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate targeted resume');\n            }\n            // Update form data with enhanced resume\n            if (data.enhancedResume) {\n                var _data_enhancedResume_personal, _data_enhancedResume_personal1, _data_enhancedResume_personal2, _data_enhancedResume_personal3, _data_enhancedResume_personal4, _data_enhancedResume_personal5, _data_enhancedResume_personal6, _data_enhancedResume_personal7, _data_enhancedResume_education, _data_enhancedResume_experience, _data_enhancedResume_skills, _data_enhancedResume_skills1, _data_enhancedResume_skills2, _data_enhancedResume_projects, _data_atsScore;\n                // Properly structure the enhanced resume data to match form data structure\n                const enhancedFormData = {\n                    personal: {\n                        firstName: ((_data_enhancedResume_personal = data.enhancedResume.personal) === null || _data_enhancedResume_personal === void 0 ? void 0 : _data_enhancedResume_personal.firstName) || '',\n                        lastName: ((_data_enhancedResume_personal1 = data.enhancedResume.personal) === null || _data_enhancedResume_personal1 === void 0 ? void 0 : _data_enhancedResume_personal1.lastName) || '',\n                        email: ((_data_enhancedResume_personal2 = data.enhancedResume.personal) === null || _data_enhancedResume_personal2 === void 0 ? void 0 : _data_enhancedResume_personal2.email) || '',\n                        phone: ((_data_enhancedResume_personal3 = data.enhancedResume.personal) === null || _data_enhancedResume_personal3 === void 0 ? void 0 : _data_enhancedResume_personal3.phone) || '',\n                        location: ((_data_enhancedResume_personal4 = data.enhancedResume.personal) === null || _data_enhancedResume_personal4 === void 0 ? void 0 : _data_enhancedResume_personal4.location) || '',\n                        linkedin: ((_data_enhancedResume_personal5 = data.enhancedResume.personal) === null || _data_enhancedResume_personal5 === void 0 ? void 0 : _data_enhancedResume_personal5.linkedin) || '',\n                        portfolio: ((_data_enhancedResume_personal6 = data.enhancedResume.personal) === null || _data_enhancedResume_personal6 === void 0 ? void 0 : _data_enhancedResume_personal6.portfolio) || '',\n                        summary: ((_data_enhancedResume_personal7 = data.enhancedResume.personal) === null || _data_enhancedResume_personal7 === void 0 ? void 0 : _data_enhancedResume_personal7.summary) || ''\n                    },\n                    education: ((_data_enhancedResume_education = data.enhancedResume.education) === null || _data_enhancedResume_education === void 0 ? void 0 : _data_enhancedResume_education.length) > 0 ? data.enhancedResume.education.map((edu)=>({\n                            id: edu.id || Date.now() + Math.random(),\n                            degree: edu.degree || '',\n                            institution: edu.institution || '',\n                            location: edu.location || '',\n                            startDate: edu.startDate || '',\n                            endDate: edu.endDate || '',\n                            gpa: edu.gpa || '',\n                            relevant: edu.relevant || ''\n                        })) : [\n                        {\n                            id: 1,\n                            degree: \"\",\n                            institution: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            gpa: \"\",\n                            relevant: \"\"\n                        }\n                    ],\n                    experience: ((_data_enhancedResume_experience = data.enhancedResume.experience) === null || _data_enhancedResume_experience === void 0 ? void 0 : _data_enhancedResume_experience.length) > 0 ? data.enhancedResume.experience.map((exp)=>({\n                            id: exp.id || Date.now() + Math.random(),\n                            title: exp.title || '',\n                            company: exp.company || '',\n                            location: exp.location || '',\n                            startDate: exp.startDate || '',\n                            endDate: exp.endDate || '',\n                            current: exp.current || false,\n                            description: exp.description || (exp.achievements ? exp.achievements.join('\\n') : '')\n                        })) : [\n                        {\n                            id: 1,\n                            title: \"\",\n                            company: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            current: false,\n                            description: \"\"\n                        }\n                    ],\n                    skills: {\n                        technical: ((_data_enhancedResume_skills = data.enhancedResume.skills) === null || _data_enhancedResume_skills === void 0 ? void 0 : _data_enhancedResume_skills.technical) || [],\n                        languages: ((_data_enhancedResume_skills1 = data.enhancedResume.skills) === null || _data_enhancedResume_skills1 === void 0 ? void 0 : _data_enhancedResume_skills1.languages) || [],\n                        certifications: ((_data_enhancedResume_skills2 = data.enhancedResume.skills) === null || _data_enhancedResume_skills2 === void 0 ? void 0 : _data_enhancedResume_skills2.certifications) || []\n                    },\n                    projects: ((_data_enhancedResume_projects = data.enhancedResume.projects) === null || _data_enhancedResume_projects === void 0 ? void 0 : _data_enhancedResume_projects.length) > 0 ? data.enhancedResume.projects.map((proj)=>({\n                            id: proj.id || Date.now() + Math.random(),\n                            name: proj.name || '',\n                            description: proj.description || '',\n                            technologies: proj.technologies || '',\n                            link: proj.link || ''\n                        })) : [\n                        {\n                            id: 1,\n                            name: \"\",\n                            description: \"\",\n                            technologies: \"\",\n                            link: \"\"\n                        }\n                    ]\n                };\n                setFormData(enhancedFormData);\n                setTargetedResumeData(data);\n                setAtsScore(((_data_atsScore = data.atsScore) === null || _data_atsScore === void 0 ? void 0 : _data_atsScore.overall) || 85);\n                setSuggestions(data.recommendations || []);\n                // Show success and move to form editing\n                setShowJobDescriptionInput(false);\n                setCurrentStep(0);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume optimized for the target job!');\n            }\n        } catch (error) {\n            console.error('Targeted resume generation error:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate targeted resume');\n        } finally{\n            setIsGeneratingTargeted(false);\n        }\n    };\n    // Handle mode change\n    const handleModeChange = (mode)=>{\n        setBuilderMode(mode);\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setShowJobDescriptionInput(false);\n        setIsGeneratingTargeted(false);\n        setTargetedResumeData(null);\n    };\n    // Reset to create mode\n    const resetToCreateMode = ()=>{\n        setBuilderMode('create');\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setFormData({\n            personal: {\n                firstName: \"\",\n                lastName: \"\",\n                email: \"\",\n                phone: \"\",\n                location: \"\",\n                linkedin: \"\",\n                portfolio: \"\",\n                summary: \"\"\n            },\n            education: [\n                {\n                    id: 1,\n                    degree: \"\",\n                    institution: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    gpa: \"\",\n                    relevant: \"\"\n                }\n            ],\n            experience: [\n                {\n                    id: 1,\n                    title: \"\",\n                    company: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    current: false,\n                    description: \"\"\n                }\n            ],\n            skills: {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: [\n                {\n                    id: 1,\n                    name: \"\",\n                    description: \"\",\n                    technologies: \"\",\n                    link: \"\"\n                }\n            ]\n        });\n    };\n    // Floating background elements with deterministic values to avoid hydration mismatch\n    const FloatingElements = ()=>{\n        // Use deterministic values to avoid hydration mismatch\n        const elements = [\n            {\n                width: 180,\n                height: 160,\n                left: 10,\n                top: 20,\n                duration: 15,\n                x: 30,\n                y: 40\n            },\n            {\n                width: 220,\n                height: 190,\n                left: 80,\n                top: 60,\n                duration: 18,\n                x: -25,\n                y: -30\n            },\n            {\n                width: 150,\n                height: 140,\n                left: 60,\n                top: 80,\n                duration: 12,\n                x: 35,\n                y: 25\n            },\n            {\n                width: 200,\n                height: 170,\n                left: 30,\n                top: 40,\n                duration: 20,\n                x: -40,\n                y: 35\n            },\n            {\n                width: 170,\n                height: 200,\n                left: 90,\n                top: 10,\n                duration: 16,\n                x: 20,\n                y: -45\n            },\n            {\n                width: 190,\n                height: 150,\n                left: 20,\n                top: 70,\n                duration: 14,\n                x: -30,\n                y: 20\n            },\n            {\n                width: 160,\n                height: 180,\n                left: 70,\n                top: 30,\n                duration: 22,\n                x: 45,\n                y: -25\n            },\n            {\n                width: 210,\n                height: 160,\n                left: 50,\n                top: 90,\n                duration: 17,\n                x: -20,\n                y: 30\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: elements.map((element, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                    className: \"absolute rounded-full opacity-10 blur-xl\",\n                    style: {\n                        backgroundColor: '#832ED3',\n                        width: element.width,\n                        height: element.height,\n                        left: element.left + '%',\n                        top: element.top + '%'\n                    },\n                    animate: {\n                        x: [\n                            0,\n                            element.x,\n                            0\n                        ],\n                        y: [\n                            0,\n                            element.y,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: element.duration,\n                        repeat: Infinity,\n                        repeatType: 'reverse',\n                        ease: \"easeInOut\"\n                    }\n                }, i, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 517,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 515,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Show upload analysis results for quick ATS check\n    if (builderMode === 'analyze' && showAnalysis && uploadAnalysis) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 547,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 551,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                        children: \"ATS Analysis Results\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Here's your comprehensive resume analysis and recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 559,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"quick\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 568,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                        onClick: ()=>handleModeChange('upload'),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Enhance This Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                        onClick: resetToCreateMode,\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Start Fresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 554,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 553,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 546,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show success screen if resume is generated\n    if (resumeGenerated && resumeData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 606,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 605,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 609,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                        initial: {\n                                            scale: 0\n                                        },\n                                        animate: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"h-10 w-10 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                        children: \"Resume Generated Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300\",\n                                        children: \"Your AI-optimized resume is ready for download\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6 text-center\",\n                                                children: \"ATS Compatibility Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    score: atsScore || 75,\n                                                    size: 150\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6\",\n                                                children: \"AI Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-left\",\n                                                children: suggestions.length > 0 ? suggestions.slice(0, 4).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                                        className: \"flex items-start gap-3 p-3 bg-neural-purple/10 rounded-lg border border-neural-purple/20\",\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 10\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            delay: 0.8 + index * 0.1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-neural-purple rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: typeof suggestion === 'string' ? suggestion : suggestion.message || suggestion\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 23\n                                                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-center\",\n                                                    children: \"No specific recommendations at this time.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 638,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mb-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        formData: formData,\n                                        resumeData: resumeData,\n                                        templateId: selectedTemplate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__.ViewResumeButton, {\n                                        formData: formData,\n                                        resumeData: resumeData,\n                                        templateId: selectedTemplate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                        onClick: ()=>setShowPreview(!showPreview),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 text-neural-blue hover:text-white font-medium rounded-xl transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? 'Hide Preview' : 'View Preview'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                        onClick: ()=>{\n                                            setResumeGenerated(false);\n                                            setResumeData(null);\n                                            setAtsScore(null);\n                                            setSuggestions([]);\n                                            setCurrentStep(0);\n                                        },\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 724,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create Another Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 684,\n                                columnNumber: 13\n                            }, undefined),\n                            showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        formData: formData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 731,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 612,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 611,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 603,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 752,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 751,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 755,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 757,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                        className: \"text-center mb-8 md:mb-12 relative z-20\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-4 md:mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                className: \"h-8 w-8 md:h-10 md:w-10 text-neural-pink animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl lg:text-5xl font-bold text-white\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 770,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-4\",\n                                children: \"Create professional, ATS-friendly resumes in minutes with our AI-powered builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 779,\n                                columnNumber: 11\n                            }, undefined),\n                            builderMode === 'create' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            \"Step \",\n                                            currentStep + 1,\n                                            \" of \",\n                                            steps.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 787,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 785,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 764,\n                        columnNumber: 9\n                    }, undefined),\n                    (builderMode !== 'create' || currentStep === 0) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        currentMode: builderMode,\n                        onModeChange: handleModeChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 795,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'upload' || builderMode === 'analyze') && !showAnalysis && !showJobDescriptionInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            onAnalysisComplete: handleUploadAnalysis,\n                            analysisType: builderMode === 'analyze' ? 'quick' : 'full'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 808,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 803,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showJobDescriptionInput && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Uploaded Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Now provide the job description to create a targeted, ATS-optimized resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 822,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                onJobDescriptionSubmit: handleJobDescriptionSubmit,\n                                isLoading: isGeneratingTargeted\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 827,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                    onClick: ()=>{\n                                        setShowJobDescriptionInput(false);\n                                        setCurrentStep(0);\n                                    },\n                                    className: \"inline-flex items-center gap-2 px-6 py-3 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: \"Skip Job Targeting\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 832,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 817,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showAnalysis && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Analysis Complete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Review the extracted information and continue to enhance your resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 855,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 860,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                    onClick: ()=>setCurrentStep(0),\n                                    className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Continue to Form Editor\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 866,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 865,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 850,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'create' || builderMode === 'upload' && currentStep >= 0 && !showJobDescriptionInput) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            builderMode === 'create' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 md:mb-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center space-x-2 md:space-x-4 overflow-x-auto pb-4 px-4\",\n                                    children: steps.map((step, index)=>{\n                                        const Icon = step.icon;\n                                        const isActive = index === currentStep;\n                                        const isCompleted = index < currentStep;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                            className: \"flex items-center space-x-2 px-3 md:px-4 py-2 md:py-3 rounded-full border transition-all duration-300 min-w-fit \".concat(isActive ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25' : isCompleted ? 'bg-neural-purple/20 border-neural-purple/50' : 'bg-gray-800/50 border-gray-700'),\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4 md:h-5 md:w-5 \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs md:text-sm font-medium hidden sm:block \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400'),\n                                                    children: step.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium sm:hidden \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400'),\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, step.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 884,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/10 shadow-2xl\",\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 md:mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(steps[currentStep].icon, {\n                                                                    className: \"h-6 w-6 md:h-7 md:w-7 text-neural-purple\"\n                                                                }),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl md:text-2xl font-bold text-white\",\n                                                                    children: steps[currentStep].title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm md:text-base\",\n                                                            children: steps[currentStep].description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_36__.AnimatePresence, {\n                                                    mode: \"wait\",\n                                                    children: [\n                                                        currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonalInfoForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EducationForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_22__.ExperienceForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_22__.SkillsProjectsForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_22__.ReviewForm, {\n                                                            formData: formData,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 39\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 924,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-4 md:p-6 border border-white/10 sticky top-4 md:top-8 shadow-2xl\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 0.4\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4 md:mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-base md:text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                        className: \"h-4 w-4 md:h-5 md:w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 19\n                                                                    }, undefined),\n                                                                    \"Live Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 965,\n                                                                columnNumber: 17\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                                                onClick: ()=>setShowPreview(!showPreview),\n                                                                className: \"px-3 py-1.5 md:px-4 md:py-2 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 rounded-lg text-neural-blue hover:text-white transition-all duration-300 text-sm font-medium\",\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                children: showPreview ? 'Hide' : 'Show'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 969,\n                                                                columnNumber: 17\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 964,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-[600px] md:max-h-[700px] overflow-y-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            formData: formData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 17\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12 md:py-16 text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                                            initial: {\n                                                                scale: 0.8,\n                                                                opacity: 0\n                                                            },\n                                                            animate: {\n                                                                scale: 1,\n                                                                opacity: 1\n                                                            },\n                                                            transition: {\n                                                                duration: 0.5\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                    className: \"h-16 w-16 md:h-20 md:w-20 mx-auto mb-4 opacity-30\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm md:text-base mb-2\",\n                                                                    children: \"Preview your resume\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 991,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs md:text-sm text-gray-500\",\n                                                                    children: 'Click \"Show\" to see live updates'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 992,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 985,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 958,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n                                                className: \"mt-6\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 0.6\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSOptimizationPanel__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        formData: formData,\n                                                        atsScore: atsAnalysis.overallScore,\n                                                        suggestions: atsAnalysis.recommendations,\n                                                        realTimeAnalysis: atsAnalysis\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1006,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 999,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 922,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 md:mt-12 max-w-7xl mx-auto px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                        onClick: prevStep,\n                                        disabled: currentStep === 0,\n                                        className: \"flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl transition-all duration-300 font-medium text-sm md:text-base w-full sm:w-auto \".concat(currentStep === 0 ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed border border-gray-700' : 'bg-gray-800/80 hover:bg-gray-700 text-white border border-gray-600 hover:border-gray-500 shadow-lg hover:shadow-xl'),\n                                        whileHover: currentStep !== 0 ? {\n                                            scale: 1.02\n                                        } : {},\n                                        whileTap: currentStep !== 0 ? {\n                                            scale: 0.98\n                                        } : {},\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                className: \"h-4 w-4 md:h-5 md:w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1030,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            \"Previous\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 sm:hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: [\n                                                \"Step \",\n                                                currentStep + 1,\n                                                \" of \",\n                                                steps.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    currentStep === steps.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3 w-full sm:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                                onClick: ()=>setShowTemplateSelector(true),\n                                                className: \"flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl bg-gray-800/80 hover:bg-gray-700 text-white font-medium border border-gray-600 hover:border-gray-500 transition-all duration-300 shadow-lg hover:shadow-xl text-sm md:text-base\",\n                                                whileHover: {\n                                                    scale: 1.02\n                                                },\n                                                whileTap: {\n                                                    scale: 0.98\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                        className: \"h-4 w-4 md:h-5 md:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1049,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Choose Template\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                                onClick: generateResume,\n                                                disabled: isGenerating,\n                                                className: \"flex items-center gap-2 px-8 md:px-10 py-3 md:py-4 rounded-xl bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold hover:opacity-90 transition-all duration-300 disabled:opacity-50 shadow-lg hover:shadow-xl text-sm md:text-base\",\n                                                whileHover: !isGenerating ? {\n                                                    scale: 1.02\n                                                } : {},\n                                                whileTap: !isGenerating ? {\n                                                    scale: 0.98\n                                                } : {},\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        className: \"h-4 w-4 md:h-5 md:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1060,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    isGenerating ? 'Generating...' : 'Generate Resume'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1053,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 13\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.button, {\n                                        onClick: nextStep,\n                                        className: \"flex items-center gap-2 px-6 md:px-8 py-3 md:py-4 rounded-xl bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl text-sm md:text-base w-full sm:w-auto\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            \"Next\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                className: \"h-4 w-4 md:h-5 md:w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1072,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1065,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1018,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TemplateSelector__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        selectedTemplate: selectedTemplate,\n                        onTemplateSelect: setSelectedTemplate,\n                        onClose: ()=>setShowTemplateSelector(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1081,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 762,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 749,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"a0BSqrlX8sRKr1UqCv9NeYWv9AQ=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    ];\n});\n_c = ResumeBuilder;\n// Personal Information Form Component\nconst PersonalInfoForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis } = param;\n    var _atsAnalysis_fieldAnalysis, _atsAnalysis_fieldAnalysis1, _atsAnalysis_fieldAnalysis2, _atsAnalysis_fieldAnalysis3;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6 md:space-y-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                \"First Name *\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSTooltip__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    fieldType: \"firstName\",\n                                    className: \"ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1104,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1102,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.firstName,\n                            onChange: (e)=>updateFormData('personal', 'firstName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Rahul\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1106,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                fieldName: \"firstName\",\n                                value: formData.personal.firstName,\n                                analysis: (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis.firstName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1114,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1113,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1101,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Last Name *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1123,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.lastName,\n                            onChange: (e)=>updateFormData('personal', 'lastName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Sharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1126,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                fieldName: \"lastName\",\n                                value: formData.personal.lastName,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis1 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis1 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis1.lastName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1134,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1133,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1122,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1144,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Email *\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1143,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            value: formData.personal.email,\n                            onChange: (e)=>updateFormData('personal', 'email', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"<EMAIL>\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1147,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                fieldName: \"email\",\n                                value: formData.personal.email,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis2 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis2 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis2.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1155,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1154,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1142,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1165,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Phone\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1164,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"tel\",\n                            value: formData.personal.phone,\n                            onChange: (e)=>updateFormData('personal', 'phone', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"+91 98765 43210\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1168,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1163,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1179,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Location\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1178,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.location,\n                            onChange: (e)=>updateFormData('personal', 'location', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Mumbai, Maharashtra\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1182,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1177,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1193,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"LinkedIn\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1192,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.linkedin,\n                            onChange: (e)=>updateFormData('personal', 'linkedin', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://linkedin.com/in/rahulsharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1196,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1191,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1207,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Portfolio/Website\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1206,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.portfolio,\n                            onChange: (e)=>updateFormData('personal', 'portfolio', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://rahulsharma.dev\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1210,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1205,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Professional Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1220,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: formData.personal.summary,\n                            onChange: (e)=>updateFormData('personal', 'summary', e.target.value),\n                            rows: 4,\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 resize-none transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Experienced software engineer with 5+ years in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable solutions and leading cross-functional teams...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1223,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                fieldName: \"summary\",\n                                value: formData.personal.summary,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis3 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis3 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis3.summary,\n                                showDetails: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1231,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1230,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIContentSuggestions__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                fieldType: \"summary\",\n                                currentValue: formData.personal.summary,\n                                onSuggestionApply: (suggestion)=>updateFormData('personal', 'summary', suggestion),\n                                context: {\n                                    firstName: formData.personal.firstName,\n                                    lastName: formData.personal.lastName,\n                                    experience: formData.experience,\n                                    skills: formData.skills\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1241,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1240,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1219,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 1100,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1094,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PersonalInfoForm;\n// Education Form Component\nconst EducationForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem, atsAnalysis } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                className: \"h-6 w-6 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1268,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1269,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1267,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addArrayItem('education', {\n                                degree: \"\",\n                                institution: \"\",\n                                location: \"\",\n                                startDate: \"\",\n                                endDate: \"\",\n                                gpa: \"\",\n                                relevant: \"\"\n                            }),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1283,\n                                columnNumber: 9\n                            }, undefined),\n                            \"Add Education\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1271,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1266,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: formData.education.map((edu, index)=>{\n                    var _atsAnalysis_fieldAnalysis, _atsAnalysis_fieldAnalysis1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 p-6 rounded-lg border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Education \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1292,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeArrayItem('education', edu.id),\n                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 1298,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1294,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1291,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Degree *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1305,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.degree,\n                                                onChange: (e)=>updateFormData('education', 'degree', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"Bachelor of Science\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    fieldName: \"degree\",\n                                                    value: edu.degree,\n                                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis[\"education_degree_\".concat(index)]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1316,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1315,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1304,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Institution *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.institution,\n                                                onChange: (e)=>updateFormData('education', 'institution', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"University of Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1328,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    fieldName: \"institution\",\n                                                    value: edu.institution,\n                                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis1 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis1 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis1[\"education_institution_\".concat(index)]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1336,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1335,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1324,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Location\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1345,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.location,\n                                                onChange: (e)=>updateFormData('education', 'location', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"New York, NY\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1344,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"GPA (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1359,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.gpa,\n                                                onChange: (e)=>updateFormData('education', 'gpa', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"3.8/4.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1362,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1358,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_46__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1373,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Start Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1372,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.startDate,\n                                                onChange: (e)=>updateFormData('education', 'startDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1376,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1371,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_46__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"End Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1385,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.endDate,\n                                                onChange: (e)=>updateFormData('education', 'endDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1389,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Relevant Coursework/Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1398,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: edu.relevant,\n                                                onChange: (e)=>updateFormData('education', 'relevant', e.target.value, index),\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"Relevant coursework, honors, achievements...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1401,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1303,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, edu.id, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1290,\n                        columnNumber: 9\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1288,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1260,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EducationForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ResumeBuilder\");\n$RefreshReg$(_c1, \"PersonalInfoForm\");\n$RefreshReg$(_c2, \"EducationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FormHeader.jsx":
/*!***************************************!*\
  !*** ./src/components/FormHeader.jsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_HelpCircle_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,HelpCircle,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_HelpCircle_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,HelpCircle,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_HelpCircle_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,HelpCircle,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_HelpCircle_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,HelpCircle,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst FormHeader = (param)=>{\n    let { currentStep, totalSteps, steps = [], onBack, onPreview, onSave, showPreview = false, className = \"\" } = param;\n    const currentStepData = steps[currentStep];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            y: -50,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        className: \"sticky top-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-700 \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                            onClick: onBack,\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"flex items-center gap-2 px-3 py-2 text-gray-400 hover:text-white transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_HelpCircle_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden sm:inline\",\n                                    children: \"Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 text-center px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: \"Resume Builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400 mt-1\",\n                                children: [\n                                    currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.title,\n                                    \" • Step \",\n                                    currentStep + 1,\n                                    \" of \",\n                                    totalSteps\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"p-2 text-gray-400 hover:text-neural-purple transition-colors\",\n                                title: \"Help & Tips\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_HelpCircle_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: onPreview,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"\\n                flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\\n                \".concat(showPreview ? 'bg-neural-purple text-white' : 'text-gray-400 hover:text-white hover:bg-gray-800', \"\\n              \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_HelpCircle_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"Preview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: onSave,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"flex items-center gap-2 px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_HelpCircle_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\FormHeader.jsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FormHeader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormHeader);\nvar _c;\n$RefreshReg$(_c, \"FormHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FormHeader.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/StepIndicator.jsx":
/*!******************************************!*\
  !*** ./src/components/StepIndicator.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Briefcase,CheckCircle,Circle,FileText,GraduationCap,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Briefcase,CheckCircle,Circle,FileText,GraduationCap,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Briefcase,CheckCircle,Circle,FileText,GraduationCap,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Briefcase,CheckCircle,Circle,FileText,GraduationCap,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Briefcase,CheckCircle,Circle,FileText,GraduationCap,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Briefcase,CheckCircle,Circle,FileText,GraduationCap,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Briefcase,CheckCircle,Circle,FileText,GraduationCap,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst StepIndicator = (param)=>{\n    let { currentStep, totalSteps, steps, onStepClick, allowClickNavigation = true, className = \"\" } = param;\n    var _steps_currentStep, _steps_currentStep1, _steps_;\n    const getStepIcon = (stepIndex)=>{\n        const icons = [\n            _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        ];\n        return icons[stepIndex] || _barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n    };\n    const getStepStatus = (stepIndex)=>{\n        if (stepIndex < currentStep) return 'completed';\n        if (stepIndex === currentStep) return 'current';\n        return 'upcoming';\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'bg-green-500 border-green-500 text-white';\n            case 'current':\n                return 'bg-neural-purple border-neural-purple text-white';\n            case 'upcoming':\n                return 'bg-gray-700 border-gray-600 text-gray-400';\n            default:\n                return 'bg-gray-700 border-gray-600 text-gray-400';\n        }\n    };\n    const getConnectorColor = (stepIndex)=>{\n        return stepIndex < currentStep ? 'bg-green-500' : 'bg-gray-600';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"block md:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-gray-300\",\n                                children: [\n                                    \"Step \",\n                                    currentStep + 1,\n                                    \" of \",\n                                    totalSteps\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    Math.round((currentStep + 1) / totalSteps * 100),\n                                    \"% Complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            className: \"bg-neural-purple h-2 rounded-full\",\n                            initial: {\n                                width: 0\n                            },\n                            animate: {\n                                width: \"\".concat((currentStep + 1) / totalSteps * 100, \"%\")\n                            },\n                            transition: {\n                                duration: 0.5\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white\",\n                                children: (_steps_currentStep = steps[currentStep]) === null || _steps_currentStep === void 0 ? void 0 : _steps_currentStep.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400 mt-1\",\n                                children: (_steps_currentStep1 = steps[currentStep]) === null || _steps_currentStep1 === void 0 ? void 0 : _steps_currentStep1.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: steps.map((step, index)=>{\n                            const Icon = getStepIcon(index);\n                            const status = getStepStatus(index);\n                            const isClickable = allowClickNavigation && (index <= currentStep || status === 'completed');\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                onClick: ()=>isClickable && onStepClick && onStepClick(index),\n                                                disabled: !isClickable,\n                                                className: \"\\n                      relative z-10 flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300\\n                      \".concat(getStepColor(status), \"\\n                      \").concat(isClickable ? 'cursor-pointer hover:scale-110' : 'cursor-not-allowed', \"\\n                    \"),\n                                                whileHover: isClickable ? {\n                                                    scale: 1.1\n                                                } : {},\n                                                whileTap: isClickable ? {\n                                                    scale: 0.95\n                                                } : {},\n                                                children: status === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Briefcase_CheckCircle_Circle_FileText_GraduationCap_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-12 left-1/2 transform -translate-x-1/2 text-center min-w-max\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(status === 'current' ? 'text-neural-purple' : status === 'completed' ? 'text-green-400' : 'text-gray-400'),\n                                                        children: step.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: step.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 mx-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-0.5 bg-gray-600 w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    className: \"h-0.5 absolute top-0 left-0 \".concat(getConnectorColor(index)),\n                                                    initial: {\n                                                        width: 0\n                                                    },\n                                                    animate: {\n                                                        width: index < currentStep ? '100%' : '0%'\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: index * 0.1\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"Progress: \",\n                                    currentStep + 1,\n                                    \" of \",\n                                    totalSteps,\n                                    \" steps completed\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: currentStep < totalSteps - 1 ? \"Next: \".concat((_steps_ = steps[currentStep + 1]) === null || _steps_ === void 0 ? void 0 : _steps_.title) : 'Ready to generate your resume!'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StepIndicator.jsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StepIndicator;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StepIndicator);\nvar _c;\n$RefreshReg$(_c, \"StepIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/StepIndicator.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/StickyNavigation.jsx":
/*!*********************************************!*\
  !*** ./src/components/StickyNavigation.jsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,ChevronLeft,ChevronRight,Save,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,ChevronLeft,ChevronRight,Save,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,ChevronLeft,ChevronRight,Save,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,ChevronLeft,ChevronRight,Save,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst StickyNavigation = (param)=>{\n    let { currentStep, totalSteps, onPrevious, onNext, onGenerate, isGenerating = false, canProceed = true, steps = [], className = \"\" } = param;\n    const isFirstStep = currentStep === 0;\n    const isLastStep = currentStep === totalSteps - 1;\n    const currentStepData = steps[currentStep];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            y: 100,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        className: \"fixed bottom-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-sm border-t border-gray-700 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: !isFirstStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: onPrevious,\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg transition-all duration-200 text-gray-300 hover:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"Previous\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                lineNumber: 39,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, undefined) // Spacer for alignment\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 text-center px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.title) || \"Step \".concat(currentStep + 1)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        currentStep + 1,\n                                        \" of \",\n                                        totalSteps,\n                                        \" • \",\n                                        Math.round((currentStep + 1) / totalSteps * 100),\n                                        \"% Complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: !isLastStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: onNext,\n                                disabled: !canProceed,\n                                whileHover: canProceed ? {\n                                    scale: 1.02\n                                } : {},\n                                whileTap: canProceed ? {\n                                    scale: 0.98\n                                } : {},\n                                className: \"\\n                  flex items-center gap-2 px-6 py-2 rounded-lg transition-all duration-200 font-medium\\n                  \".concat(canProceed ? 'bg-neural-purple hover:bg-neural-purple/80 text-white' : 'bg-gray-700 text-gray-400 cursor-not-allowed', \"\\n                \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Next\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: onGenerate,\n                                disabled: isGenerating || !canProceed,\n                                whileHover: !isGenerating && canProceed ? {\n                                    scale: 1.02\n                                } : {},\n                                whileTap: !isGenerating && canProceed ? {\n                                    scale: 0.98\n                                } : {},\n                                className: \"\\n                  flex items-center gap-2 px-6 py-2 rounded-lg transition-all duration-200 font-medium\\n                  \".concat(!isGenerating && canProceed ? 'bg-gradient-to-r from-neural-purple to-neural-pink hover:from-neural-purple/80 hover:to-neural-pink/80 text-white' : 'bg-gray-700 text-gray-400 cursor-not-allowed', \"\\n                \"),\n                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            animate: {\n                                                rotate: 360\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                repeat: Infinity,\n                                                ease: \"linear\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Generating...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_ChevronLeft_ChevronRight_Save_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Generate Resume\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-800 h-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"bg-gradient-to-r from-neural-purple to-neural-pink h-1\",\n                    initial: {\n                        width: 0\n                    },\n                    animate: {\n                        width: \"\".concat((currentStep + 1) / totalSteps * 100, \"%\")\n                    },\n                    transition: {\n                        duration: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\StickyNavigation.jsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StickyNavigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StickyNavigation);\nvar _c;\n$RefreshReg$(_c, \"StickyNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/StickyNavigation.jsx\n"));

/***/ })

});