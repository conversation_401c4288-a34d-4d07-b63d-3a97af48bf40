"use client";
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>R<PERSON>, 
  FileText, 
  Sparkles, 
  CheckCircle,
  TrendingUp,
  Zap,
  Eye,
  EyeOff
} from 'lucide-react';
import ATSScoreCircle from './ATSScoreCircle';

const BeforeAfterComparison = ({ originalData, enhancedData, analysisData }) => {
  const [showComparison, setShowComparison] = useState(false);
  const [activeTab, setActiveTab] = useState('summary');

  if (!originalData || !enhancedData) {
    return null;
  }

  const tabs = [
    { id: 'summary', label: 'Summary', icon: FileText },
    { id: 'experience', label: 'Experience', icon: TrendingUp },
    { id: 'skills', label: 'Skills', icon: Zap },
  ];

  const renderBeforeAfterContent = () => {
    switch (activeTab) {
      case 'summary':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Before */}
            <div className="bg-red-900/20 rounded-xl p-6 border border-red-500/30">
              <h4 className="text-red-400 font-semibold mb-4 flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Original Summary
              </h4>
              <div className="text-gray-300 text-sm leading-relaxed">
                {originalData.personal?.summary || 'No summary provided'}
              </div>
            </div>

            {/* After */}
            <div className="bg-green-900/20 rounded-xl p-6 border border-green-500/30">
              <h4 className="text-green-400 font-semibold mb-4 flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Enhanced Summary
              </h4>
              <div className="text-gray-300 text-sm leading-relaxed">
                {enhancedData.personal?.summary || 'Enhanced summary not available'}
              </div>
            </div>
          </div>
        );

      case 'experience':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Before */}
            <div className="bg-red-900/20 rounded-xl p-6 border border-red-500/30">
              <h4 className="text-red-400 font-semibold mb-4 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Original Experience
              </h4>
              <div className="space-y-4">
                {originalData.experience?.slice(0, 2).map((exp, index) => (
                  <div key={index} className="border-l-2 border-red-500/30 pl-4">
                    <h5 className="font-medium text-white">{exp.title || 'Position'}</h5>
                    <p className="text-gray-400 text-sm">{exp.company || 'Company'}</p>
                    <p className="text-gray-300 text-xs mt-2">{exp.description || 'No description'}</p>
                  </div>
                )) || <p className="text-gray-400">No experience data</p>}
              </div>
            </div>

            {/* After */}
            <div className="bg-green-900/20 rounded-xl p-6 border border-green-500/30">
              <h4 className="text-green-400 font-semibold mb-4 flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Enhanced Experience
              </h4>
              <div className="space-y-4">
                {enhancedData.experience?.slice(0, 2).map((exp, index) => (
                  <div key={index} className="border-l-2 border-green-500/30 pl-4">
                    <h5 className="font-medium text-white">{exp.title || 'Position'}</h5>
                    <p className="text-gray-400 text-sm">{exp.company || 'Company'}</p>
                    <p className="text-gray-300 text-xs mt-2">{exp.description || 'Enhanced description'}</p>
                  </div>
                )) || <p className="text-gray-400">Enhanced experience not available</p>}
              </div>
            </div>
          </div>
        );

      case 'skills':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Before */}
            <div className="bg-red-900/20 rounded-xl p-6 border border-red-500/30">
              <h4 className="text-red-400 font-semibold mb-4 flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Original Skills
              </h4>
              <div className="space-y-3">
                <div>
                  <p className="text-gray-400 text-sm mb-2">Technical:</p>
                  <div className="flex flex-wrap gap-2">
                    {originalData.skills?.technical?.slice(0, 5).map((skill, index) => (
                      <span key={index} className="px-2 py-1 bg-red-800/30 text-red-300 text-xs rounded">
                        {skill}
                      </span>
                    )) || <span className="text-gray-500 text-xs">No skills listed</span>}
                  </div>
                </div>
              </div>
            </div>

            {/* After */}
            <div className="bg-green-900/20 rounded-xl p-6 border border-green-500/30">
              <h4 className="text-green-400 font-semibold mb-4 flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Enhanced Skills
              </h4>
              <div className="space-y-3">
                <div>
                  <p className="text-gray-400 text-sm mb-2">Technical:</p>
                  <div className="flex flex-wrap gap-2">
                    {enhancedData.skills?.technical?.slice(0, 5).map((skill, index) => (
                      <span key={index} className="px-2 py-1 bg-green-800/30 text-green-300 text-xs rounded">
                        {skill}
                      </span>
                    )) || <span className="text-gray-500 text-xs">Enhanced skills not available</span>}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-neural-blue to-neural-purple rounded-full flex items-center justify-center">
            <ArrowRight className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">Before vs After Comparison</h3>
            <p className="text-gray-400 text-sm">See how AI enhanced your resume</p>
          </div>
        </div>
        
        <motion.button
          onClick={() => setShowComparison(!showComparison)}
          className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 text-neural-purple hover:text-white rounded-lg transition-all duration-300"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {showComparison ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          {showComparison ? 'Hide' : 'Show'} Comparison
        </motion.button>
      </div>

      {/* ATS Score Improvement */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-900/50 rounded-lg p-4 text-center">
          <h4 className="text-gray-400 text-sm mb-2">Original Score</h4>
          <div className="flex justify-center">
            <ATSScoreCircle score={65} size={80} />
          </div>
        </div>
        
        <div className="flex items-center justify-center">
          <ArrowRight className="h-8 w-8 text-neural-purple" />
        </div>
        
        <div className="bg-gray-900/50 rounded-lg p-4 text-center">
          <h4 className="text-gray-400 text-sm mb-2">Enhanced Score</h4>
          <div className="flex justify-center">
            <ATSScoreCircle score={analysisData?.atsScore?.overall || 85} size={80} />
          </div>
        </div>
      </div>

      {/* Improvements Summary */}
      <div className="bg-neural-purple/10 rounded-lg p-4 border border-neural-purple/20 mb-6">
        <h4 className="text-neural-purple font-semibold mb-3 flex items-center gap-2">
          <CheckCircle className="h-4 w-4" />
          Key Improvements Made
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {[
            'Enhanced professional summary',
            'Quantified achievements added',
            'Industry keywords optimized',
            'ATS-friendly formatting applied',
            'Skills section reorganized',
            'Action verbs strengthened'
          ].map((improvement, index) => (
            <div key={index} className="flex items-center gap-2 text-sm text-gray-300">
              <div className="w-1.5 h-1.5 bg-neural-purple rounded-full" />
              {improvement}
            </div>
          ))}
        </div>
      </div>

      {/* Detailed Comparison */}
      {showComparison && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="space-y-6"
        >
          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-gray-900/50 rounded-lg p-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-neural-purple text-white'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Tab Content */}
          <div className="min-h-[300px]">
            {renderBeforeAfterContent()}
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default BeforeAfterComparison;
