'use client'
import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { Command, Sparkles, Menu, X } from 'lucide-react'
import toast from 'react-hot-toast'

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [role, setRole] = useState(null)
  const pathname = usePathname()
  const router = useRouter()

  const links = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/aboutus' },
    { name: 'Resume Builder', href: '/resume-builder' },
  ]

  // Check if the user is logged in and set the role on initial render
  useEffect(() => {
    const token = localStorage.getItem('token')
    const storedRole = localStorage.getItem('role')
    
    if (token) {
      setIsLoggedIn(true)
    } else {
      setIsLoggedIn(false)
    }
    setRole(storedRole)
  }, [])

  const handleLoginLogout = useCallback(() => {
    if (isLoggedIn) {
      // User is logged in, perform logout
      localStorage.removeItem('token')
      localStorage.removeItem('role')
      setIsLoggedIn(false)
      toast.success('Logged out successfully')
      router.push('/')
      setIsOpen(false)
    } else {
      // User is not logged in, redirect to login
      router.push('/login')
      setIsOpen(false)
    }
  }, [isLoggedIn, router])

  const handleNavItemClick = (href) => {
    if (href.startsWith('#')) {
      if (pathname !== '/') {
        router.push(`/${href}`)
      }
    }
    setIsOpen(false)
  }

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 768) {
        setIsOpen(false)
      }
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <header className="fixed inset-x-0 top-0 z-50 backdrop-blur-md bg-black/50 border-b border-white/10">
      <nav className="flex items-center justify-between p-6 lg:px-8" aria-label="Global">
        <div className="flex lg:flex-1">
          <Link href="/" className="-m-1.5 p-1.5 flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-neural-purple" />
            <span className="font-mono font-bold text-white">BlinkFind<span className="text-neural-purple">AI</span></span>
          </Link>
        </div>
        
        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-400"
            onClick={() => setIsOpen(true)}
          >
            <span className="sr-only">Open main menu</span>
            <Menu className="h-6 w-6" aria-hidden="true" />
          </button>
        </div>
        
        <div className="hidden lg:flex lg:gap-x-12">
          {links.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              onClick={() => handleNavItemClick(item.href)}
              className={`text-sm font-medium leading-6 ${
                pathname === item.href ? 'text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              {item.name}
            </Link>
          ))}
          {role === 'admin' && (
            <Link
              href="/admin/contact-forms"
              className="text-sm font-medium leading-6 text-gray-400 hover:text-white"
            >
              Admin
            </Link>
          )}
        </div>
        
        <div className="hidden lg:flex lg:flex-1 lg:justify-end lg:items-center lg:gap-4">
         {/*<button className="flex items-center gap-2 text-sm font-medium leading-6 text-gray-400 hover:text-white">
            <Command className="h-4 w-4" />
            <span>⌘K</span>
          </button>*/} 
          {/* <button
            onClick={handleLoginLogout}
            className="text-sm font-medium leading-6 text-white"
          >
            {isLoggedIn ? 'Logout' : 'Login'} <span aria-hidden="true">&rarr;</span>
          </button> */}
        </div>
      </nav>

      {/* Mobile menu */}
      <div className={`lg:hidden fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-gray-900/95 px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10 transform ${isOpen ? 'translate-x-0' : 'translate-x-full'} transition-transform duration-300 ease-in-out`}>
        <div className="flex items-center justify-between">
          <Link href="/" className="-m-1.5 p-1.5 flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-neural-purple" />
            <span className="font-mono font-bold text-white">BlinkFind<span className="text-neural-purple">AI</span></span>
          </Link>
          <button
            type="button"
            className="-m-2.5 rounded-md p-2.5 text-gray-400"
            onClick={() => setIsOpen(false)}
          >
            <span className="sr-only">Close menu</span>
            <X className="h-6 w-6" aria-hidden="true" />
          </button>
        </div>
        {/* <div className="mt-6 flow-root">
          <div className="-my-6 divide-y divide-gray-500/10">
            <div className="space-y-2 py-6">
              {links.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => handleNavItemClick(item.href)}
                  className={`-mx-3 block rounded-lg px-3 py-2 text-base font-medium leading-7 ${
                    pathname === item.href ? 'text-white bg-gray-800' : 'text-gray-400 hover:bg-gray-800 hover:text-white'
                  }`}
                >
                  {item.name}
                </Link>
              ))}
              {role === 'admin' && (
                <Link
                  href="/admin/contact-forms"
                  className="-mx-3 block rounded-lg px-3 py-2 text-base font-medium leading-7 text-gray-400 hover:bg-gray-800 hover:text-white"
                >
                  Admin
                </Link>
              )}
            </div>
            <div className="py-6">
              <button
                onClick={handleLoginLogout}
                className="-mx-3 block rounded-lg px-3 py-2.5 text-base font-medium leading-7 text-white hover:bg-gray-800 w-full text-left"
              >
                {isLoggedIn ? 'Logout' : 'Login'}
              </button>
            </div>
          </div>
        </div> */}
      </div>
    </header>
  )
}

export default Navbar