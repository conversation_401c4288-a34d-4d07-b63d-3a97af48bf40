"use client";
import React, { useState } from 'react';
import { Download, FileText, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';

const PDFDownload = ({ formData, resumeData, templateId = 'modern', className = "" }) => {
  const [isGenerating, setIsGenerating] = useState(false);

  const generatePDF = async () => {
    try {
      setIsGenerating(true);
      
      // Get the resume HTML from the API
      const response = await fetch(`/api/download-resume/${Date.now()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formData,
          resumeData,
          templateId
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error('Failed to generate resume HTML');
      }

      // Create a new window with the resume HTML
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      // Write the HTML to the new window
      printWindow.document.write(data.html);
      printWindow.document.close();

      // Wait for content to load, then trigger print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

    } catch (error) {
      console.error('PDF generation error:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <motion.button
      onClick={generatePDF}
      disabled={isGenerating}
      className={`inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 ${className}`}
      whileHover={!isGenerating ? { scale: 1.02 } : {}}
      whileTap={!isGenerating ? { scale: 0.98 } : {}}
    >
      {isGenerating ? (
        <>
          <Loader2 className="h-5 w-5 animate-spin" />
          Generating PDF...
        </>
      ) : (
        <>
          <Download className="h-5 w-5" />
          Download PDF Resume
        </>
      )}
    </motion.button>
  );
};

// Alternative component for viewing resume in new tab
export const ViewResumeButton = ({ formData, resumeData, templateId = 'modern', className = "" }) => {
  const [isLoading, setIsLoading] = useState(false);

  const viewResume = async () => {
    try {
      setIsLoading(true);
      
      // Get the resume HTML from the API
      const response = await fetch(`/api/download-resume/${Date.now()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formData,
          resumeData,
          templateId
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error('Failed to generate resume HTML');
      }

      // Create a new window with the resume HTML
      const viewWindow = window.open('', '_blank');
      if (!viewWindow) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      // Write the HTML to the new window
      viewWindow.document.write(data.html);
      viewWindow.document.close();

    } catch (error) {
      console.error('Resume view error:', error);
      alert('Failed to open resume. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.button
      onClick={viewResume}
      disabled={isLoading}
      className={`inline-flex items-center gap-2 px-8 py-4 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 text-neural-blue hover:text-white font-medium rounded-xl transition-all duration-300 disabled:opacity-50 ${className}`}
      whileHover={!isLoading ? { scale: 1.02 } : {}}
      whileTap={!isLoading ? { scale: 0.98 } : {}}
    >
      {isLoading ? (
        <>
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading...
        </>
      ) : (
        <>
          <FileText className="h-5 w-5" />
          View Full Resume
        </>
      )}
    </motion.button>
  );
};

export default PDFDownload;
