"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/ATSAnalysisDisplay.jsx":
/*!***********************************************!*\
  !*** ./src/components/ATSAnalysisDisplay.jsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,CheckCircle,FileText,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,CheckCircle,FileText,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,CheckCircle,FileText,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,CheckCircle,FileText,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,CheckCircle,FileText,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,CheckCircle,FileText,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,CheckCircle,FileText,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,CheckCircle,FileText,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _ATSScoreCircle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ATSScoreCircle */ \"(app-pages-browser)/./src/components/ATSScoreCircle.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst ATSAnalysisDisplay = (param)=>{\n    let { analysisData, analysisType = 'full' } = param;\n    const { atsScore, analysis, keywordAnalysis, enhancements, fallback } = analysisData;\n    const getScoreColor = (score)=>{\n        if (score >= 81) return 'text-green-400';\n        if (score >= 61) return 'text-yellow-400';\n        return 'text-red-400';\n    };\n    const getScoreIcon = (score)=>{\n        if (score >= 81) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-5 w-5 text-green-400\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n            lineNumber: 26,\n            columnNumber: 29\n        }, undefined);\n        if (score >= 61) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-5 w-5 text-yellow-400\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n            lineNumber: 27,\n            columnNumber: 29\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5 text-red-400\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n            lineNumber: 28,\n            columnNumber: 12\n        }, undefined);\n    };\n    const ScoreBreakdownCard = (param)=>{\n        let { title, score, icon: Icon } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            className: \"bg-gray-800/50 backdrop-blur-md rounded-xl p-4 border border-gray-700\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                            lineNumber: 38,\n                            columnNumber: 9\n                        }, undefined),\n                        getScoreIcon(score)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                    lineNumber: 37,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-2xl font-bold \".concat(getScoreColor(score)),\n                            children: [\n                                score,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                            lineNumber: 45,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-2 rounded-full \".concat(score >= 81 ? 'bg-green-400' : score >= 61 ? 'bg-yellow-400' : 'bg-red-400'),\n                                style: {\n                                    width: \"\".concat(score, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                            lineNumber: 48,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                    lineNumber: 44,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n            lineNumber: 32,\n            columnNumber: 5\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            fallback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"bg-yellow-900/20 backdrop-blur-md rounded-xl p-4 border border-yellow-500/30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-yellow-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold\",\n                                children: \"Manual Review Required\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-300 text-sm mt-2\",\n                        children: \"Your file was uploaded successfully, but automatic text extraction was limited. Please review and edit the form manually for the best results.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.9\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6\",\n                        children: analysisType === 'quick' ? 'Quick ATS Analysis' : 'Comprehensive Resume Analysis'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        score: (atsScore === null || atsScore === void 0 ? void 0 : atsScore.overall) || 0,\n                        size: 160\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            (atsScore === null || atsScore === void 0 ? void 0 : atsScore.breakdown) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 0.2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-4 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Score Breakdown\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreBreakdownCard, {\n                                title: \"Keywords\",\n                                score: atsScore.breakdown.keywords,\n                                icon: _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreBreakdownCard, {\n                                title: \"Formatting\",\n                                score: atsScore.breakdown.formatting,\n                                icon: _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreBreakdownCard, {\n                                title: \"Structure\",\n                                score: atsScore.breakdown.structure,\n                                icon: _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, undefined),\n                            atsScore.breakdown.achievements && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreBreakdownCard, {\n                                title: \"Achievements\",\n                                score: atsScore.breakdown.achievements,\n                                icon: _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, undefined),\n                            atsScore.breakdown.skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreBreakdownCard, {\n                                title: \"Skills\",\n                                score: atsScore.breakdown.skills,\n                                icon: _barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, undefined),\n            analysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    analysis.strengths && analysis.strengths.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"bg-green-900/20 backdrop-blur-md rounded-xl p-6 border border-green-500/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-green-400 mb-4 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Strengths\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: analysis.strengths.map((strength, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-start gap-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            strength\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, undefined),\n                    analysis.weaknesses && analysis.weaknesses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        className: \"bg-red-900/20 backdrop-blur-md rounded-xl p-6 border border-red-500/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-red-400 mb-4 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Areas for Improvement\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: analysis.weaknesses.map((weakness, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-start gap-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            weakness\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, undefined),\n            ((analysis === null || analysis === void 0 ? void 0 : analysis.recommendations) || (enhancements === null || enhancements === void 0 ? void 0 : enhancements.suggestions)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 0.5\n                },\n                className: \"bg-neural-purple/10 backdrop-blur-md rounded-xl p-6 border border-neural-purple/30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-neural-purple mb-4 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined),\n                            \"AI Recommendations\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: ((analysis === null || analysis === void 0 ? void 0 : analysis.recommendations) || (enhancements === null || enhancements === void 0 ? void 0 : enhancements.suggestions) || []).map((recommendation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.6 + index * 0.1\n                                },\n                                className: \"flex items-start gap-3 p-3 bg-neural-purple/5 rounded-lg border border-neural-purple/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-neural-purple/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-bold text-neural-purple\",\n                                            children: index + 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 text-sm leading-relaxed\",\n                                        children: recommendation\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, undefined),\n            keywordAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 0.6\n                },\n                className: \"bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-white mb-4 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_CheckCircle_FileText_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-5 w-5 text-neural-blue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Keyword Analysis\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            keywordAnalysis.found && keywordAnalysis.found.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-semibold text-green-400 mb-2\",\n                                        children: \"Found Keywords\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: keywordAnalysis.found.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-green-900/30 text-green-300 rounded-md text-xs border border-green-500/30\",\n                                                children: keyword\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, undefined),\n                            keywordAnalysis.missing && keywordAnalysis.missing.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-semibold text-red-400 mb-2\",\n                                        children: \"Missing Keywords\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: keywordAnalysis.missing.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-red-900/30 text-red-300 rounded-md text-xs border border-red-500/30\",\n                                                children: keyword\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, undefined),\n                    keywordAnalysis.suggestions && keywordAnalysis.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"font-semibold text-neural-blue mb-2\",\n                                children: \"Keyword Suggestions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: keywordAnalysis.suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-sm text-gray-300 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-neural-blue rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            suggestion\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                        lineNumber: 267,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n                lineNumber: 221,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\components\\\\ATSAnalysisDisplay.jsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ATSAnalysisDisplay;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ATSAnalysisDisplay);\nvar _c;\n$RefreshReg$(_c, \"ATSAnalysisDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ATSAnalysisDisplay.jsx\n"));

/***/ })

});