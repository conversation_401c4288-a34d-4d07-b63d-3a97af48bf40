/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-suggestions/route";
exports.ids = ["app/api/generate-suggestions/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-suggestions%2Froute&page=%2Fapi%2Fgenerate-suggestions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-suggestions%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-suggestions%2Froute&page=%2Fapi%2Fgenerate-suggestions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-suggestions%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_suggestions_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-suggestions/route.js */ \"(rsc)/./src/app/api/generate-suggestions/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-suggestions/route\",\n        pathname: \"/api/generate-suggestions\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-suggestions/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\api\\\\generate-suggestions\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_BlinkFind_BlinkFind_Web_frontend_src_app_api_generate_suggestions_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZnZW5lcmF0ZS1zdWdnZXN0aW9ucyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGZ2VuZXJhdGUtc3VnZ2VzdGlvbnMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZnZW5lcmF0ZS1zdWdnZXN0aW9ucyUyRnJvdXRlLmpzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNCbGlua0ZpbmQlNUNCbGlua0ZpbmQtV2ViJTVDZnJvbnRlbmQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9anMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPXRzeCZyb290RGlyPUMlM0ElNUNQcm9qZWN0cyU1Q0JsaW5rRmluZCU1Q0JsaW5rRmluZC1XZWIlNUNmcm9udGVuZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDK0M7QUFDNUg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFByb2plY3RzXFxcXEJsaW5rRmluZFxcXFxCbGlua0ZpbmQtV2ViXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGdlbmVyYXRlLXN1Z2dlc3Rpb25zXFxcXHJvdXRlLmpzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9nZW5lcmF0ZS1zdWdnZXN0aW9ucy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2dlbmVyYXRlLXN1Z2dlc3Rpb25zXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9nZW5lcmF0ZS1zdWdnZXN0aW9ucy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFByb2plY3RzXFxcXEJsaW5rRmluZFxcXFxCbGlua0ZpbmQtV2ViXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGdlbmVyYXRlLXN1Z2dlc3Rpb25zXFxcXHJvdXRlLmpzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-suggestions%2Froute&page=%2Fapi%2Fgenerate-suggestions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-suggestions%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-suggestions/route.js":
/*!***************************************************!*\
  !*** ./src/app/api/generate-suggestions/route.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    try {\n        console.log('🎯 AI content suggestions API called');\n        const { fieldType, currentValue, context } = await request.json();\n        if (!fieldType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Field type is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📊 Generating suggestions for:', fieldType);\n        console.log('🔍 Context:', context);\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback suggestions');\n            return createFallbackSuggestions(fieldType, currentValue, context);\n        }\n        try {\n            // Get the generative model - Using Gemini 2.0 Flash\n            const model = genAI.getGenerativeModel({\n                model: 'gemini-2.0-flash-exp',\n                generationConfig: {\n                    temperature: 0.7,\n                    topK: 40,\n                    topP: 0.95,\n                    maxOutputTokens: 2000\n                }\n            });\n            const prompt = createSuggestionPrompt(fieldType, currentValue, context);\n            console.log('🤖 Calling Gemini AI for content suggestions...');\n            const result = await model.generateContent(prompt);\n            const response = await result.response;\n            const generatedContent = response.text();\n            console.log('✅ Gemini suggestions completed, length:', generatedContent?.length || 0);\n            // Parse the AI response\n            const suggestions = parseSuggestionsResponse(generatedContent, fieldType);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                suggestions,\n                fieldType,\n                processedAt: new Date().toISOString()\n            });\n        } catch (aiError) {\n            console.error('AI suggestions error:', aiError);\n            console.log('⚠️ AI suggestions failed, using fallback suggestions');\n            return createFallbackSuggestions(fieldType, currentValue, context);\n        }\n    } catch (error) {\n        console.error('💥 Content suggestions error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate content suggestions',\n            details:  true ? error.message : 0\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createSuggestionPrompt(fieldType, currentValue, context) {\n    const basePrompt = `You are an expert resume writer and career coach. Generate 3 high-quality, professional suggestions for the following resume field.\n\nFIELD TYPE: ${fieldType}\nCURRENT VALUE: ${currentValue || 'Not provided'}\nCONTEXT: ${JSON.stringify(context)}\n\nRequirements:\n- Make suggestions professional and ATS-friendly\n- Use action verbs and quantifiable achievements where appropriate\n- Tailor suggestions to the field type and context\n- Keep suggestions concise but impactful\n- Use industry-standard terminology\n\n`;\n    switch(fieldType){\n        case 'summary':\n            return basePrompt + `\nGenerate 3 professional summary suggestions that:\n- Are 2-3 sentences long\n- Highlight key skills and experience\n- Include quantifiable achievements when possible\n- Use strong action words\n- Are tailored to the person's background\n\nFormat: Return only the 3 suggestions, one per line, without numbering or bullets.`;\n        case 'experience':\n            return basePrompt + `\nGenerate 3 professional experience bullet points that:\n- Start with strong action verbs\n- Include quantifiable results and metrics\n- Highlight technical skills and achievements\n- Are specific and impactful\n- Follow the format: \"• [Action verb] + [what you did] + [quantified result]\"\n\nFormat: Return only the 3 bullet points, one per line, each starting with \"•\".`;\n        case 'skills':\n            return basePrompt + `\nGenerate 3 different skill combinations that:\n- Include relevant technical skills for the field\n- Mix programming languages, frameworks, and tools\n- Are appropriate for the experience level\n- Include modern, in-demand technologies\n- Are formatted as comma-separated lists\n\nFormat: Return only the 3 skill lists, one per line, comma-separated.`;\n        case 'projects':\n            return basePrompt + `\nGenerate 3 project descriptions that:\n- Describe realistic, impressive projects\n- Include technical details and technologies used\n- Mention the impact or results achieved\n- Are relevant to the person's field\n- Are 1-2 sentences long\n\nFormat: Return only the 3 project descriptions, one per line.`;\n        default:\n            return basePrompt + `\nGenerate 3 professional suggestions for this field that are:\n- Relevant and industry-appropriate\n- Well-written and professional\n- Specific and actionable\n- ATS-friendly\n\nFormat: Return only the 3 suggestions, one per line.`;\n    }\n}\nfunction parseSuggestionsResponse(generatedContent, fieldType) {\n    try {\n        // Split the response into lines and clean them up\n        const lines = generatedContent.split('\\n').map((line)=>line.trim()).filter((line)=>line.length > 0).filter((line)=>!line.match(/^(Here are|Suggestion|Option|\\d+\\.|\\*)/i)) // Remove headers\n        .slice(0, 3); // Take only first 3 suggestions\n        if (lines.length > 0) {\n            return lines;\n        }\n    } catch (error) {\n        console.error('Error parsing suggestions response:', error);\n    }\n    // Fallback suggestions\n    return getFallbackSuggestionsByType(fieldType);\n}\nfunction getFallbackSuggestionsByType(fieldType) {\n    switch(fieldType){\n        case 'summary':\n            return [\n                \"Experienced professional with strong background in technology and proven track record of delivering high-quality solutions.\",\n                \"Results-driven specialist with expertise in modern development practices and commitment to continuous learning.\",\n                \"Dedicated professional with comprehensive experience in software development and team collaboration.\"\n            ];\n        case 'experience':\n            return [\n                \"• Developed and maintained web applications using modern frameworks, improving user experience by 40%\",\n                \"• Collaborated with cross-functional teams to deliver software solutions, reducing project timeline by 25%\",\n                \"• Implemented best practices for code quality and testing, increasing system reliability by 30%\"\n            ];\n        case 'skills':\n            return [\n                \"JavaScript, React, Node.js, Python, MongoDB, Git\",\n                \"HTML5, CSS3, TypeScript, PostgreSQL, Docker, AWS\",\n                \"Java, Spring Boot, MySQL, Jenkins, Kubernetes, Agile\"\n            ];\n        case 'projects':\n            return [\n                \"Developed a full-stack e-commerce platform with React and Node.js, supporting 1000+ concurrent users\",\n                \"Built a real-time chat application using WebSocket technology, achieving 99.9% uptime\",\n                \"Created a data visualization dashboard with D3.js, processing 10M+ data points efficiently\"\n            ];\n        default:\n            return [\n                \"Professional suggestion for your resume field\",\n                \"Industry-standard content recommendation\",\n                \"ATS-optimized professional content\"\n            ];\n    }\n}\nfunction createFallbackSuggestions(fieldType, currentValue, context) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        suggestions: getFallbackSuggestionsByType(fieldType),\n        fieldType,\n        fallback: true,\n        processedAt: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-suggestions/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-suggestions%2Froute&page=%2Fapi%2Fgenerate-suggestions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-suggestions%2Froute.js&appDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CBlinkFind%5CBlinkFind-Web%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();