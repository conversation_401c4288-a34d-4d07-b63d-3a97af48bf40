import { GoogleGenerativeAI } from '@google/generative-ai';
import { NextResponse } from 'next/server';

// Initialize Gemini AI with API key
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export async function POST(request) {
  let formData = {};
  try {
    console.log('🚀 Resume generation API called');
    formData = await request.json();
    console.log('📝 Form data received:', JSON.stringify(formData, null, 2));

    // Validate required fields
    if (!formData.personal?.firstName || !formData.personal?.lastName || !formData.personal?.email) {
      console.error('❌ Missing required fields');
      return NextResponse.json(
        { error: 'Missing required personal information (First Name, Last Name, and Email are required)' },
        { status: 400 }
      );
    }

    // Check if API key is configured
    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {
      console.log('⚠️ Gemini API key not configured, using fallback');
      // Fallback: return a basic resume structure
      const fallbackResume = createFallbackResume(formData);
      return NextResponse.json({
        success: true,
        resumeData: fallbackResume,
        atsScore: fallbackResume.atsScore?.overall || 75,
        suggestions: fallbackResume.atsScore?.improvements || [],
        downloadUrl: '/api/download-resume/' + Date.now(),
        message: 'Resume generated successfully (using template)',
        fallback: true
      });
    }

    console.log('🤖 Initializing Gemini AI...');

    // Get the generative model - Using Gemini 2.0 Flash (latest and most efficient)
    const model = genAI.getGenerativeModel({
      model: 'gemini-2.0-flash-exp',
      generationConfig: {
        temperature: 0.3,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8000,
      }
    });

    // Prepare prompt for Gemini API
    const prompt = createResumePrompt(formData);
    console.log('📋 Prompt created, length:', prompt.length);

    // Generate content using the official SDK
    console.log('🔄 Calling Gemini API...');
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const generatedContent = response.text();
    console.log('✅ Gemini response received, length:', generatedContent?.length || 0);

    if (!generatedContent) {
      throw new Error('No content generated from Gemini API');
    }

    // Process the generated content and create resume
    console.log('🔧 Processing generated content...');
    const resumeData = processGeneratedContent(generatedContent, formData);
    console.log('📊 Resume data processed:', {
      hasEnhancedContent: !!resumeData.enhancedContent,
      atsScore: resumeData.atsScore?.overall,
      suggestionsCount: resumeData.atsScore?.improvements?.length || 0
    });

    // Return the processed data with enhanced structure
    const responseData = {
      success: true,
      resumeData,
      atsScore: resumeData.atsScore?.overall || 75,
      suggestions: resumeData.atsScore?.improvements || [],
      downloadUrl: '/api/download-resume/' + Date.now(),
      message: 'Resume generated successfully with AI optimization',
      generatedAt: new Date().toISOString()
    };

    console.log('✨ Sending successful response');
    return NextResponse.json(responseData);

  } catch (error) {
    console.error('💥 Resume generation error:', error);
    console.error('Error stack:', error.stack);

    // Enhanced error handling with fallback
    try {
      const fallbackResume = createFallbackResume(formData || {});
      const fallbackResponse = {
        success: true,
        resumeData: fallbackResume,
        atsScore: fallbackResume.atsScore?.overall || 75,
        suggestions: fallbackResume.atsScore?.improvements || [],
        downloadUrl: '/api/download-resume/' + Date.now(),
        message: 'Resume generated successfully (fallback mode)',
        fallback: true,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      };

      console.log('🔄 Sending fallback response');
      return NextResponse.json(fallbackResponse);
    } catch (fallbackError) {
      console.error('💥 Fallback also failed:', fallbackError);
      return NextResponse.json(
        {
          error: 'Failed to generate resume',
          details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        },
        { status: 500 }
      );
    }
  }
}

function createResumePrompt(formData) {
  const { personal, education, experience, skills, projects } = formData;

  // Create a raw resume text from form data
  const rawResumeText = `
NAME: ${personal.firstName} ${personal.lastName}
EMAIL: ${personal.email}
PHONE: ${personal.phone || 'Not provided'}
LOCATION: ${personal.location || 'Not provided'}
LINKEDIN: ${personal.linkedin || 'Not provided'}
PORTFOLIO: ${personal.portfolio || 'Not provided'}

SUMMARY: ${personal.summary || 'Not provided'}

EDUCATION:
${education?.map(edu => `${edu.degree} | ${edu.institution} | ${edu.startDate} - ${edu.endDate}`).join('\n') || 'Not provided'}

EXPERIENCE:
${experience?.map(exp => `${exp.title} | ${exp.company} | ${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}\n${exp.description || ''}`).join('\n\n') || 'Not provided'}

SKILLS:
Technical: ${skills?.technical?.join(', ') || 'Not provided'}
Languages: ${skills?.languages?.join(', ') || 'Not provided'}
Certifications: ${skills?.certifications?.join(', ') || 'Not provided'}

PROJECTS:
${projects?.map(proj => `${proj.name} | ${proj.technologies}\n${proj.description || ''}`).join('\n\n') || 'Not provided'}
  `;

  return `You are a professional ATS resume optimizer designed to maximize job-matching score (target: 90+ ATS score) for technology and professional positions.

Your tasks:
✅ Reformat the resume using consistent, clean bullet points
✅ Match job-specific keywords and relevant tech terms
✅ Improve wording, sentence flow, and action verbs
✅ Maintain clear formatting: Summary, Education, Experience, Projects, Skills
✅ Add quantified achievements with metrics (%, numbers, growth, impact)
✅ Use strong action verbs (Led, Developed, Implemented, Achieved, Optimized, etc.)

Resume Format Reference (mimic this style):
---
NAME
📧 Email | 📱 Phone | 🔗 LinkedIn | 🌐 Portfolio

*PROFESSIONAL SUMMARY*
2–4 lines that describe role focus, key skills, and major achievements with quantified impact

*EDUCATION*
Degree | Institution | Year | GPA (if strong) | Relevant Coursework

*EXPERIENCE*
Role | Company | Date Range
• What you built, led, or achieved with specific technologies
• Quantifiable impact (%, time saved, revenue, users, performance improvements)
• Technical skills and methodologies used

*PROJECTS*
Project Title | Timeline | Technologies Used
• Goal, approach, and technical implementation
• Measurable results or impact

*SKILLS*
Technical Skills: Programming languages, frameworks, tools
Languages: Spoken languages with proficiency levels
Certifications: Professional certifications and credentials
---

CRITICAL REQUIREMENTS:
1. Use action verbs and quantify achievements wherever possible
2. Include relevant technical keywords naturally
3. Ensure ATS compatibility with standard section headers
4. Make bullet points impactful and results-focused
5. Maintain professional tone and formatting

OUTPUT FORMAT: Return a JSON object with this exact structure:
{
  "enhancedContent": {
    "professionalSummary": "Enhanced 2-4 line summary with quantified achievements",
    "experience": [
      {
        "title": "Enhanced job title",
        "company": "Company name",
        "location": "Location",
        "startDate": "MM/YYYY",
        "endDate": "MM/YYYY or Present",
        "achievements": [
          "• Action verb + what you did + quantified result",
          "• Another achievement with metrics and impact",
          "• Third achievement highlighting technical skills"
        ]
      }
    ],
    "education": [
      {
        "degree": "Degree name",
        "institution": "Institution name",
        "location": "Location",
        "startDate": "MM/YYYY",
        "endDate": "MM/YYYY",
        "gpa": "GPA if strong",
        "relevant": "Relevant coursework or achievements"
      }
    ],
    "skills": {
      "technical": ["Enhanced technical skills with industry-standard terms"],
      "languages": ["Languages with proficiency levels"],
      "certifications": ["Professional certifications"]
    },
    "projects": [
      {
        "name": "Enhanced project name",
        "description": "Enhanced description with technical details and impact",
        "technologies": "Technologies used",
        "link": "Project link if available"
      }
    ]
  },
  "atsScore": {
    "overall": 88,
    "breakdown": {
      "keywords": 90,
      "formatting": 85,
      "achievements": 90,
      "skills": 85
    },
    "improvements": [
      "Add specific metrics to Summary for +4 ATS points",
      "Include more action verbs in Experience for +3 ATS points",
      "Add relevant certifications for +5 ATS points"
    ]
  },
  "keywords": ["list of important technical and industry keywords included"]
}

Resume to optimize:
${rawResumeText}

Make the resume significantly more professional, impactful, and ATS-optimized than the original input.`;
}

function processGeneratedContent(content, originalData) {
  try {
    // Extract JSON from the AI response (handle potential markdown formatting)
    let jsonContent = content;

    // Remove markdown code blocks if present
    if (content.includes('```json')) {
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonContent = jsonMatch[1];
      }
    } else if (content.includes('```')) {
      const jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonContent = jsonMatch[1];
      }
    }

    // Parse the enhanced content
    const enhancedData = JSON.parse(jsonContent);

    // Validate the structure
    if (!enhancedData.enhancedContent || !enhancedData.atsScore) {
      throw new Error('Invalid AI response structure');
    }

    return {
      enhancedContent: enhancedData.enhancedContent,
      atsScore: enhancedData.atsScore,
      keywords: enhancedData.keywords || [],
      originalData,
      timestamp: new Date().toISOString(),
      version: '2.0',
      type: 'ai-enhanced'
    };

  } catch (error) {
    console.error('Error parsing AI response:', error);

    // Fallback: create a basic enhanced structure
    return createEnhancedFallback(originalData, content);
  }
}

function createEnhancedFallback(originalData, rawContent) {
  const { personal, education, experience, skills, projects } = originalData;

  return {
    enhancedContent: {
      professionalSummary: personal.summary || 'Dedicated professional with strong background and commitment to excellence.',
      experience: experience?.map(exp => ({
        title: exp.title,
        company: exp.company,
        location: exp.location,
        startDate: exp.startDate,
        endDate: exp.current ? 'Present' : exp.endDate,
        achievements: exp.description ? [exp.description] : ['Contributed to team success and organizational goals']
      })) || [],
      education: education || [],
      skills: skills || { technical: [], languages: [], certifications: [] },
      projects: projects || []
    },
    atsScore: {
      overall: 75,
      breakdown: {
        keywords: 70,
        formatting: 80,
        achievements: 70,
        skills: 80
      },
      improvements: ['Add more quantified achievements', 'Include industry keywords', 'Enhance technical skills section']
    },
    keywords: ['professional', 'experienced', 'skilled'],
    originalData,
    timestamp: new Date().toISOString(),
    version: '2.0',
    type: 'fallback-enhanced',
    rawContent
  };
}

function createFallbackResume(formData) {
  console.log('🔄 Creating fallback resume for:', formData);

  // Ensure formData has the expected structure
  const safeFormData = {
    personal: formData?.personal || {},
    education: formData?.education || [],
    experience: formData?.experience || [],
    skills: formData?.skills || { technical: [], languages: [], certifications: [] },
    projects: formData?.projects || []
  };

  const { personal, education, experience, skills, projects } = safeFormData;

  const fallbackData = {
    enhancedContent: {
      professionalSummary: personal.summary || 'Dedicated professional with strong educational background and commitment to excellence.',
      experience: experience.map(exp => ({
        title: exp.title || 'Position',
        company: exp.company || 'Company',
        location: exp.location || '',
        startDate: exp.startDate || '',
        endDate: exp.current ? 'Present' : (exp.endDate || ''),
        achievements: exp.description ? [exp.description] : ['Contributed to team objectives and organizational success']
      })),
      education: education,
      skills: {
        technical: skills.technical || [],
        languages: skills.languages || [],
        certifications: skills.certifications || []
      },
      projects: projects
    },
    atsScore: {
      overall: 70,
      breakdown: {
        keywords: 65,
        formatting: 75,
        achievements: 65,
        skills: 75
      },
      improvements: [
        'Add quantified achievements with specific metrics',
        'Include more industry-relevant keywords',
        'Enhance technical skills with proficiency levels',
        'Add action verbs to experience descriptions'
      ]
    },
    keywords: ['professional', 'experienced', 'dedicated', 'skilled'],
    originalData: safeFormData,
    timestamp: new Date().toISOString(),
    version: '2.0',
    type: 'fallback'
  };

  console.log('✅ Fallback resume created:', fallbackData);
  return fallbackData;
}
