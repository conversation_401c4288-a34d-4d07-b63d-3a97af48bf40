import { GoogleGenerativeAI } from '@google/generative-ai';
import { NextResponse } from 'next/server';

// Initialize Gemini AI with API key
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export async function POST(request) {
  let formData = {};
  try {
    console.log('🚀 Resume generation API called');
    formData = await request.json();
    console.log('📝 Form data received:', JSON.stringify(formData, null, 2));

    // Validate required fields
    if (!formData.personal?.firstName || !formData.personal?.lastName || !formData.personal?.email) {
      console.error('❌ Missing required fields');
      return NextResponse.json(
        { error: 'Missing required personal information (First Name, Last Name, and Email are required)' },
        { status: 400 }
      );
    }

    // Check if API key is configured
    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {
      console.log('⚠️ Gemini API key not configured, using fallback');
      // Fallback: return a basic resume structure
      const fallbackResume = createFallbackResume(formData);
      return NextResponse.json({
        success: true,
        resumeData: fallbackResume,
        atsScore: fallbackResume.atsScore?.overall || 75,
        suggestions: fallbackResume.atsScore?.improvements || [],
        downloadUrl: '/api/download-resume/' + Date.now(),
        message: 'Resume generated successfully (using template)',
        fallback: true
      });
    }

    console.log('🤖 Initializing Gemini AI...');

    // Get the generative model - Using Gemini Pro
    const model = genAI.getGenerativeModel({
      model: 'gemini-1.5-pro-latest',
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 4000,
      }
    });

    // Prepare prompt for Gemini API
    const prompt = createResumePrompt(formData);
    console.log('📋 Prompt created, length:', prompt.length);

    // Generate content using the official SDK
    console.log('🔄 Calling Gemini API...');
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const generatedContent = response.text();
    console.log('✅ Gemini response received, length:', generatedContent?.length || 0);

    if (!generatedContent) {
      throw new Error('No content generated from Gemini API');
    }

    // Process the generated content and create resume
    console.log('🔧 Processing generated content...');
    const resumeData = processGeneratedContent(generatedContent, formData);
    console.log('📊 Resume data processed:', {
      hasEnhancedContent: !!resumeData.enhancedContent,
      atsScore: resumeData.atsScore?.overall,
      suggestionsCount: resumeData.atsScore?.improvements?.length || 0
    });

    // Return the processed data with enhanced structure
    const responseData = {
      success: true,
      resumeData,
      atsScore: resumeData.atsScore?.overall || 75,
      suggestions: resumeData.atsScore?.improvements || [],
      downloadUrl: '/api/download-resume/' + Date.now(),
      message: 'Resume generated successfully with AI optimization',
      generatedAt: new Date().toISOString()
    };

    console.log('✨ Sending successful response');
    return NextResponse.json(responseData);

  } catch (error) {
    console.error('💥 Resume generation error:', error);
    console.error('Error stack:', error.stack);

    // Enhanced error handling with fallback
    try {
      const fallbackResume = createFallbackResume(formData || {});
      const fallbackResponse = {
        success: true,
        resumeData: fallbackResume,
        atsScore: fallbackResume.atsScore?.overall || 75,
        suggestions: fallbackResume.atsScore?.improvements || [],
        downloadUrl: '/api/download-resume/' + Date.now(),
        message: 'Resume generated successfully (fallback mode)',
        fallback: true,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      };

      console.log('🔄 Sending fallback response');
      return NextResponse.json(fallbackResponse);
    } catch (fallbackError) {
      console.error('💥 Fallback also failed:', fallbackError);
      return NextResponse.json(
        {
          error: 'Failed to generate resume',
          details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        },
        { status: 500 }
      );
    }
  }
}

function createResumePrompt(formData) {
  const { personal, education, experience, skills, projects } = formData;

  return `You are an expert resume writer and ATS optimization specialist. Create a highly optimized, ATS-friendly professional resume based on the following information. Your goal is to maximize ATS compatibility while maintaining professional appeal.

CRITICAL ATS OPTIMIZATION REQUIREMENTS:
1. Use standard section headers (PROFESSIONAL SUMMARY, EXPERIENCE, EDUCATION, SKILLS, PROJECTS)
2. Include relevant industry keywords and action verbs
3. Quantify achievements with specific metrics and percentages
4. Use bullet points with strong action verbs (Led, Managed, Developed, Implemented, Achieved, etc.)
5. Ensure keyword density for technical skills and industry terms
6. Format dates consistently (MM/YYYY format)
7. Use standard job titles and company descriptions

INPUT DATA:
Personal Information:
- Name: ${personal.firstName} ${personal.lastName}
- Email: ${personal.email}
- Phone: ${personal.phone || 'Not provided'}
- Location: ${personal.location || 'Not provided'}
- LinkedIn: ${personal.linkedin || 'Not provided'}
- Portfolio: ${personal.portfolio || 'Not provided'}
- Summary: ${personal.summary || 'Not provided'}

Education: ${education ? JSON.stringify(education, null, 2) : 'Not provided'}
Experience: ${experience ? JSON.stringify(experience, null, 2) : 'Not provided'}
Skills: ${skills ? JSON.stringify(skills, null, 2) : 'Not provided'}
Projects: ${projects ? JSON.stringify(projects, null, 2) : 'Not provided'}

ENHANCEMENT INSTRUCTIONS:
1. **Professional Summary**: Rewrite to be compelling, keyword-rich, and achievement-focused (3-4 lines max)
2. **Experience**: Transform basic job descriptions into achievement-focused bullet points with:
   - Strong action verbs
   - Quantified results (percentages, numbers, metrics)
   - Industry-relevant keywords
   - Impact statements
3. **Skills**: Organize into categories (Technical, Languages, Certifications) with industry-standard terminology
4. **Projects**: Enhance descriptions with technical details and business impact
5. **Education**: Add relevant coursework, honors, or achievements if applicable

OUTPUT FORMAT: Return a JSON object with this exact structure:
{
  "enhancedContent": {
    "professionalSummary": "Enhanced summary text",
    "experience": [
      {
        "title": "Enhanced job title",
        "company": "Company name",
        "location": "Location",
        "startDate": "MM/YYYY",
        "endDate": "MM/YYYY or Present",
        "achievements": ["Enhanced bullet point 1", "Enhanced bullet point 2", "Enhanced bullet point 3"]
      }
    ],
    "education": [enhanced education entries],
    "skills": {
      "technical": ["enhanced technical skills"],
      "languages": ["languages"],
      "certifications": ["certifications"]
    },
    "projects": [enhanced project entries]
  },
  "atsScore": {
    "overall": 85,
    "breakdown": {
      "keywords": 90,
      "formatting": 85,
      "achievements": 80,
      "skills": 90
    },
    "improvements": ["Specific improvement suggestions"]
  },
  "keywords": ["list of important keywords included"]
}

Make the resume content significantly more professional and impactful than the original input.`;
}

function processGeneratedContent(content, originalData) {
  try {
    // Extract JSON from the AI response (handle potential markdown formatting)
    let jsonContent = content;

    // Remove markdown code blocks if present
    if (content.includes('```json')) {
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonContent = jsonMatch[1];
      }
    } else if (content.includes('```')) {
      const jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonContent = jsonMatch[1];
      }
    }

    // Parse the enhanced content
    const enhancedData = JSON.parse(jsonContent);

    // Validate the structure
    if (!enhancedData.enhancedContent || !enhancedData.atsScore) {
      throw new Error('Invalid AI response structure');
    }

    return {
      enhancedContent: enhancedData.enhancedContent,
      atsScore: enhancedData.atsScore,
      keywords: enhancedData.keywords || [],
      originalData,
      timestamp: new Date().toISOString(),
      version: '2.0',
      type: 'ai-enhanced'
    };

  } catch (error) {
    console.error('Error parsing AI response:', error);

    // Fallback: create a basic enhanced structure
    return createEnhancedFallback(originalData, content);
  }
}

function createEnhancedFallback(originalData, rawContent) {
  const { personal, education, experience, skills, projects } = originalData;

  return {
    enhancedContent: {
      professionalSummary: personal.summary || 'Dedicated professional with strong background and commitment to excellence.',
      experience: experience?.map(exp => ({
        title: exp.title,
        company: exp.company,
        location: exp.location,
        startDate: exp.startDate,
        endDate: exp.current ? 'Present' : exp.endDate,
        achievements: exp.description ? [exp.description] : ['Contributed to team success and organizational goals']
      })) || [],
      education: education || [],
      skills: skills || { technical: [], languages: [], certifications: [] },
      projects: projects || []
    },
    atsScore: {
      overall: 75,
      breakdown: {
        keywords: 70,
        formatting: 80,
        achievements: 70,
        skills: 80
      },
      improvements: ['Add more quantified achievements', 'Include industry keywords', 'Enhance technical skills section']
    },
    keywords: ['professional', 'experienced', 'skilled'],
    originalData,
    timestamp: new Date().toISOString(),
    version: '2.0',
    type: 'fallback-enhanced',
    rawContent
  };
}

function createFallbackResume(formData) {
  console.log('🔄 Creating fallback resume for:', formData);

  // Ensure formData has the expected structure
  const safeFormData = {
    personal: formData?.personal || {},
    education: formData?.education || [],
    experience: formData?.experience || [],
    skills: formData?.skills || { technical: [], languages: [], certifications: [] },
    projects: formData?.projects || []
  };

  const { personal, education, experience, skills, projects } = safeFormData;

  const fallbackData = {
    enhancedContent: {
      professionalSummary: personal.summary || 'Dedicated professional with strong educational background and commitment to excellence.',
      experience: experience.map(exp => ({
        title: exp.title || 'Position',
        company: exp.company || 'Company',
        location: exp.location || '',
        startDate: exp.startDate || '',
        endDate: exp.current ? 'Present' : (exp.endDate || ''),
        achievements: exp.description ? [exp.description] : ['Contributed to team objectives and organizational success']
      })),
      education: education,
      skills: {
        technical: skills.technical || [],
        languages: skills.languages || [],
        certifications: skills.certifications || []
      },
      projects: projects
    },
    atsScore: {
      overall: 70,
      breakdown: {
        keywords: 65,
        formatting: 75,
        achievements: 65,
        skills: 75
      },
      improvements: [
        'Add quantified achievements with specific metrics',
        'Include more industry-relevant keywords',
        'Enhance technical skills with proficiency levels',
        'Add action verbs to experience descriptions'
      ]
    },
    keywords: ['professional', 'experienced', 'dedicated', 'skilled'],
    originalData: safeFormData,
    timestamp: new Date().toISOString(),
    version: '2.0',
    type: 'fallback'
  };

  console.log('✅ Fallback resume created:', fallbackData);
  return fallbackData;
}
