import { NextResponse } from 'next/server';

// Note: Replace with your actual Gemini API integration
// This is a placeholder structure for the Gemini API integration
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

export async function POST(request) {
  try {
    const formData = await request.json();

    // Validate required fields
    if (!formData.personal?.name || !formData.personal?.email) {
      return NextResponse.json(
        { error: 'Missing required personal information' },
        { status: 400 }
      );
    }

    // Check if API key is configured
    if (!GEMINI_API_KEY || GEMINI_API_KEY === 'your_actual_gemini_api_key_here') {
      console.error('Gemini API key not configured, using fallback');
      // Fallback: return a basic resume structure
      const fallbackResume = createFallbackResume(formData);
      return NextResponse.json({
        success: true,
        resumeData: fallbackResume,
        downloadUrl: '/api/download-resume/' + Date.now(),
        message: 'Resume generated successfully (using template)',
        fallback: true
      });
    }

    // Prepare prompt for Gemini API
    const prompt = createResumePrompt(formData);

    // Call Gemini API
    const geminiResponse = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        }
      })
    });

    if (!geminiResponse.ok) {
      throw new Error(`Gemini API error: ${geminiResponse.status}`);
    }

    const geminiData = await geminiResponse.json();
    const generatedContent = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!generatedContent) {
      throw new Error('No content generated from Gemini API');
    }

    // Process the generated content and create resume
    const resumeData = processGeneratedContent(generatedContent, formData);
    
    // In a real implementation, you would:
    // 1. Generate PDF from the resume data
    // 2. Store it in cloud storage
    // 3. Return the download URL
    
    // For now, return the processed data
    return NextResponse.json({
      success: true,
      resumeData,
      // Mock URL - replace with actual PDF generation
      downloadUrl: '/api/download-resume/' + Date.now(),
      message: 'Resume generated successfully'
    });

  } catch (error) {
    console.error('Resume generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate resume', details: error.message },
      { status: 500 }
    );
  }
}

function createResumePrompt(formData) {
  const { personal, education, experience, skills } = formData;
  
  return `Create a professional resume based on the following information:

Personal Information:
- Name: ${personal.name}
- Email: ${personal.email}
- Phone: ${personal.phone || 'Not provided'}

Education:
- Degree: ${education.degree || 'Not provided'}
- Institution: ${education.institution || 'Not provided'}
- Field of Study: ${education.field || 'Not provided'}
- Graduation Year: ${education.graduationYear || 'Not provided'}

Experience: ${experience ? JSON.stringify(experience) : 'Not provided'}
Skills: ${skills ? JSON.stringify(skills) : 'Not provided'}

Please generate a well-structured, professional resume in a clean format. Include:
1. A professional summary
2. Work experience with bullet points highlighting achievements
3. Education details
4. Skills section
5. Any additional relevant sections

Make it ATS-friendly and professional. Return the content in a structured format that can be easily converted to PDF.`;
}

function processGeneratedContent(content, originalData) {
  // Process the Gemini-generated content
  // This would typically involve parsing the AI response and structuring it
  return {
    generatedContent: content,
    originalData,
    timestamp: new Date().toISOString(),
    version: '1.0'
  };
}

function createFallbackResume(formData) {
  const { personal, education } = formData;

  return {
    generatedContent: `
# ${personal.name}
**Email:** ${personal.email}
**Phone:** ${personal.phone || 'Not provided'}

## Professional Summary
Dedicated professional with strong educational background and commitment to excellence.

## Education
**${education.degree || 'Degree'}** in ${education.field || 'Field of Study'}
${education.institution || 'Institution'} | ${education.graduationYear || 'Year'}

## Skills
- Strong communication skills
- Problem-solving abilities
- Team collaboration
- Attention to detail

## Experience
Ready to contribute to a dynamic team and grow professionally.
    `,
    originalData: formData,
    timestamp: new Date().toISOString(),
    version: '1.0',
    type: 'fallback'
  };
}
