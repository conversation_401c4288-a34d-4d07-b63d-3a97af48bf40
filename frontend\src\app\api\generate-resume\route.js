import { NextResponse } from 'next/server';

// Note: Replace with your actual Gemini API integration
// This is a placeholder structure for the Gemini API integration
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

export async function POST(request) {
  try {
    const formData = await request.json();

    // Validate required fields
    if (!formData.personal?.firstName || !formData.personal?.lastName || !formData.personal?.email) {
      return NextResponse.json(
        { error: 'Missing required personal information (First Name, Last Name, and Email are required)' },
        { status: 400 }
      );
    }

    // Check if API key is configured
    if (!GEMINI_API_KEY || GEMINI_API_KEY === 'your_actual_gemini_api_key_here') {
      console.error('Gemini API key not configured, using fallback');
      // Fallback: return a basic resume structure
      const fallbackResume = createFallbackResume(formData);
      return NextResponse.json({
        success: true,
        resumeData: fallbackResume,
        downloadUrl: '/api/download-resume/' + Date.now(),
        message: 'Resume generated successfully (using template)',
        fallback: true
      });
    }

    // Prepare prompt for Gemini API
    const prompt = createResumePrompt(formData);

    // Call Gemini API
    const geminiResponse = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        }
      })
    });

    if (!geminiResponse.ok) {
      throw new Error(`Gemini API error: ${geminiResponse.status}`);
    }

    const geminiData = await geminiResponse.json();
    const generatedContent = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!generatedContent) {
      throw new Error('No content generated from Gemini API');
    }

    // Process the generated content and create resume
    const resumeData = processGeneratedContent(generatedContent, formData);
    
    // In a real implementation, you would:
    // 1. Generate PDF from the resume data
    // 2. Store it in cloud storage
    // 3. Return the download URL
    
    // For now, return the processed data
    return NextResponse.json({
      success: true,
      resumeData,
      // Mock URL - replace with actual PDF generation
      downloadUrl: '/api/download-resume/' + Date.now(),
      message: 'Resume generated successfully'
    });

  } catch (error) {
    console.error('Resume generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate resume', details: error.message },
      { status: 500 }
    );
  }
}

function createResumePrompt(formData) {
  const { personal, education, experience, skills, projects } = formData;

  return `You are an expert resume writer and ATS optimization specialist. Create a highly optimized, ATS-friendly professional resume based on the following information. Your goal is to maximize ATS compatibility while maintaining professional appeal.

CRITICAL ATS OPTIMIZATION REQUIREMENTS:
1. Use standard section headers (PROFESSIONAL SUMMARY, EXPERIENCE, EDUCATION, SKILLS, PROJECTS)
2. Include relevant industry keywords and action verbs
3. Quantify achievements with specific metrics and percentages
4. Use bullet points with strong action verbs (Led, Managed, Developed, Implemented, Achieved, etc.)
5. Ensure keyword density for technical skills and industry terms
6. Format dates consistently (MM/YYYY format)
7. Use standard job titles and company descriptions

INPUT DATA:
Personal Information:
- Name: ${personal.firstName} ${personal.lastName}
- Email: ${personal.email}
- Phone: ${personal.phone || 'Not provided'}
- Location: ${personal.location || 'Not provided'}
- LinkedIn: ${personal.linkedin || 'Not provided'}
- Portfolio: ${personal.portfolio || 'Not provided'}
- Summary: ${personal.summary || 'Not provided'}

Education: ${education ? JSON.stringify(education, null, 2) : 'Not provided'}
Experience: ${experience ? JSON.stringify(experience, null, 2) : 'Not provided'}
Skills: ${skills ? JSON.stringify(skills, null, 2) : 'Not provided'}
Projects: ${projects ? JSON.stringify(projects, null, 2) : 'Not provided'}

ENHANCEMENT INSTRUCTIONS:
1. **Professional Summary**: Rewrite to be compelling, keyword-rich, and achievement-focused (3-4 lines max)
2. **Experience**: Transform basic job descriptions into achievement-focused bullet points with:
   - Strong action verbs
   - Quantified results (percentages, numbers, metrics)
   - Industry-relevant keywords
   - Impact statements
3. **Skills**: Organize into categories (Technical, Languages, Certifications) with industry-standard terminology
4. **Projects**: Enhance descriptions with technical details and business impact
5. **Education**: Add relevant coursework, honors, or achievements if applicable

OUTPUT FORMAT: Return a JSON object with this exact structure:
{
  "enhancedContent": {
    "professionalSummary": "Enhanced summary text",
    "experience": [
      {
        "title": "Enhanced job title",
        "company": "Company name",
        "location": "Location",
        "startDate": "MM/YYYY",
        "endDate": "MM/YYYY or Present",
        "achievements": ["Enhanced bullet point 1", "Enhanced bullet point 2", "Enhanced bullet point 3"]
      }
    ],
    "education": [enhanced education entries],
    "skills": {
      "technical": ["enhanced technical skills"],
      "languages": ["languages"],
      "certifications": ["certifications"]
    },
    "projects": [enhanced project entries]
  },
  "atsScore": {
    "overall": 85,
    "breakdown": {
      "keywords": 90,
      "formatting": 85,
      "achievements": 80,
      "skills": 90
    },
    "improvements": ["Specific improvement suggestions"]
  },
  "keywords": ["list of important keywords included"]
}

Make the resume content significantly more professional and impactful than the original input.`;
}

function processGeneratedContent(content, originalData) {
  try {
    // Extract JSON from the AI response (handle potential markdown formatting)
    let jsonContent = content;

    // Remove markdown code blocks if present
    if (content.includes('```json')) {
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonContent = jsonMatch[1];
      }
    } else if (content.includes('```')) {
      const jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonContent = jsonMatch[1];
      }
    }

    // Parse the enhanced content
    const enhancedData = JSON.parse(jsonContent);

    // Validate the structure
    if (!enhancedData.enhancedContent || !enhancedData.atsScore) {
      throw new Error('Invalid AI response structure');
    }

    return {
      enhancedContent: enhancedData.enhancedContent,
      atsScore: enhancedData.atsScore,
      keywords: enhancedData.keywords || [],
      originalData,
      timestamp: new Date().toISOString(),
      version: '2.0',
      type: 'ai-enhanced'
    };

  } catch (error) {
    console.error('Error parsing AI response:', error);

    // Fallback: create a basic enhanced structure
    return createEnhancedFallback(originalData, content);
  }
}

function createEnhancedFallback(originalData, rawContent) {
  const { personal, education, experience, skills, projects } = originalData;

  return {
    enhancedContent: {
      professionalSummary: personal.summary || 'Dedicated professional with strong background and commitment to excellence.',
      experience: experience?.map(exp => ({
        title: exp.title,
        company: exp.company,
        location: exp.location,
        startDate: exp.startDate,
        endDate: exp.current ? 'Present' : exp.endDate,
        achievements: exp.description ? [exp.description] : ['Contributed to team success and organizational goals']
      })) || [],
      education: education || [],
      skills: skills || { technical: [], languages: [], certifications: [] },
      projects: projects || []
    },
    atsScore: {
      overall: 75,
      breakdown: {
        keywords: 70,
        formatting: 80,
        achievements: 70,
        skills: 80
      },
      improvements: ['Add more quantified achievements', 'Include industry keywords', 'Enhance technical skills section']
    },
    keywords: ['professional', 'experienced', 'skilled'],
    originalData,
    timestamp: new Date().toISOString(),
    version: '2.0',
    type: 'fallback-enhanced',
    rawContent
  };
}

function createFallbackResume(formData) {
  const { personal, education, experience, skills, projects } = formData;

  return {
    enhancedContent: {
      professionalSummary: personal.summary || 'Dedicated professional with strong educational background and commitment to excellence.',
      experience: experience?.map(exp => ({
        title: exp.title || 'Position',
        company: exp.company || 'Company',
        location: exp.location || '',
        startDate: exp.startDate || '',
        endDate: exp.current ? 'Present' : (exp.endDate || ''),
        achievements: exp.description ? [exp.description] : ['Contributed to team objectives and organizational success']
      })) || [],
      education: education || [],
      skills: skills || { technical: [], languages: [], certifications: [] },
      projects: projects || []
    },
    atsScore: {
      overall: 70,
      breakdown: {
        keywords: 65,
        formatting: 75,
        achievements: 65,
        skills: 75
      },
      improvements: [
        'Add quantified achievements with specific metrics',
        'Include more industry-relevant keywords',
        'Enhance technical skills with proficiency levels',
        'Add action verbs to experience descriptions'
      ]
    },
    keywords: ['professional', 'experienced', 'dedicated', 'skilled'],
    originalData: formData,
    timestamp: new Date().toISOString(),
    version: '2.0',
    type: 'fallback'
  };
}
