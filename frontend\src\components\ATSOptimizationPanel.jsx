"use client";
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Target, 
  TrendingUp, 
  CheckCircle, 
  AlertTriangle, 
  Zap,
  FileText,
  Award,
  BarChart3,
  Lightbulb,
  ArrowUp
} from 'lucide-react';
import ATSScoreCircle from './ATSScoreCircle';

const ATSOptimizationPanel = ({ formData, atsScore, suggestions }) => {
  const [optimizationTips, setOptimizationTips] = useState([]);
  const [scoreBreakdown, setScoreBreakdown] = useState({
    keywords: 0,
    formatting: 0,
    achievements: 0,
    skills: 0
  });

  useEffect(() => {
    // Calculate ATS optimization tips based on form data
    const tips = generateOptimizationTips(formData);
    setOptimizationTips(tips);

    // Set score breakdown
    if (atsScore && typeof atsScore === 'object' && atsScore.breakdown) {
      setScoreBreakdown(atsScore.breakdown);
    } else {
      // Calculate estimated scores based on form data
      const estimated = calculateEstimatedScores(formData);
      setScoreBreakdown(estimated);
    }
  }, [formData, atsScore]);

  const generateOptimizationTips = (data) => {
    const tips = [];

    // Check professional summary
    if (!data.personal.summary || data.personal.summary.length < 50) {
      tips.push({
        type: 'warning',
        category: 'Summary',
        message: 'Add a compelling professional summary (50+ characters)',
        impact: '+8 ATS points',
        icon: FileText
      });
    }

    // Check experience descriptions
    const hasQuantifiedAchievements = data.experience.some(exp => 
      exp.description && (exp.description.includes('%') || exp.description.includes('$') || /\d+/.test(exp.description))
    );
    if (!hasQuantifiedAchievements) {
      tips.push({
        type: 'warning',
        category: 'Experience',
        message: 'Add quantified achievements with numbers, percentages, or metrics',
        impact: '+12 ATS points',
        icon: TrendingUp
      });
    }

    // Check skills section
    if (data.skills.technical.length < 5) {
      tips.push({
        type: 'info',
        category: 'Skills',
        message: 'Add more technical skills (aim for 8-12 relevant skills)',
        impact: '+6 ATS points',
        icon: Zap
      });
    }

    // Check action verbs
    const hasActionVerbs = data.experience.some(exp => 
      exp.description && /^(Led|Developed|Implemented|Managed|Created|Built|Designed|Optimized|Achieved)/i.test(exp.description)
    );
    if (!hasActionVerbs) {
      tips.push({
        type: 'warning',
        category: 'Language',
        message: 'Start experience bullets with strong action verbs',
        impact: '+5 ATS points',
        icon: Award
      });
    }

    // Check contact information
    if (!data.personal.linkedin) {
      tips.push({
        type: 'info',
        category: 'Contact',
        message: 'Add LinkedIn profile URL for better visibility',
        impact: '+3 ATS points',
        icon: Target
      });
    }

    return tips.slice(0, 5); // Limit to top 5 tips
  };

  const calculateEstimatedScores = (data) => {
    let keywords = 60;
    let formatting = 80;
    let achievements = 50;
    let skills = 60;

    // Keywords score
    if (data.personal.summary && data.personal.summary.length > 50) keywords += 15;
    if (data.skills.technical.length > 5) keywords += 15;
    if (data.experience.some(exp => exp.description && exp.description.length > 100)) keywords += 10;

    // Achievements score
    if (data.experience.some(exp => exp.description && /\d+/.test(exp.description))) achievements += 20;
    if (data.experience.some(exp => exp.description && exp.description.includes('%'))) achievements += 15;
    if (data.experience.some(exp => exp.description && /^(Led|Developed|Implemented)/i.test(exp.description))) achievements += 15;

    // Skills score
    if (data.skills.technical.length > 8) skills += 20;
    if (data.skills.certifications.length > 0) skills += 10;
    if (data.skills.languages.length > 0) skills += 10;

    return {
      keywords: Math.min(keywords, 100),
      formatting: formatting,
      achievements: Math.min(achievements, 100),
      skills: Math.min(skills, 100)
    };
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBarColor = (score) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const overallScore = atsScore?.overall || Math.round((scoreBreakdown.keywords + scoreBreakdown.formatting + scoreBreakdown.achievements + scoreBreakdown.skills) / 4);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700"
    >
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-gradient-to-r from-neural-blue to-neural-purple rounded-full flex items-center justify-center">
          <Target className="h-5 w-5 text-white" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-white">ATS Optimization</h3>
          <p className="text-gray-400 text-sm">Real-time resume analysis and suggestions</p>
        </div>
      </div>

      {/* Overall Score */}
      <div className="flex items-center justify-center mb-6">
        <ATSScoreCircle score={overallScore} size={120} />
      </div>

      {/* Score Breakdown */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        {Object.entries(scoreBreakdown).map(([category, score]) => (
          <div key={category} className="bg-gray-900/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-300 capitalize">{category}</span>
              <span className={`text-sm font-bold ${getScoreColor(score)}`}>{score}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${getScoreBarColor(score)}`}
                style={{ width: `${score}%` }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Optimization Tips */}
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-white flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-neural-purple" />
          Optimization Tips
        </h4>
        
        {optimizationTips.length > 0 ? (
          <div className="space-y-3">
            {optimizationTips.map((tip, index) => {
              const Icon = tip.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-3 rounded-lg border ${
                    tip.type === 'warning' 
                      ? 'bg-yellow-900/20 border-yellow-500/30' 
                      : 'bg-blue-900/20 border-blue-500/30'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <Icon className={`h-4 w-4 mt-0.5 ${
                      tip.type === 'warning' ? 'text-yellow-400' : 'text-blue-400'
                    }`} />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                          {tip.category}
                        </span>
                        <span className="text-xs font-semibold text-green-400 flex items-center gap-1">
                          <ArrowUp className="h-3 w-3" />
                          {tip.impact}
                        </span>
                      </div>
                      <p className="text-sm text-gray-300">{tip.message}</p>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-4">
            <CheckCircle className="h-8 w-8 text-green-400 mx-auto mb-2" />
            <p className="text-sm text-green-400 font-medium">Great job! Your resume is well optimized.</p>
            <p className="text-xs text-gray-400 mt-1">Continue filling out sections for even better results.</p>
          </div>
        )}
      </div>

      {/* Additional Suggestions */}
      {suggestions && suggestions.length > 0 && (
        <div className="mt-6 pt-6 border-t border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-neural-blue" />
            AI Recommendations
          </h4>
          <div className="space-y-2">
            {suggestions.slice(0, 3).map((suggestion, index) => (
              <div key={index} className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-neural-blue rounded-full mt-2" />
                <p className="text-sm text-gray-300">{suggestion}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default ATSOptimizationPanel;
