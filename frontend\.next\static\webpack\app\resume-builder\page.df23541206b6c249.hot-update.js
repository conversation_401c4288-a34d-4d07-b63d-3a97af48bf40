"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Sparkles,Trash2,Upload,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* harmony import */ var _components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ATSScoreCircle */ \"(app-pages-browser)/./src/components/ATSScoreCircle.jsx\");\n/* harmony import */ var _components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/EnhancedResumePreview */ \"(app-pages-browser)/./src/components/EnhancedResumePreview.jsx\");\n/* harmony import */ var _components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/PDFDownload */ \"(app-pages-browser)/./src/components/PDFDownload.jsx\");\n/* harmony import */ var _components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ResumeBuilderModeToggle */ \"(app-pages-browser)/./src/components/ResumeBuilderModeToggle.jsx\");\n/* harmony import */ var _components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ResumeUpload */ \"(app-pages-browser)/./src/components/ResumeUpload.jsx\");\n/* harmony import */ var _components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ATSAnalysisDisplay */ \"(app-pages-browser)/./src/components/ATSAnalysisDisplay.jsx\");\n/* harmony import */ var _components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/JobDescriptionInput */ \"(app-pages-browser)/./src/components/JobDescriptionInput.jsx\");\n/* harmony import */ var _components_BeforeAfterComparison__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/BeforeAfterComparison */ \"(app-pages-browser)/./src/components/BeforeAfterComparison.jsx\");\n/* harmony import */ var _components_AIContentSuggestions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AIContentSuggestions */ \"(app-pages-browser)/./src/components/AIContentSuggestions.jsx\");\n/* harmony import */ var _components_ATSOptimizationPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ATSOptimizationPanel */ \"(app-pages-browser)/./src/components/ATSOptimizationPanel.jsx\");\n/* harmony import */ var _components_TemplateSelector__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/TemplateSelector */ \"(app-pages-browser)/./src/components/TemplateSelector.jsx\");\n/* harmony import */ var _components_UploadEnhancementWorkflow__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/UploadEnhancementWorkflow */ \"(app-pages-browser)/./src/components/UploadEnhancementWorkflow.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* harmony import */ var _components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ATSFieldIndicator */ \"(app-pages-browser)/./src/components/ATSFieldIndicator.jsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./src/components/ClientOnly.jsx\");\n/* harmony import */ var _components_ATSTooltip__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ATSTooltip */ \"(app-pages-browser)/./src/components/ATSTooltip.jsx\");\n/* harmony import */ var _components_StepIndicator__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/StepIndicator */ \"(app-pages-browser)/./src/components/StepIndicator.jsx\");\n/* harmony import */ var _components_StickyNavigation__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/StickyNavigation */ \"(app-pages-browser)/./src/components/StickyNavigation.jsx\");\n/* harmony import */ var _components_FormHeader__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/FormHeader */ \"(app-pages-browser)/./src/components/FormHeader.jsx\");\n/* harmony import */ var _components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ResumeFormComponents */ \"(app-pages-browser)/./src/components/ResumeFormComponents.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import SuccessScreen from \"@/components/SuccessScreen\";\n\nconst ResumeBuilder = ()=>{\n    _s();\n    // Main state management\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for upload functionality\n    const [builderMode, setBuilderMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create'); // 'create', 'upload', 'analyze'\n    const [uploadAnalysis, setUploadAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAnalysis, setShowAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUploadWorkflow, setShowUploadWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Enhanced upload workflow state\n    const [showJobDescriptionInput, setShowJobDescriptionInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingTargeted, setIsGeneratingTargeted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetedResumeData, setTargetedResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Template selection state\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ]\n    });\n    // Real-time ATS analysis\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(formData);\n    // Steps configuration\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            description: \"Tell us about yourself\"\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            description: \"Your academic background\"\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            description: \"Your work experience\"\n        },\n        {\n            id: 3,\n            title: \"Skills & Projects\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            description: \"Showcase your abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            description: \"Finalize your resume\"\n        }\n    ];\n    // Update form data\n    const updateFormData = function(section, field, value) {\n        let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        setFormData((prev)=>{\n            if (index !== null && Array.isArray(prev[section])) {\n                const newArray = [\n                    ...prev[section]\n                ];\n                newArray[index] = {\n                    ...newArray[index],\n                    [field]: value\n                };\n                return {\n                    ...prev,\n                    [section]: newArray\n                };\n            } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                return {\n                    ...prev,\n                    [section]: {\n                        ...prev[section],\n                        [field]: value\n                    }\n                };\n            }\n            return prev;\n        });\n    };\n    // Add new item to array sections\n    const addArrayItem = (section, template)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: [\n                    ...prev[section],\n                    {\n                        ...template,\n                        id: Math.random().toString(36).substring(2, 11)\n                    }\n                ]\n            }));\n    };\n    // Remove item from array sections\n    const removeArrayItem = (section, id)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: prev[section].filter((item)=>item.id !== id)\n            }));\n    };\n    // Navigation functions\n    const nextStep = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    // Generate resume with AI\n    const generateResume = async ()=>{\n        try {\n            var _data_resumeData_atsScore, _data_resumeData, _data_resumeData_atsScore1, _data_resumeData1;\n            console.log('🚀 Starting resume generation...');\n            console.log('📝 Form data:', formData);\n            setIsGenerating(true);\n            setShowProgressBar(true);\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    templateId: selectedTemplate\n                })\n            });\n            console.log('📡 API response status:', response.status);\n            console.log('📡 API response headers:', Object.fromEntries(response.headers.entries()));\n            // Check if response is actually JSON\n            const contentType = response.headers.get('content-type');\n            if (!contentType || !contentType.includes('application/json')) {\n                const textResponse = await response.text();\n                console.error('❌ Non-JSON response received:', textResponse);\n                throw new Error('Server returned non-JSON response');\n            }\n            const data = await response.json();\n            console.log('📊 API response data:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            // Store the enhanced resume data and ATS information\n            console.log('✅ Setting resume data...');\n            setResumeUrl(data.downloadUrl);\n            setResumeData(data.resumeData);\n            setAtsScore(data.atsScore || ((_data_resumeData = data.resumeData) === null || _data_resumeData === void 0 ? void 0 : (_data_resumeData_atsScore = _data_resumeData.atsScore) === null || _data_resumeData_atsScore === void 0 ? void 0 : _data_resumeData_atsScore.overall) || 75);\n            setSuggestions(data.suggestions || ((_data_resumeData1 = data.resumeData) === null || _data_resumeData1 === void 0 ? void 0 : (_data_resumeData_atsScore1 = _data_resumeData1.atsScore) === null || _data_resumeData_atsScore1 === void 0 ? void 0 : _data_resumeData_atsScore1.improvements) || []);\n            console.log('🎉 Resume generation completed successfully');\n        } catch (error) {\n            console.error(\"💥 Error generating resume:\", error);\n            console.error(\"Error details:\", {\n                message: error.message,\n                stack: error.stack,\n                name: error.name\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || \"Failed to generate resume\");\n            setIsGenerating(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsGenerating(false);\n        // Show success message with ATS score\n        const scoreMessage = atsScore ? \"Resume generated! ATS Score: \".concat(atsScore, \"%\") : \"Your resume has been generated successfully!\";\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(scoreMessage);\n    };\n    // Handle upload analysis completion\n    const handleUploadAnalysis = (analysisData)=>{\n        console.log('📊 Upload analysis received:', analysisData);\n        setUploadAnalysis(analysisData);\n        // For upload & enhance mode, show job description input\n        if (builderMode === 'upload') {\n            setShowJobDescriptionInput(true);\n        } else {\n            // For quick analysis, show results immediately\n            setShowAnalysis(true);\n        }\n        // If it's a full analysis, populate form data (even with minimal data)\n        if (analysisData.analysisType === 'full' && analysisData.extractedData) {\n            const extracted = analysisData.extractedData;\n            console.log('📋 Extracted data for form population:', extracted);\n            // Update form data with extracted information\n            setFormData((prevData)=>{\n                var _extracted_personal, _extracted_personal1, _extracted_personal2, _extracted_personal3, _extracted_personal4, _extracted_personal5, _extracted_personal6, _extracted_personal7, _extracted_education, _extracted_experience, _extracted_skills_technical, _extracted_skills, _extracted_skills_languages, _extracted_skills1, _extracted_skills_certifications, _extracted_skills2, _extracted_projects;\n                return {\n                    ...prevData,\n                    personal: {\n                        ...prevData.personal,\n                        firstName: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.firstName) || prevData.personal.firstName,\n                        lastName: ((_extracted_personal1 = extracted.personal) === null || _extracted_personal1 === void 0 ? void 0 : _extracted_personal1.lastName) || prevData.personal.lastName,\n                        email: ((_extracted_personal2 = extracted.personal) === null || _extracted_personal2 === void 0 ? void 0 : _extracted_personal2.email) || prevData.personal.email,\n                        phone: ((_extracted_personal3 = extracted.personal) === null || _extracted_personal3 === void 0 ? void 0 : _extracted_personal3.phone) || prevData.personal.phone,\n                        location: ((_extracted_personal4 = extracted.personal) === null || _extracted_personal4 === void 0 ? void 0 : _extracted_personal4.location) || prevData.personal.location,\n                        linkedin: ((_extracted_personal5 = extracted.personal) === null || _extracted_personal5 === void 0 ? void 0 : _extracted_personal5.linkedin) || prevData.personal.linkedin,\n                        portfolio: ((_extracted_personal6 = extracted.personal) === null || _extracted_personal6 === void 0 ? void 0 : _extracted_personal6.portfolio) || prevData.personal.portfolio,\n                        summary: ((_extracted_personal7 = extracted.personal) === null || _extracted_personal7 === void 0 ? void 0 : _extracted_personal7.summary) || prevData.personal.summary\n                    },\n                    education: ((_extracted_education = extracted.education) === null || _extracted_education === void 0 ? void 0 : _extracted_education.length) > 0 ? extracted.education.map((edu)=>({\n                            ...edu,\n                            id: edu.id || Date.now() + Math.random()\n                        })) : prevData.education,\n                    experience: ((_extracted_experience = extracted.experience) === null || _extracted_experience === void 0 ? void 0 : _extracted_experience.length) > 0 ? extracted.experience.map((exp)=>({\n                            ...exp,\n                            id: exp.id || Date.now() + Math.random()\n                        })) : prevData.experience,\n                    skills: {\n                        technical: ((_extracted_skills = extracted.skills) === null || _extracted_skills === void 0 ? void 0 : (_extracted_skills_technical = _extracted_skills.technical) === null || _extracted_skills_technical === void 0 ? void 0 : _extracted_skills_technical.length) > 0 ? extracted.skills.technical : prevData.skills.technical,\n                        languages: ((_extracted_skills1 = extracted.skills) === null || _extracted_skills1 === void 0 ? void 0 : (_extracted_skills_languages = _extracted_skills1.languages) === null || _extracted_skills_languages === void 0 ? void 0 : _extracted_skills_languages.length) > 0 ? extracted.skills.languages : prevData.skills.languages,\n                        certifications: ((_extracted_skills2 = extracted.skills) === null || _extracted_skills2 === void 0 ? void 0 : (_extracted_skills_certifications = _extracted_skills2.certifications) === null || _extracted_skills_certifications === void 0 ? void 0 : _extracted_skills_certifications.length) > 0 ? extracted.skills.certifications : prevData.skills.certifications\n                    },\n                    projects: ((_extracted_projects = extracted.projects) === null || _extracted_projects === void 0 ? void 0 : _extracted_projects.length) > 0 ? extracted.projects.map((proj)=>({\n                            ...proj,\n                            id: proj.id || Date.now() + Math.random()\n                        })) : prevData.projects\n                };\n            });\n            // Set ATS score and suggestions\n            if (analysisData.atsScore) {\n                var _analysisData_enhancements, _analysisData_analysis;\n                setAtsScore(analysisData.atsScore.overall);\n                setSuggestions(((_analysisData_enhancements = analysisData.enhancements) === null || _analysisData_enhancements === void 0 ? void 0 : _analysisData_enhancements.suggestions) || ((_analysisData_analysis = analysisData.analysis) === null || _analysisData_analysis === void 0 ? void 0 : _analysisData_analysis.recommendations) || []);\n            }\n            console.log('✅ Form data updated with extracted information');\n        } else if (analysisData.fallback) {\n            console.log('⚠️ Using fallback data - minimal extraction');\n            // Even with fallback, try to extract any available information\n            if (analysisData.extractedData) {\n                const extracted = analysisData.extractedData;\n                setFormData((prevData)=>{\n                    var _extracted_personal;\n                    return {\n                        ...prevData,\n                        personal: {\n                            ...prevData.personal,\n                            summary: ((_extracted_personal = extracted.personal) === null || _extracted_personal === void 0 ? void 0 : _extracted_personal.summary) || 'Please add your professional summary here.'\n                        }\n                    };\n                });\n            }\n        }\n    };\n    // Handle job description submission for targeted resume generation\n    const handleJobDescriptionSubmit = async (jobData)=>{\n        if (!(uploadAnalysis === null || uploadAnalysis === void 0 ? void 0 : uploadAnalysis.extractedData)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('No resume data available. Please upload a resume first.');\n            return;\n        }\n        try {\n            setIsGeneratingTargeted(true);\n            console.log('🎯 Generating targeted resume with job data:', jobData);\n            const response = await fetch('/api/generate-targeted-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    extractedResumeData: uploadAnalysis.extractedData,\n                    jobDescription: jobData.description,\n                    jobTitle: jobData.jobTitle,\n                    company: jobData.company\n                })\n            });\n            const data = await response.json();\n            console.log('📊 Targeted resume response:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate targeted resume');\n            }\n            // Update form data with enhanced resume\n            if (data.enhancedResume) {\n                var _data_enhancedResume_personal, _data_enhancedResume_personal1, _data_enhancedResume_personal2, _data_enhancedResume_personal3, _data_enhancedResume_personal4, _data_enhancedResume_personal5, _data_enhancedResume_personal6, _data_enhancedResume_personal7, _data_enhancedResume_education, _data_enhancedResume_experience, _data_enhancedResume_skills, _data_enhancedResume_skills1, _data_enhancedResume_skills2, _data_enhancedResume_projects, _data_atsScore;\n                // Properly structure the enhanced resume data to match form data structure\n                const enhancedFormData = {\n                    personal: {\n                        firstName: ((_data_enhancedResume_personal = data.enhancedResume.personal) === null || _data_enhancedResume_personal === void 0 ? void 0 : _data_enhancedResume_personal.firstName) || '',\n                        lastName: ((_data_enhancedResume_personal1 = data.enhancedResume.personal) === null || _data_enhancedResume_personal1 === void 0 ? void 0 : _data_enhancedResume_personal1.lastName) || '',\n                        email: ((_data_enhancedResume_personal2 = data.enhancedResume.personal) === null || _data_enhancedResume_personal2 === void 0 ? void 0 : _data_enhancedResume_personal2.email) || '',\n                        phone: ((_data_enhancedResume_personal3 = data.enhancedResume.personal) === null || _data_enhancedResume_personal3 === void 0 ? void 0 : _data_enhancedResume_personal3.phone) || '',\n                        location: ((_data_enhancedResume_personal4 = data.enhancedResume.personal) === null || _data_enhancedResume_personal4 === void 0 ? void 0 : _data_enhancedResume_personal4.location) || '',\n                        linkedin: ((_data_enhancedResume_personal5 = data.enhancedResume.personal) === null || _data_enhancedResume_personal5 === void 0 ? void 0 : _data_enhancedResume_personal5.linkedin) || '',\n                        portfolio: ((_data_enhancedResume_personal6 = data.enhancedResume.personal) === null || _data_enhancedResume_personal6 === void 0 ? void 0 : _data_enhancedResume_personal6.portfolio) || '',\n                        summary: ((_data_enhancedResume_personal7 = data.enhancedResume.personal) === null || _data_enhancedResume_personal7 === void 0 ? void 0 : _data_enhancedResume_personal7.summary) || ''\n                    },\n                    education: ((_data_enhancedResume_education = data.enhancedResume.education) === null || _data_enhancedResume_education === void 0 ? void 0 : _data_enhancedResume_education.length) > 0 ? data.enhancedResume.education.map((edu)=>({\n                            id: edu.id || Date.now() + Math.random(),\n                            degree: edu.degree || '',\n                            institution: edu.institution || '',\n                            location: edu.location || '',\n                            startDate: edu.startDate || '',\n                            endDate: edu.endDate || '',\n                            gpa: edu.gpa || '',\n                            relevant: edu.relevant || ''\n                        })) : [\n                        {\n                            id: 1,\n                            degree: \"\",\n                            institution: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            gpa: \"\",\n                            relevant: \"\"\n                        }\n                    ],\n                    experience: ((_data_enhancedResume_experience = data.enhancedResume.experience) === null || _data_enhancedResume_experience === void 0 ? void 0 : _data_enhancedResume_experience.length) > 0 ? data.enhancedResume.experience.map((exp)=>({\n                            id: exp.id || Date.now() + Math.random(),\n                            title: exp.title || '',\n                            company: exp.company || '',\n                            location: exp.location || '',\n                            startDate: exp.startDate || '',\n                            endDate: exp.endDate || '',\n                            current: exp.current || false,\n                            description: exp.description || (exp.achievements ? exp.achievements.join('\\n') : '')\n                        })) : [\n                        {\n                            id: 1,\n                            title: \"\",\n                            company: \"\",\n                            location: \"\",\n                            startDate: \"\",\n                            endDate: \"\",\n                            current: false,\n                            description: \"\"\n                        }\n                    ],\n                    skills: {\n                        technical: ((_data_enhancedResume_skills = data.enhancedResume.skills) === null || _data_enhancedResume_skills === void 0 ? void 0 : _data_enhancedResume_skills.technical) || [],\n                        languages: ((_data_enhancedResume_skills1 = data.enhancedResume.skills) === null || _data_enhancedResume_skills1 === void 0 ? void 0 : _data_enhancedResume_skills1.languages) || [],\n                        certifications: ((_data_enhancedResume_skills2 = data.enhancedResume.skills) === null || _data_enhancedResume_skills2 === void 0 ? void 0 : _data_enhancedResume_skills2.certifications) || []\n                    },\n                    projects: ((_data_enhancedResume_projects = data.enhancedResume.projects) === null || _data_enhancedResume_projects === void 0 ? void 0 : _data_enhancedResume_projects.length) > 0 ? data.enhancedResume.projects.map((proj)=>({\n                            id: proj.id || Date.now() + Math.random(),\n                            name: proj.name || '',\n                            description: proj.description || '',\n                            technologies: proj.technologies || '',\n                            link: proj.link || ''\n                        })) : [\n                        {\n                            id: 1,\n                            name: \"\",\n                            description: \"\",\n                            technologies: \"\",\n                            link: \"\"\n                        }\n                    ]\n                };\n                setFormData(enhancedFormData);\n                setTargetedResumeData(data);\n                setAtsScore(((_data_atsScore = data.atsScore) === null || _data_atsScore === void 0 ? void 0 : _data_atsScore.overall) || 85);\n                setSuggestions(data.recommendations || []);\n                // Show success and move to form editing\n                setShowJobDescriptionInput(false);\n                setCurrentStep(0);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume optimized for the target job!');\n            }\n        } catch (error) {\n            console.error('Targeted resume generation error:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate targeted resume');\n        } finally{\n            setIsGeneratingTargeted(false);\n        }\n    };\n    // Handle mode change\n    const handleModeChange = (mode)=>{\n        setBuilderMode(mode);\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setShowJobDescriptionInput(false);\n        setIsGeneratingTargeted(false);\n        setTargetedResumeData(null);\n        // Show enhanced workflow for upload mode\n        if (mode === 'upload') {\n            setShowUploadWorkflow(true);\n        } else {\n            setShowUploadWorkflow(false);\n        }\n    };\n    // Reset to create mode\n    const resetToCreateMode = ()=>{\n        setBuilderMode('create');\n        setUploadAnalysis(null);\n        setShowAnalysis(false);\n        setResumeGenerated(false);\n        setCurrentStep(0);\n        setFormData({\n            personal: {\n                firstName: \"\",\n                lastName: \"\",\n                email: \"\",\n                phone: \"\",\n                location: \"\",\n                linkedin: \"\",\n                portfolio: \"\",\n                summary: \"\"\n            },\n            education: [\n                {\n                    id: 1,\n                    degree: \"\",\n                    institution: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    gpa: \"\",\n                    relevant: \"\"\n                }\n            ],\n            experience: [\n                {\n                    id: 1,\n                    title: \"\",\n                    company: \"\",\n                    location: \"\",\n                    startDate: \"\",\n                    endDate: \"\",\n                    current: false,\n                    description: \"\"\n                }\n            ],\n            skills: {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: [\n                {\n                    id: 1,\n                    name: \"\",\n                    description: \"\",\n                    technologies: \"\",\n                    link: \"\"\n                }\n            ]\n        });\n    };\n    // Floating background elements with deterministic values to avoid hydration mismatch\n    const FloatingElements = ()=>{\n        // Use deterministic values to avoid hydration mismatch\n        const elements = [\n            {\n                width: 180,\n                height: 160,\n                left: 10,\n                top: 20,\n                duration: 15,\n                x: 30,\n                y: 40\n            },\n            {\n                width: 220,\n                height: 190,\n                left: 80,\n                top: 60,\n                duration: 18,\n                x: -25,\n                y: -30\n            },\n            {\n                width: 150,\n                height: 140,\n                left: 60,\n                top: 80,\n                duration: 12,\n                x: 35,\n                y: 25\n            },\n            {\n                width: 200,\n                height: 170,\n                left: 30,\n                top: 40,\n                duration: 20,\n                x: -40,\n                y: 35\n            },\n            {\n                width: 170,\n                height: 200,\n                left: 90,\n                top: 10,\n                duration: 16,\n                x: 20,\n                y: -45\n            },\n            {\n                width: 190,\n                height: 150,\n                left: 20,\n                top: 70,\n                duration: 14,\n                x: -30,\n                y: 20\n            },\n            {\n                width: 160,\n                height: 180,\n                left: 70,\n                top: 30,\n                duration: 22,\n                x: 45,\n                y: -25\n            },\n            {\n                width: 210,\n                height: 160,\n                left: 50,\n                top: 90,\n                duration: 17,\n                x: -20,\n                y: 30\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: elements.map((element, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                    className: \"absolute rounded-full opacity-10 blur-xl\",\n                    style: {\n                        backgroundColor: '#832ED3',\n                        width: element.width,\n                        height: element.height,\n                        left: element.left + '%',\n                        top: element.top + '%'\n                    },\n                    animate: {\n                        x: [\n                            0,\n                            element.x,\n                            0\n                        ],\n                        y: [\n                            0,\n                            element.y,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: element.duration,\n                        repeat: Infinity,\n                        repeatType: 'reverse',\n                        ease: \"easeInOut\"\n                    }\n                }, i, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 526,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 524,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Show upload analysis results for quick ATS check\n    if (builderMode === 'analyze' && showAnalysis && uploadAnalysis) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 557,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 556,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 560,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                        children: \"ATS Analysis Results\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Here's your comprehensive resume analysis and recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 568,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"quick\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 577,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: ()=>handleModeChange('upload'),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Enhance This Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: resetToCreateMode,\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Start Fresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 582,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 563,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 562,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 555,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show success screen if resume is generated\n    if (resumeGenerated && resumeData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 615,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 614,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                        initial: {\n                                            scale: 0\n                                        },\n                                        animate: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                            className: \"h-10 w-10 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                        children: \"Resume Generated Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300\",\n                                        children: \"Your AI-optimized resume is ready for download\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 628,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6 text-center\",\n                                                children: \"ATS Compatibility Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSScoreCircle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    score: atsScore || 75,\n                                                    size: 150\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-6\",\n                                                children: \"AI Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-left\",\n                                                children: suggestions.length > 0 ? suggestions.slice(0, 4).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                        className: \"flex items-start gap-3 p-3 bg-neural-purple/10 rounded-lg border border-neural-purple/20\",\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 10\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            delay: 0.8 + index * 0.1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-neural-purple rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: typeof suggestion === 'string' ? suggestion : suggestion.message || suggestion\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 23\n                                                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-center\",\n                                                    children: \"No specific recommendations at this time.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mb-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        formData: formData,\n                                        resumeData: resumeData,\n                                        templateId: selectedTemplate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFDownload__WEBPACK_IMPORTED_MODULE_6__.ViewResumeButton, {\n                                        formData: formData,\n                                        resumeData: resumeData,\n                                        templateId: selectedTemplate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: ()=>setShowPreview(!showPreview),\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 text-neural-blue hover:text-white font-medium rounded-xl transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? 'Hide Preview' : 'View Preview'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                        onClick: ()=>{\n                                            setResumeGenerated(false);\n                                            setResumeData(null);\n                                            setAtsScore(null);\n                                            setSuggestions([]);\n                                            setCurrentStep(0);\n                                        },\n                                        className: \"inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create Another Resume\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 693,\n                                columnNumber: 13\n                            }, undefined),\n                            showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        formData: formData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 740,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 621,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 620,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 612,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormHeader__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                steps: steps,\n                onBack: ()=>window.history.back(),\n                onPreview: ()=>setShowPreview(!showPreview),\n                onSave: ()=>console.log('Save draft'),\n                showPreview: showPreview\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 760,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 772,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 771,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 775,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 777,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        className: \"text-center mb-8 md:mb-12 relative z-20\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-4 md:mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                className: \"h-8 w-8 md:h-10 md:w-10 text-neural-pink animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 792,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl lg:text-5xl font-bold text-white\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 790,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-4\",\n                                children: \"Create professional, ATS-friendly resumes in minutes with our AI-powered builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 799,\n                                columnNumber: 11\n                            }, undefined),\n                            builderMode === 'create' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            \"Step \",\n                                            currentStep + 1,\n                                            \" of \",\n                                            steps.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 807,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 806,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 805,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 784,\n                        columnNumber: 9\n                    }, undefined),\n                    (builderMode !== 'create' || currentStep === 0) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeBuilderModeToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        currentMode: builderMode,\n                        onModeChange: handleModeChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 815,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'upload' || builderMode === 'analyze') && !showAnalysis && !showJobDescriptionInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            onAnalysisComplete: handleUploadAnalysis,\n                            analysisType: builderMode === 'analyze' ? 'quick' : 'full'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 828,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 823,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showJobDescriptionInput && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-2xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Uploaded Successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Now provide the job description to create a targeted, ATS-optimized resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 842,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                onJobDescriptionSubmit: handleJobDescriptionSubmit,\n                                isLoading: isGeneratingTargeted\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 847,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                    onClick: ()=>{\n                                        setShowJobDescriptionInput(false);\n                                        setCurrentStep(0);\n                                    },\n                                    className: \"inline-flex items-center gap-2 px-6 py-3 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: \"Skip Job Targeting\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 852,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 837,\n                        columnNumber: 11\n                    }, undefined),\n                    builderMode === 'upload' && showAnalysis && uploadAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-4xl mx-auto mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"Resume Analysis Complete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Review the extracted information and continue to enhance your resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 875,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSAnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                analysisData: uploadAnalysis,\n                                analysisType: \"full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 880,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                    onClick: ()=>setCurrentStep(0),\n                                    className: \"inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Continue to Form Editor\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 885,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 870,\n                        columnNumber: 11\n                    }, undefined),\n                    (builderMode === 'create' || builderMode === 'upload' && currentStep >= 0 && !showJobDescriptionInput) && !resumeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                className: \"mb-8 md:mb-12\",\n                                initial: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StepIndicator__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    currentStep: currentStep,\n                                    totalSteps: steps.length,\n                                    steps: steps,\n                                    onStepClick: (stepIndex)=>{\n                                        if (stepIndex <= currentStep) {\n                                            setCurrentStep(stepIndex);\n                                        }\n                                    },\n                                    allowClickNavigation: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/10 shadow-2xl\",\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 md:mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(steps[currentStep].icon, {\n                                                                    className: \"h-6 w-6 md:h-7 md:w-7 text-neural-purple\"\n                                                                }),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl md:text-2xl font-bold text-white\",\n                                                                    children: steps[currentStep].title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm md:text-base\",\n                                                            children: steps[currentStep].description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 935,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_37__.AnimatePresence, {\n                                                    mode: \"wait\",\n                                                    children: [\n                                                        currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonalInfoForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EducationForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__.ExperienceForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__.SkillsProjectsForm, {\n                                                            formData: formData,\n                                                            updateFormData: updateFormData,\n                                                            addArrayItem: addArrayItem,\n                                                            removeArrayItem: removeArrayItem,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_23__.ReviewForm, {\n                                                            formData: formData,\n                                                            atsAnalysis: atsAnalysis\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 39\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 949,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 927,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-4 md:p-6 border border-white/10 sticky top-4 md:top-8 shadow-2xl\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 0.4\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4 md:mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-base md:text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                        className: \"h-4 w-4 md:h-5 md:w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 969,\n                                                                        columnNumber: 19\n                                                                    }, undefined),\n                                                                    \"Live Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 968,\n                                                                columnNumber: 17\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.button, {\n                                                                onClick: ()=>setShowPreview(!showPreview),\n                                                                className: \"px-3 py-1.5 md:px-4 md:py-2 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 rounded-lg text-neural-blue hover:text-white transition-all duration-300 text-sm font-medium\",\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                children: showPreview ? 'Hide' : 'Show'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 972,\n                                                                columnNumber: 17\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-[600px] md:max-h-[700px] overflow-y-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedResumePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            formData: formData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 984,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 17\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12 md:py-16 text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                            initial: {\n                                                                scale: 0.8,\n                                                                opacity: 0\n                                                            },\n                                                            animate: {\n                                                                scale: 1,\n                                                                opacity: 1\n                                                            },\n                                                            transition: {\n                                                                duration: 0.5\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                    className: \"h-16 w-16 md:h-20 md:w-20 mx-auto mb-4 opacity-30\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 993,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm md:text-base mb-2\",\n                                                                    children: \"Preview your resume\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 994,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs md:text-sm text-gray-500\",\n                                                                    children: 'Click \"Show\" to see live updates'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 961,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                                                className: \"mt-6\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 0.6\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSOptimizationPanel__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        formData: formData,\n                                                        atsScore: atsAnalysis.overallScore,\n                                                        suggestions: atsAnalysis.recommendations,\n                                                        realTimeAnalysis: atsAnalysis\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1008,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 960,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 925,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pb-24\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1021,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TemplateSelector__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        selectedTemplate: selectedTemplate,\n                        onTemplateSelect: setSelectedTemplate,\n                        onClose: ()=>setShowTemplateSelector(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1027,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StickyNavigation__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        currentStep: currentStep,\n                        totalSteps: steps.length,\n                        onPrevious: ()=>{\n                            if (currentStep > 0) {\n                                setCurrentStep(currentStep - 1);\n                            }\n                        },\n                        onNext: ()=>{\n                            if (currentStep < steps.length - 1) {\n                                setCurrentStep(currentStep + 1);\n                            }\n                        },\n                        onGenerate: generateResume,\n                        isGenerating: isGenerating,\n                        canProceed: true,\n                        steps: steps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1035,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 782,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 758,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"ov9cNX+p3PbbnqwGct0srf01HjQ=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    ];\n});\n_c = ResumeBuilder;\n// Personal Information Form Component\nconst PersonalInfoForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis } = param;\n    var _atsAnalysis_fieldAnalysis, _atsAnalysis_fieldAnalysis1, _atsAnalysis_fieldAnalysis2, _atsAnalysis_fieldAnalysis3;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6 md:space-y-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                \"First Name *\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSTooltip__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    fieldType: \"firstName\",\n                                    className: \"ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1068,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.firstName,\n                            onChange: (e)=>updateFormData('personal', 'firstName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Rahul\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1072,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"firstName\",\n                                value: formData.personal.firstName,\n                                analysis: (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis.firstName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1080,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1079,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1067,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Last Name *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1089,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.lastName,\n                            onChange: (e)=>updateFormData('personal', 'lastName', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Sharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1092,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"lastName\",\n                                value: formData.personal.lastName,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis1 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis1 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis1.lastName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1100,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1099,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1088,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1110,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Email *\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1109,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            value: formData.personal.email,\n                            onChange: (e)=>updateFormData('personal', 'email', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"<EMAIL>\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1113,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"email\",\n                                value: formData.personal.email,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis2 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis2 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis2.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1121,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1120,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1108,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1131,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Phone\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1130,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"tel\",\n                            value: formData.personal.phone,\n                            onChange: (e)=>updateFormData('personal', 'phone', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"+91 98765 43210\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1134,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1129,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1145,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Location\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1144,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: formData.personal.location,\n                            onChange: (e)=>updateFormData('personal', 'location', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Mumbai, Maharashtra\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1148,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1143,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"LinkedIn\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1158,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.linkedin,\n                            onChange: (e)=>updateFormData('personal', 'linkedin', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://linkedin.com/in/rahulsharma\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1162,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1157,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                    className: \"inline h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 1173,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Portfolio/Website\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1172,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"url\",\n                            value: formData.personal.portfolio,\n                            onChange: (e)=>updateFormData('personal', 'portfolio', e.target.value),\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"https://rahulsharma.dev\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1176,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1171,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm md:text-base font-medium text-gray-300 mb-2 md:mb-3\",\n                            children: \"Professional Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1186,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: formData.personal.summary,\n                            onChange: (e)=>updateFormData('personal', 'summary', e.target.value),\n                            rows: 4,\n                            className: \"w-full px-4 md:px-5 py-3 md:py-4 bg-gray-800/60 border border-gray-600 rounded-xl focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 resize-none transition-all duration-300 hover:border-gray-500 text-sm md:text-base\",\n                            placeholder: \"Experienced software engineer with 5+ years in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable solutions and leading cross-functional teams...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1189,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                fieldName: \"summary\",\n                                value: formData.personal.summary,\n                                analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis3 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis3 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis3.summary,\n                                showDetails: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1197,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1196,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIContentSuggestions__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                fieldType: \"summary\",\n                                currentValue: formData.personal.summary,\n                                onSuggestionApply: (suggestion)=>updateFormData('personal', 'summary', suggestion),\n                                context: {\n                                    firstName: formData.personal.firstName,\n                                    lastName: formData.personal.lastName,\n                                    experience: formData.experience,\n                                    skills: formData.skills\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1207,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 1206,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 1185,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 1066,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1060,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PersonalInfoForm;\n// Education Form Component\nconst EducationForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem, atsAnalysis } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"h-6 w-6 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1234,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1235,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1233,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addArrayItem('education', {\n                                degree: \"\",\n                                institution: \"\",\n                                location: \"\",\n                                startDate: \"\",\n                                endDate: \"\",\n                                gpa: \"\",\n                                relevant: \"\"\n                            }),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1249,\n                                columnNumber: 9\n                            }, undefined),\n                            \"Add Education\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1237,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1232,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: formData.education.map((edu, index)=>{\n                    var _atsAnalysis_fieldAnalysis, _atsAnalysis_fieldAnalysis1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 p-6 rounded-lg border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Education \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeArrayItem('education', edu.id),\n                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1260,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1257,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Degree *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1271,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.degree,\n                                                onChange: (e)=>updateFormData('education', 'degree', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"Bachelor of Science\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    fieldName: \"degree\",\n                                                    value: edu.degree,\n                                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis[\"education_degree_\".concat(index)]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1282,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1281,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1270,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Institution *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1291,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.institution,\n                                                onChange: (e)=>updateFormData('education', 'institution', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"University of Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1294,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ATSFieldIndicator__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    fieldName: \"institution\",\n                                                    value: edu.institution,\n                                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis1 = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis1 === void 0 ? void 0 : _atsAnalysis_fieldAnalysis1[\"education_institution_\".concat(index)]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1301,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1290,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1312,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Location\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1311,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.location,\n                                                onChange: (e)=>updateFormData('education', 'location', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"New York, NY\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1315,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1310,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"GPA (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.gpa,\n                                                onChange: (e)=>updateFormData('education', 'gpa', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"3.8/4.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1328,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1324,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1339,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Start Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1338,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.startDate,\n                                                onChange: (e)=>updateFormData('education', 'startDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1342,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1337,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Sparkles_Trash2_Upload_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 1352,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"End Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1351,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.endDate,\n                                                onChange: (e)=>updateFormData('education', 'endDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1355,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1350,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Relevant Coursework/Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1364,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: edu.relevant,\n                                                onChange: (e)=>updateFormData('education', 'relevant', e.target.value, index),\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"Relevant coursework, honors, achievements...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 1367,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 1363,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 1269,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, edu.id, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 1256,\n                        columnNumber: 9\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 1254,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 1226,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EducationForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ResumeBuilder\");\n$RefreshReg$(_c1, \"PersonalInfoForm\");\n$RefreshReg$(_c2, \"EducationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcmVzdW1lLWJ1aWxkZXIvcGFnZS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDd0M7QUFDZ0I7QUF1QmxDO0FBQ21DO0FBQ2pCO0FBQ1c7QUFDTTtBQUNjO0FBQ0U7QUFDRTtBQUN0QjtBQUNZO0FBQ0U7QUFDSTtBQUNGO0FBQ0E7QUFDUjtBQUNrQjtBQUMzQjtBQUNXO0FBQ2Q7QUFDQTtBQUNNO0FBQ007QUFDWjtBQUNqRCwwREFBMEQ7QUFDeUM7QUFFbkcsTUFBTW1ELGdCQUFnQjs7SUFDcEIsd0JBQXdCO0lBQ3hCLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHcEQsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDcUQsY0FBY0MsZ0JBQWdCLEdBQUd0RCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN1RCxpQkFBaUJDLG1CQUFtQixHQUFHeEQsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDeUQsaUJBQWlCQyxtQkFBbUIsR0FBRzFELCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzJELFdBQVdDLGFBQWEsR0FBRzVELCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzZELFlBQVlDLGNBQWMsR0FBRzlELCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQytELFVBQVVDLFlBQVksR0FBR2hFLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ2lFLGFBQWFDLGVBQWUsR0FBR2xFLCtDQUFRQSxDQUFDLEVBQUU7SUFDakQsTUFBTSxDQUFDbUUsYUFBYUMsZUFBZSxHQUFHcEUsK0NBQVFBLENBQUM7SUFFL0MscUNBQXFDO0lBQ3JDLE1BQU0sQ0FBQ3FFLGFBQWFDLGVBQWUsR0FBR3RFLCtDQUFRQSxDQUFDLFdBQVcsZ0NBQWdDO0lBQzFGLE1BQU0sQ0FBQ3VFLGdCQUFnQkMsa0JBQWtCLEdBQUd4RSwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUN5RSxjQUFjQyxnQkFBZ0IsR0FBRzFFLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzJFLG9CQUFvQkMsc0JBQXNCLEdBQUc1RSwrQ0FBUUEsQ0FBQztJQUU3RCxpQ0FBaUM7SUFDakMsTUFBTSxDQUFDNkUseUJBQXlCQywyQkFBMkIsR0FBRzlFLCtDQUFRQSxDQUFDO0lBQ3ZFLE1BQU0sQ0FBQytFLHNCQUFzQkMsd0JBQXdCLEdBQUdoRiwrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNpRixvQkFBb0JDLHNCQUFzQixHQUFHbEYsK0NBQVFBLENBQUM7SUFFN0QsMkJBQTJCO0lBQzNCLE1BQU0sQ0FBQ21GLGtCQUFrQkMsb0JBQW9CLEdBQUdwRiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNxRixzQkFBc0JDLHdCQUF3QixHQUFHdEYsK0NBQVFBLENBQUM7SUFFakUsa0JBQWtCO0lBQ2xCLE1BQU0sQ0FBQ3VGLFVBQVVDLFlBQVksR0FBR3hGLCtDQUFRQSxDQUFDO1FBQ3ZDeUYsVUFBVTtZQUNSQyxXQUFXO1lBQ1hDLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxPQUFPO1lBQ1BDLFVBQVU7WUFDVkMsVUFBVTtZQUNWQyxXQUFXO1lBQ1hDLFNBQVM7UUFDWDtRQUNBQyxXQUFXO1lBQ1Q7Z0JBQ0VDLElBQUk7Z0JBQ0pDLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JQLFVBQVU7Z0JBQ1ZRLFdBQVc7Z0JBQ1hDLFNBQVM7Z0JBQ1RDLEtBQUs7Z0JBQ0xDLFVBQVU7WUFDWjtTQUNEO1FBQ0RDLFlBQVk7WUFDVjtnQkFDRVAsSUFBSTtnQkFDSlEsT0FBTztnQkFDUEMsU0FBUztnQkFDVGQsVUFBVTtnQkFDVlEsV0FBVztnQkFDWEMsU0FBUztnQkFDVE0sU0FBUztnQkFDVEMsYUFBYTtZQUNmO1NBQ0Q7UUFDREMsUUFBUTtZQUNOQyxXQUFXLEVBQUU7WUFDYkMsV0FBVyxFQUFFO1lBQ2JDLGdCQUFnQixFQUFFO1FBQ3BCO1FBQ0FDLFVBQVU7WUFDUjtnQkFDRWhCLElBQUk7Z0JBQ0ppQixNQUFNO2dCQUNOTixhQUFhO2dCQUNiTyxjQUFjO2dCQUNkQyxNQUFNO1lBQ1I7U0FDRDtJQUNIO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU1DLGNBQWMvRSxrRUFBY0EsQ0FBQytDO0lBRW5DLHNCQUFzQjtJQUN0QixNQUFNaUMsUUFBUTtRQUNaO1lBQ0VyQixJQUFJO1lBQ0pRLE9BQU87WUFDUGMsTUFBTXRILDRPQUFJQTtZQUNWMkcsYUFBYTtRQUNmO1FBQ0E7WUFDRVgsSUFBSTtZQUNKUSxPQUFPO1lBQ1BjLE1BQU1ySCw0T0FBYUE7WUFDbkIwRyxhQUFhO1FBQ2Y7UUFDQTtZQUNFWCxJQUFJO1lBQ0pRLE9BQU87WUFDUGMsTUFBTXBILDRPQUFTQTtZQUNmeUcsYUFBYTtRQUNmO1FBQ0E7WUFDRVgsSUFBSTtZQUNKUSxPQUFPO1lBQ1BjLE1BQU1uSCw0T0FBS0E7WUFDWHdHLGFBQWE7UUFDZjtRQUNBO1lBQ0VYLElBQUk7WUFDSlEsT0FBTztZQUNQYyxNQUFNbEgsNE9BQVFBO1lBQ2R1RyxhQUFhO1FBQ2Y7S0FDRDtJQUVELG1CQUFtQjtJQUNuQixNQUFNWSxpQkFBaUIsU0FBQ0MsU0FBU0MsT0FBT0M7WUFBT0MseUVBQVE7UUFDckR0QyxZQUFZdUMsQ0FBQUE7WUFDVixJQUFJRCxVQUFVLFFBQVFFLE1BQU1DLE9BQU8sQ0FBQ0YsSUFBSSxDQUFDSixRQUFRLEdBQUc7Z0JBQ2xELE1BQU1PLFdBQVc7dUJBQUlILElBQUksQ0FBQ0osUUFBUTtpQkFBQztnQkFDbkNPLFFBQVEsQ0FBQ0osTUFBTSxHQUFHO29CQUFFLEdBQUdJLFFBQVEsQ0FBQ0osTUFBTTtvQkFBRSxDQUFDRixNQUFNLEVBQUVDO2dCQUFNO2dCQUN2RCxPQUFPO29CQUFFLEdBQUdFLElBQUk7b0JBQUUsQ0FBQ0osUUFBUSxFQUFFTztnQkFBUztZQUN4QyxPQUFPLElBQUksT0FBT0gsSUFBSSxDQUFDSixRQUFRLEtBQUssWUFBWSxDQUFDSyxNQUFNQyxPQUFPLENBQUNGLElBQUksQ0FBQ0osUUFBUSxHQUFHO2dCQUM3RSxPQUFPO29CQUNMLEdBQUdJLElBQUk7b0JBQ1AsQ0FBQ0osUUFBUSxFQUFFO3dCQUFFLEdBQUdJLElBQUksQ0FBQ0osUUFBUTt3QkFBRSxDQUFDQyxNQUFNLEVBQUVDO29CQUFNO2dCQUNoRDtZQUNGO1lBQ0EsT0FBT0U7UUFDVDtJQUNGO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU1JLGVBQWUsQ0FBQ1IsU0FBU1M7UUFDN0I1QyxZQUFZdUMsQ0FBQUEsT0FBUztnQkFDbkIsR0FBR0EsSUFBSTtnQkFDUCxDQUFDSixRQUFRLEVBQUU7dUJBQUlJLElBQUksQ0FBQ0osUUFBUTtvQkFBRTt3QkFBRSxHQUFHUyxRQUFRO3dCQUFFakMsSUFBSWtDLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHO29CQUFJO2lCQUFFO1lBQ2pHO0lBQ0Y7SUFFQSxrQ0FBa0M7SUFDbEMsTUFBTUMsa0JBQWtCLENBQUNkLFNBQVN4QjtRQUNoQ1gsWUFBWXVDLENBQUFBLE9BQVM7Z0JBQ25CLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0osUUFBUSxFQUFFSSxJQUFJLENBQUNKLFFBQVEsQ0FBQ2UsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLeEMsRUFBRSxLQUFLQTtZQUN0RDtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU15QyxXQUFXO1FBQ2YsSUFBSXpGLGNBQWNxRSxNQUFNcUIsTUFBTSxHQUFHLEdBQUc7WUFDbEN6RixlQUFlRCxjQUFjO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNMkYsV0FBVztRQUNmLElBQUkzRixjQUFjLEdBQUc7WUFDbkJDLGVBQWVELGNBQWM7UUFDL0I7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNNEYsaUJBQWlCO1FBQ3JCLElBQUk7Z0JBd0MyQkMsMkJBQUFBLGtCQUNNQSw0QkFBQUE7WUF4Q25DQyxRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQjNEO1lBRTdCakMsZ0JBQWdCO1lBQ2hCRSxtQkFBbUI7WUFFbkIsTUFBTTJGLFdBQVcsTUFBTUMsTUFBTSx3QkFBd0I7Z0JBQ25EQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJsRTtvQkFDQW1FLFlBQVl2RTtnQkFDZDtZQUNGO1lBRUE4RCxRQUFRQyxHQUFHLENBQUMsMkJBQTJCQyxTQUFTUSxNQUFNO1lBQ3REVixRQUFRQyxHQUFHLENBQUMsNEJBQTRCVSxPQUFPQyxXQUFXLENBQUNWLFNBQVNHLE9BQU8sQ0FBQ1EsT0FBTztZQUVuRixxQ0FBcUM7WUFDckMsTUFBTUMsY0FBY1osU0FBU0csT0FBTyxDQUFDVSxHQUFHLENBQUM7WUFDekMsSUFBSSxDQUFDRCxlQUFlLENBQUNBLFlBQVlFLFFBQVEsQ0FBQyxxQkFBcUI7Z0JBQzdELE1BQU1DLGVBQWUsTUFBTWYsU0FBU2dCLElBQUk7Z0JBQ3hDbEIsUUFBUW1CLEtBQUssQ0FBQyxpQ0FBaUNGO2dCQUMvQyxNQUFNLElBQUlHLE1BQU07WUFDbEI7WUFFQSxNQUFNckIsT0FBTyxNQUFNRyxTQUFTbUIsSUFBSTtZQUNoQ3JCLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJGO1lBRXJDLElBQUksQ0FBQ0csU0FBU29CLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJRixNQUFNckIsS0FBS29CLEtBQUssSUFBSTtZQUNoQztZQUVBLHFEQUFxRDtZQUNyRG5CLFFBQVFDLEdBQUcsQ0FBQztZQUNadEYsYUFBYW9GLEtBQUt3QixXQUFXO1lBQzdCMUcsY0FBY2tGLEtBQUtuRixVQUFVO1lBQzdCRyxZQUFZZ0YsS0FBS2pGLFFBQVEsTUFBSWlGLG1CQUFBQSxLQUFLbkYsVUFBVSxjQUFmbUYsd0NBQUFBLDRCQUFBQSxpQkFBaUJqRixRQUFRLGNBQXpCaUYsZ0RBQUFBLDBCQUEyQnlCLE9BQU8sS0FBSTtZQUNuRXZHLGVBQWU4RSxLQUFLL0UsV0FBVyxNQUFJK0Usb0JBQUFBLEtBQUtuRixVQUFVLGNBQWZtRix5Q0FBQUEsNkJBQUFBLGtCQUFpQmpGLFFBQVEsY0FBekJpRixpREFBQUEsMkJBQTJCMEIsWUFBWSxLQUFJLEVBQUU7WUFFaEZ6QixRQUFRQyxHQUFHLENBQUM7UUFFZCxFQUFFLE9BQU9rQixPQUFPO1lBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0NuQixRQUFRbUIsS0FBSyxDQUFDLGtCQUFrQjtnQkFDOUJPLFNBQVNQLE1BQU1PLE9BQU87Z0JBQ3RCQyxPQUFPUixNQUFNUSxLQUFLO2dCQUNsQnhELE1BQU1nRCxNQUFNaEQsSUFBSTtZQUNsQjtZQUVBM0Ysa0RBQUtBLENBQUMySSxLQUFLLENBQUNBLE1BQU1PLE9BQU8sSUFBSTtZQUM3QnJILGdCQUFnQjtZQUNoQkUsbUJBQW1CO1FBQ3JCO0lBQ0Y7SUFFQSxNQUFNcUgseUJBQXlCO1FBQzdCckgsbUJBQW1CO1FBQ25CRSxtQkFBbUI7UUFDbkJKLGdCQUFnQjtRQUVoQixzQ0FBc0M7UUFDdEMsTUFBTXdILGVBQWUvRyxXQUNqQixnQ0FBeUMsT0FBVEEsVUFBUyxPQUN6QztRQUNKdEMsa0RBQUtBLENBQUNzSixPQUFPLENBQUNEO0lBQ2hCO0lBRUEsb0NBQW9DO0lBQ3BDLE1BQU1FLHVCQUF1QixDQUFDQztRQUM1QmhDLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBZ0MrQjtRQUM1Q3pHLGtCQUFrQnlHO1FBRWxCLHdEQUF3RDtRQUN4RCxJQUFJNUcsZ0JBQWdCLFVBQVU7WUFDNUJTLDJCQUEyQjtRQUM3QixPQUFPO1lBQ0wsK0NBQStDO1lBQy9DSixnQkFBZ0I7UUFDbEI7UUFFQSx1RUFBdUU7UUFDdkUsSUFBSXVHLGFBQWFDLFlBQVksS0FBSyxVQUFVRCxhQUFhRSxhQUFhLEVBQUU7WUFDdEUsTUFBTUMsWUFBWUgsYUFBYUUsYUFBYTtZQUM1Q2xDLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMENrQztZQUV0RCw4Q0FBOEM7WUFDOUM1RixZQUFZNkYsQ0FBQUE7b0JBSUdELHFCQUNEQSxzQkFDSEEsc0JBQ0FBLHNCQUNHQSxzQkFDQUEsc0JBQ0NBLHNCQUNGQSxzQkFFQUEsc0JBSUNBLHVCQUtDQSw2QkFBQUEsbUJBQ0FBLDZCQUFBQSxvQkFDS0Esa0NBQUFBLG9CQUVSQTt1QkExQmE7b0JBQ3ZCLEdBQUdDLFFBQVE7b0JBQ1g1RixVQUFVO3dCQUNSLEdBQUc0RixTQUFTNUYsUUFBUTt3QkFDcEJDLFdBQVcwRixFQUFBQSxzQkFBQUEsVUFBVTNGLFFBQVEsY0FBbEIyRiwwQ0FBQUEsb0JBQW9CMUYsU0FBUyxLQUFJMkYsU0FBUzVGLFFBQVEsQ0FBQ0MsU0FBUzt3QkFDdkVDLFVBQVV5RixFQUFBQSx1QkFBQUEsVUFBVTNGLFFBQVEsY0FBbEIyRiwyQ0FBQUEscUJBQW9CekYsUUFBUSxLQUFJMEYsU0FBUzVGLFFBQVEsQ0FBQ0UsUUFBUTt3QkFDcEVDLE9BQU93RixFQUFBQSx1QkFBQUEsVUFBVTNGLFFBQVEsY0FBbEIyRiwyQ0FBQUEscUJBQW9CeEYsS0FBSyxLQUFJeUYsU0FBUzVGLFFBQVEsQ0FBQ0csS0FBSzt3QkFDM0RDLE9BQU91RixFQUFBQSx1QkFBQUEsVUFBVTNGLFFBQVEsY0FBbEIyRiwyQ0FBQUEscUJBQW9CdkYsS0FBSyxLQUFJd0YsU0FBUzVGLFFBQVEsQ0FBQ0ksS0FBSzt3QkFDM0RDLFVBQVVzRixFQUFBQSx1QkFBQUEsVUFBVTNGLFFBQVEsY0FBbEIyRiwyQ0FBQUEscUJBQW9CdEYsUUFBUSxLQUFJdUYsU0FBUzVGLFFBQVEsQ0FBQ0ssUUFBUTt3QkFDcEVDLFVBQVVxRixFQUFBQSx1QkFBQUEsVUFBVTNGLFFBQVEsY0FBbEIyRiwyQ0FBQUEscUJBQW9CckYsUUFBUSxLQUFJc0YsU0FBUzVGLFFBQVEsQ0FBQ00sUUFBUTt3QkFDcEVDLFdBQVdvRixFQUFBQSx1QkFBQUEsVUFBVTNGLFFBQVEsY0FBbEIyRiwyQ0FBQUEscUJBQW9CcEYsU0FBUyxLQUFJcUYsU0FBUzVGLFFBQVEsQ0FBQ08sU0FBUzt3QkFDdkVDLFNBQVNtRixFQUFBQSx1QkFBQUEsVUFBVTNGLFFBQVEsY0FBbEIyRiwyQ0FBQUEscUJBQW9CbkYsT0FBTyxLQUFJb0YsU0FBUzVGLFFBQVEsQ0FBQ1EsT0FBTztvQkFDbkU7b0JBQ0FDLFdBQVdrRixFQUFBQSx1QkFBQUEsVUFBVWxGLFNBQVMsY0FBbkJrRiwyQ0FBQUEscUJBQXFCdkMsTUFBTSxJQUFHLElBQUl1QyxVQUFVbEYsU0FBUyxDQUFDb0YsR0FBRyxDQUFDQyxDQUFBQSxNQUFROzRCQUMzRSxHQUFHQSxHQUFHOzRCQUNOcEYsSUFBSW9GLElBQUlwRixFQUFFLElBQUlxRixLQUFLQyxHQUFHLEtBQUtwRCxLQUFLQyxNQUFNO3dCQUN4QyxNQUFNK0MsU0FBU25GLFNBQVM7b0JBQ3hCUSxZQUFZMEUsRUFBQUEsd0JBQUFBLFVBQVUxRSxVQUFVLGNBQXBCMEUsNENBQUFBLHNCQUFzQnZDLE1BQU0sSUFBRyxJQUFJdUMsVUFBVTFFLFVBQVUsQ0FBQzRFLEdBQUcsQ0FBQ0ksQ0FBQUEsTUFBUTs0QkFDOUUsR0FBR0EsR0FBRzs0QkFDTnZGLElBQUl1RixJQUFJdkYsRUFBRSxJQUFJcUYsS0FBS0MsR0FBRyxLQUFLcEQsS0FBS0MsTUFBTTt3QkFDeEMsTUFBTStDLFNBQVMzRSxVQUFVO29CQUN6QkssUUFBUTt3QkFDTkMsV0FBV29FLEVBQUFBLG9CQUFBQSxVQUFVckUsTUFBTSxjQUFoQnFFLHlDQUFBQSw4QkFBQUEsa0JBQWtCcEUsU0FBUyxjQUEzQm9FLGtEQUFBQSw0QkFBNkJ2QyxNQUFNLElBQUcsSUFBSXVDLFVBQVVyRSxNQUFNLENBQUNDLFNBQVMsR0FBR3FFLFNBQVN0RSxNQUFNLENBQUNDLFNBQVM7d0JBQzNHQyxXQUFXbUUsRUFBQUEscUJBQUFBLFVBQVVyRSxNQUFNLGNBQWhCcUUsMENBQUFBLDhCQUFBQSxtQkFBa0JuRSxTQUFTLGNBQTNCbUUsa0RBQUFBLDRCQUE2QnZDLE1BQU0sSUFBRyxJQUFJdUMsVUFBVXJFLE1BQU0sQ0FBQ0UsU0FBUyxHQUFHb0UsU0FBU3RFLE1BQU0sQ0FBQ0UsU0FBUzt3QkFDM0dDLGdCQUFnQmtFLEVBQUFBLHFCQUFBQSxVQUFVckUsTUFBTSxjQUFoQnFFLDBDQUFBQSxtQ0FBQUEsbUJBQWtCbEUsY0FBYyxjQUFoQ2tFLHVEQUFBQSxpQ0FBa0N2QyxNQUFNLElBQUcsSUFBSXVDLFVBQVVyRSxNQUFNLENBQUNHLGNBQWMsR0FBR21FLFNBQVN0RSxNQUFNLENBQUNHLGNBQWM7b0JBQ2pJO29CQUNBQyxVQUFVaUUsRUFBQUEsc0JBQUFBLFVBQVVqRSxRQUFRLGNBQWxCaUUsMENBQUFBLG9CQUFvQnZDLE1BQU0sSUFBRyxJQUFJdUMsVUFBVWpFLFFBQVEsQ0FBQ21FLEdBQUcsQ0FBQ0ssQ0FBQUEsT0FBUzs0QkFDekUsR0FBR0EsSUFBSTs0QkFDUHhGLElBQUl3RixLQUFLeEYsRUFBRSxJQUFJcUYsS0FBS0MsR0FBRyxLQUFLcEQsS0FBS0MsTUFBTTt3QkFDekMsTUFBTStDLFNBQVNsRSxRQUFRO2dCQUN6Qjs7WUFFQSxnQ0FBZ0M7WUFDaEMsSUFBSThELGFBQWFsSCxRQUFRLEVBQUU7b0JBRVZrSCw0QkFBMENBO2dCQUR6RGpILFlBQVlpSCxhQUFhbEgsUUFBUSxDQUFDMEcsT0FBTztnQkFDekN2RyxlQUFlK0csRUFBQUEsNkJBQUFBLGFBQWFXLFlBQVksY0FBekJYLGlEQUFBQSwyQkFBMkJoSCxXQUFXLE9BQUlnSCx5QkFBQUEsYUFBYVksUUFBUSxjQUFyQlosNkNBQUFBLHVCQUF1QmEsZUFBZSxLQUFJLEVBQUU7WUFDdkc7WUFFQTdDLFFBQVFDLEdBQUcsQ0FBQztRQUNkLE9BQU8sSUFBSStCLGFBQWFjLFFBQVEsRUFBRTtZQUNoQzlDLFFBQVFDLEdBQUcsQ0FBQztZQUNaLCtEQUErRDtZQUMvRCxJQUFJK0IsYUFBYUUsYUFBYSxFQUFFO2dCQUM5QixNQUFNQyxZQUFZSCxhQUFhRSxhQUFhO2dCQUM1QzNGLFlBQVk2RixDQUFBQTt3QkFJQ0Q7MkJBSlk7d0JBQ3ZCLEdBQUdDLFFBQVE7d0JBQ1g1RixVQUFVOzRCQUNSLEdBQUc0RixTQUFTNUYsUUFBUTs0QkFDcEJRLFNBQVNtRixFQUFBQSxzQkFBQUEsVUFBVTNGLFFBQVEsY0FBbEIyRiwwQ0FBQUEsb0JBQW9CbkYsT0FBTyxLQUFJO3dCQUMxQztvQkFDRjs7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxtRUFBbUU7SUFDbkUsTUFBTStGLDZCQUE2QixPQUFPQztRQUN4QyxJQUFJLEVBQUMxSCwyQkFBQUEscUNBQUFBLGVBQWdCNEcsYUFBYSxHQUFFO1lBQ2xDMUosa0RBQUtBLENBQUMySSxLQUFLLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSTtZQUNGcEYsd0JBQXdCO1lBQ3hCaUUsUUFBUUMsR0FBRyxDQUFDLGdEQUFnRCtDO1lBRTVELE1BQU05QyxXQUFXLE1BQU1DLE1BQU0saUNBQWlDO2dCQUM1REMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CeUMscUJBQXFCM0gsZUFBZTRHLGFBQWE7b0JBQ2pEZ0IsZ0JBQWdCRixRQUFRbkYsV0FBVztvQkFDbkNzRixVQUFVSCxRQUFRRyxRQUFRO29CQUMxQnhGLFNBQVNxRixRQUFRckYsT0FBTztnQkFDMUI7WUFDRjtZQUVBLE1BQU1vQyxPQUFPLE1BQU1HLFNBQVNtQixJQUFJO1lBQ2hDckIsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0Y7WUFFNUMsSUFBSSxDQUFDRyxTQUFTb0IsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlGLE1BQU1yQixLQUFLb0IsS0FBSyxJQUFJO1lBQ2hDO1lBRUEsd0NBQXdDO1lBQ3hDLElBQUlwQixLQUFLcUQsY0FBYyxFQUFFO29CQUlSckQsK0JBQ0RBLGdDQUNIQSxnQ0FDQUEsZ0NBQ0dBLGdDQUNBQSxnQ0FDQ0EsZ0NBQ0ZBLGdDQUVBQSxnQ0FZQ0EsaUNBYUNBLDZCQUNBQSw4QkFDS0EsOEJBRVJBLCtCQWFBQTtnQkF0RFosMkVBQTJFO2dCQUMzRSxNQUFNc0QsbUJBQW1CO29CQUN2QjdHLFVBQVU7d0JBQ1JDLFdBQVdzRCxFQUFBQSxnQ0FBQUEsS0FBS3FELGNBQWMsQ0FBQzVHLFFBQVEsY0FBNUJ1RCxvREFBQUEsOEJBQThCdEQsU0FBUyxLQUFJO3dCQUN0REMsVUFBVXFELEVBQUFBLGlDQUFBQSxLQUFLcUQsY0FBYyxDQUFDNUcsUUFBUSxjQUE1QnVELHFEQUFBQSwrQkFBOEJyRCxRQUFRLEtBQUk7d0JBQ3BEQyxPQUFPb0QsRUFBQUEsaUNBQUFBLEtBQUtxRCxjQUFjLENBQUM1RyxRQUFRLGNBQTVCdUQscURBQUFBLCtCQUE4QnBELEtBQUssS0FBSTt3QkFDOUNDLE9BQU9tRCxFQUFBQSxpQ0FBQUEsS0FBS3FELGNBQWMsQ0FBQzVHLFFBQVEsY0FBNUJ1RCxxREFBQUEsK0JBQThCbkQsS0FBSyxLQUFJO3dCQUM5Q0MsVUFBVWtELEVBQUFBLGlDQUFBQSxLQUFLcUQsY0FBYyxDQUFDNUcsUUFBUSxjQUE1QnVELHFEQUFBQSwrQkFBOEJsRCxRQUFRLEtBQUk7d0JBQ3BEQyxVQUFVaUQsRUFBQUEsaUNBQUFBLEtBQUtxRCxjQUFjLENBQUM1RyxRQUFRLGNBQTVCdUQscURBQUFBLCtCQUE4QmpELFFBQVEsS0FBSTt3QkFDcERDLFdBQVdnRCxFQUFBQSxpQ0FBQUEsS0FBS3FELGNBQWMsQ0FBQzVHLFFBQVEsY0FBNUJ1RCxxREFBQUEsK0JBQThCaEQsU0FBUyxLQUFJO3dCQUN0REMsU0FBUytDLEVBQUFBLGlDQUFBQSxLQUFLcUQsY0FBYyxDQUFDNUcsUUFBUSxjQUE1QnVELHFEQUFBQSwrQkFBOEIvQyxPQUFPLEtBQUk7b0JBQ3BEO29CQUNBQyxXQUFXOEMsRUFBQUEsaUNBQUFBLEtBQUtxRCxjQUFjLENBQUNuRyxTQUFTLGNBQTdCOEMscURBQUFBLCtCQUErQkgsTUFBTSxJQUFHLElBQy9DRyxLQUFLcUQsY0FBYyxDQUFDbkcsU0FBUyxDQUFDb0YsR0FBRyxDQUFDQyxDQUFBQSxNQUFROzRCQUN4Q3BGLElBQUlvRixJQUFJcEYsRUFBRSxJQUFJcUYsS0FBS0MsR0FBRyxLQUFLcEQsS0FBS0MsTUFBTTs0QkFDdENsQyxRQUFRbUYsSUFBSW5GLE1BQU0sSUFBSTs0QkFDdEJDLGFBQWFrRixJQUFJbEYsV0FBVyxJQUFJOzRCQUNoQ1AsVUFBVXlGLElBQUl6RixRQUFRLElBQUk7NEJBQzFCUSxXQUFXaUYsSUFBSWpGLFNBQVMsSUFBSTs0QkFDNUJDLFNBQVNnRixJQUFJaEYsT0FBTyxJQUFJOzRCQUN4QkMsS0FBSytFLElBQUkvRSxHQUFHLElBQUk7NEJBQ2hCQyxVQUFVOEUsSUFBSTlFLFFBQVEsSUFBSTt3QkFDNUIsTUFDQTt3QkFBQzs0QkFBRU4sSUFBSTs0QkFBR0MsUUFBUTs0QkFBSUMsYUFBYTs0QkFBSVAsVUFBVTs0QkFBSVEsV0FBVzs0QkFBSUMsU0FBUzs0QkFBSUMsS0FBSzs0QkFBSUMsVUFBVTt3QkFBRztxQkFBRTtvQkFDN0dDLFlBQVlzQyxFQUFBQSxrQ0FBQUEsS0FBS3FELGNBQWMsQ0FBQzNGLFVBQVUsY0FBOUJzQyxzREFBQUEsZ0NBQWdDSCxNQUFNLElBQUcsSUFDakRHLEtBQUtxRCxjQUFjLENBQUMzRixVQUFVLENBQUM0RSxHQUFHLENBQUNJLENBQUFBLE1BQVE7NEJBQ3pDdkYsSUFBSXVGLElBQUl2RixFQUFFLElBQUlxRixLQUFLQyxHQUFHLEtBQUtwRCxLQUFLQyxNQUFNOzRCQUN0QzNCLE9BQU8rRSxJQUFJL0UsS0FBSyxJQUFJOzRCQUNwQkMsU0FBUzhFLElBQUk5RSxPQUFPLElBQUk7NEJBQ3hCZCxVQUFVNEYsSUFBSTVGLFFBQVEsSUFBSTs0QkFDMUJRLFdBQVdvRixJQUFJcEYsU0FBUyxJQUFJOzRCQUM1QkMsU0FBU21GLElBQUluRixPQUFPLElBQUk7NEJBQ3hCTSxTQUFTNkUsSUFBSTdFLE9BQU8sSUFBSTs0QkFDeEJDLGFBQWE0RSxJQUFJNUUsV0FBVyxJQUFLNEUsQ0FBQUEsSUFBSWEsWUFBWSxHQUFHYixJQUFJYSxZQUFZLENBQUNDLElBQUksQ0FBQyxRQUFRLEVBQUM7d0JBQ3JGLE1BQ0E7d0JBQUM7NEJBQUVyRyxJQUFJOzRCQUFHUSxPQUFPOzRCQUFJQyxTQUFTOzRCQUFJZCxVQUFVOzRCQUFJUSxXQUFXOzRCQUFJQyxTQUFTOzRCQUFJTSxTQUFTOzRCQUFPQyxhQUFhO3dCQUFHO3FCQUFFO29CQUNsSEMsUUFBUTt3QkFDTkMsV0FBV2dDLEVBQUFBLDhCQUFBQSxLQUFLcUQsY0FBYyxDQUFDdEYsTUFBTSxjQUExQmlDLGtEQUFBQSw0QkFBNEJoQyxTQUFTLEtBQUksRUFBRTt3QkFDdERDLFdBQVcrQixFQUFBQSwrQkFBQUEsS0FBS3FELGNBQWMsQ0FBQ3RGLE1BQU0sY0FBMUJpQyxtREFBQUEsNkJBQTRCL0IsU0FBUyxLQUFJLEVBQUU7d0JBQ3REQyxnQkFBZ0I4QixFQUFBQSwrQkFBQUEsS0FBS3FELGNBQWMsQ0FBQ3RGLE1BQU0sY0FBMUJpQyxtREFBQUEsNkJBQTRCOUIsY0FBYyxLQUFJLEVBQUU7b0JBQ2xFO29CQUNBQyxVQUFVNkIsRUFBQUEsZ0NBQUFBLEtBQUtxRCxjQUFjLENBQUNsRixRQUFRLGNBQTVCNkIsb0RBQUFBLDhCQUE4QkgsTUFBTSxJQUFHLElBQzdDRyxLQUFLcUQsY0FBYyxDQUFDbEYsUUFBUSxDQUFDbUUsR0FBRyxDQUFDSyxDQUFBQSxPQUFTOzRCQUN4Q3hGLElBQUl3RixLQUFLeEYsRUFBRSxJQUFJcUYsS0FBS0MsR0FBRyxLQUFLcEQsS0FBS0MsTUFBTTs0QkFDdkNsQixNQUFNdUUsS0FBS3ZFLElBQUksSUFBSTs0QkFDbkJOLGFBQWE2RSxLQUFLN0UsV0FBVyxJQUFJOzRCQUNqQ08sY0FBY3NFLEtBQUt0RSxZQUFZLElBQUk7NEJBQ25DQyxNQUFNcUUsS0FBS3JFLElBQUksSUFBSTt3QkFDckIsTUFDQTt3QkFBQzs0QkFBRW5CLElBQUk7NEJBQUdpQixNQUFNOzRCQUFJTixhQUFhOzRCQUFJTyxjQUFjOzRCQUFJQyxNQUFNO3dCQUFHO3FCQUFFO2dCQUN4RTtnQkFFQTlCLFlBQVk4RztnQkFDWnBILHNCQUFzQjhEO2dCQUN0QmhGLFlBQVlnRixFQUFBQSxpQkFBQUEsS0FBS2pGLFFBQVEsY0FBYmlGLHFDQUFBQSxlQUFleUIsT0FBTyxLQUFJO2dCQUN0Q3ZHLGVBQWU4RSxLQUFLOEMsZUFBZSxJQUFJLEVBQUU7Z0JBRXpDLHdDQUF3QztnQkFDeENoSCwyQkFBMkI7Z0JBQzNCMUIsZUFBZTtnQkFDZjNCLGtEQUFLQSxDQUFDc0osT0FBTyxDQUFDO1lBQ2hCO1FBRUYsRUFBRSxPQUFPWCxPQUFPO1lBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkQzSSxrREFBS0EsQ0FBQzJJLEtBQUssQ0FBQ0EsTUFBTU8sT0FBTyxJQUFJO1FBQy9CLFNBQVU7WUFDUjNGLHdCQUF3QjtRQUMxQjtJQUNGO0lBRUEscUJBQXFCO0lBQ3JCLE1BQU15SCxtQkFBbUIsQ0FBQ0M7UUFDeEJwSSxlQUFlb0k7UUFDZmxJLGtCQUFrQjtRQUNsQkUsZ0JBQWdCO1FBQ2hCaEIsbUJBQW1CO1FBQ25CTixlQUFlO1FBQ2YwQiwyQkFBMkI7UUFDM0JFLHdCQUF3QjtRQUN4QkUsc0JBQXNCO1FBRXRCLHlDQUF5QztRQUN6QyxJQUFJd0gsU0FBUyxVQUFVO1lBQ3JCOUgsc0JBQXNCO1FBQ3hCLE9BQU87WUFDTEEsc0JBQXNCO1FBQ3hCO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTStILG9CQUFvQjtRQUN4QnJJLGVBQWU7UUFDZkUsa0JBQWtCO1FBQ2xCRSxnQkFBZ0I7UUFDaEJoQixtQkFBbUI7UUFDbkJOLGVBQWU7UUFDZm9DLFlBQVk7WUFDVkMsVUFBVTtnQkFDUkMsV0FBVztnQkFDWEMsVUFBVTtnQkFDVkMsT0FBTztnQkFDUEMsT0FBTztnQkFDUEMsVUFBVTtnQkFDVkMsVUFBVTtnQkFDVkMsV0FBVztnQkFDWEMsU0FBUztZQUNYO1lBQ0FDLFdBQVc7Z0JBQUM7b0JBQUVDLElBQUk7b0JBQUdDLFFBQVE7b0JBQUlDLGFBQWE7b0JBQUlQLFVBQVU7b0JBQUlRLFdBQVc7b0JBQUlDLFNBQVM7b0JBQUlDLEtBQUs7b0JBQUlDLFVBQVU7Z0JBQUc7YUFBRTtZQUNwSEMsWUFBWTtnQkFBQztvQkFBRVAsSUFBSTtvQkFBR1EsT0FBTztvQkFBSUMsU0FBUztvQkFBSWQsVUFBVTtvQkFBSVEsV0FBVztvQkFBSUMsU0FBUztvQkFBSU0sU0FBUztvQkFBT0MsYUFBYTtnQkFBRzthQUFFO1lBQzFIQyxRQUFRO2dCQUFFQyxXQUFXLEVBQUU7Z0JBQUVDLFdBQVcsRUFBRTtnQkFBRUMsZ0JBQWdCLEVBQUU7WUFBQztZQUMzREMsVUFBVTtnQkFBQztvQkFBRWhCLElBQUk7b0JBQUdpQixNQUFNO29CQUFJTixhQUFhO29CQUFJTyxjQUFjO29CQUFJQyxNQUFNO2dCQUFHO2FBQUU7UUFDOUU7SUFDRjtJQUVBLHFGQUFxRjtJQUNyRixNQUFNc0YsbUJBQW1CO1FBQ3ZCLHVEQUF1RDtRQUN2RCxNQUFNQyxXQUFXO1lBQ2Y7Z0JBQUVDLE9BQU87Z0JBQUtDLFFBQVE7Z0JBQUtDLE1BQU07Z0JBQUlDLEtBQUs7Z0JBQUlDLFVBQVU7Z0JBQUlDLEdBQUc7Z0JBQUlDLEdBQUc7WUFBRztZQUN6RTtnQkFBRU4sT0FBTztnQkFBS0MsUUFBUTtnQkFBS0MsTUFBTTtnQkFBSUMsS0FBSztnQkFBSUMsVUFBVTtnQkFBSUMsR0FBRyxDQUFDO2dCQUFJQyxHQUFHLENBQUM7WUFBRztZQUMzRTtnQkFBRU4sT0FBTztnQkFBS0MsUUFBUTtnQkFBS0MsTUFBTTtnQkFBSUMsS0FBSztnQkFBSUMsVUFBVTtnQkFBSUMsR0FBRztnQkFBSUMsR0FBRztZQUFHO1lBQ3pFO2dCQUFFTixPQUFPO2dCQUFLQyxRQUFRO2dCQUFLQyxNQUFNO2dCQUFJQyxLQUFLO2dCQUFJQyxVQUFVO2dCQUFJQyxHQUFHLENBQUM7Z0JBQUlDLEdBQUc7WUFBRztZQUMxRTtnQkFBRU4sT0FBTztnQkFBS0MsUUFBUTtnQkFBS0MsTUFBTTtnQkFBSUMsS0FBSztnQkFBSUMsVUFBVTtnQkFBSUMsR0FBRztnQkFBSUMsR0FBRyxDQUFDO1lBQUc7WUFDMUU7Z0JBQUVOLE9BQU87Z0JBQUtDLFFBQVE7Z0JBQUtDLE1BQU07Z0JBQUlDLEtBQUs7Z0JBQUlDLFVBQVU7Z0JBQUlDLEdBQUcsQ0FBQztnQkFBSUMsR0FBRztZQUFHO1lBQzFFO2dCQUFFTixPQUFPO2dCQUFLQyxRQUFRO2dCQUFLQyxNQUFNO2dCQUFJQyxLQUFLO2dCQUFJQyxVQUFVO2dCQUFJQyxHQUFHO2dCQUFJQyxHQUFHLENBQUM7WUFBRztZQUMxRTtnQkFBRU4sT0FBTztnQkFBS0MsUUFBUTtnQkFBS0MsTUFBTTtnQkFBSUMsS0FBSztnQkFBSUMsVUFBVTtnQkFBSUMsR0FBRyxDQUFDO2dCQUFJQyxHQUFHO1lBQUc7U0FDM0U7UUFFRCxxQkFDRSw4REFBQ0M7WUFBSUMsV0FBVTtzQkFDWlQsU0FBU3ZCLEdBQUcsQ0FBQyxDQUFDaUMsU0FBU0Msa0JBQ3RCLDhEQUFDdk4sa0RBQU1BLENBQUNvTixHQUFHO29CQUVUQyxXQUFVO29CQUNWRyxPQUFPO3dCQUNMQyxpQkFBaUI7d0JBQ2pCWixPQUFPUyxRQUFRVCxLQUFLO3dCQUNwQkMsUUFBUVEsUUFBUVIsTUFBTTt3QkFDdEJDLE1BQU1PLFFBQVFQLElBQUksR0FBRzt3QkFDckJDLEtBQUtNLFFBQVFOLEdBQUcsR0FBRztvQkFDckI7b0JBQ0FVLFNBQVM7d0JBQ1BSLEdBQUc7NEJBQUM7NEJBQUdJLFFBQVFKLENBQUM7NEJBQUU7eUJBQUU7d0JBQ3BCQyxHQUFHOzRCQUFDOzRCQUFHRyxRQUFRSCxDQUFDOzRCQUFFO3lCQUFFO29CQUN0QjtvQkFDQVEsWUFBWTt3QkFDVlYsVUFBVUssUUFBUUwsUUFBUTt3QkFDMUJXLFFBQVFDO3dCQUNSQyxZQUFZO3dCQUNaQyxNQUFNO29CQUNSO21CQWxCS1I7Ozs7Ozs7Ozs7SUF1QmY7SUFFQSxtREFBbUQ7SUFDbkQsSUFBSW5KLGdCQUFnQixhQUFhSSxnQkFBZ0JGLGdCQUFnQjtRQUMvRCxxQkFDRSw4REFBQzhJO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7OzhCQUdqQiw4REFBQ1Y7Ozs7OzhCQUVELDhEQUFDUztvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ3JOLGtEQUFNQSxDQUFDb04sR0FBRzt3QkFDVFksU0FBUzs0QkFBRUMsU0FBUzs0QkFBR2QsR0FBRzt3QkFBRzt3QkFDN0JPLFNBQVM7NEJBQUVPLFNBQVM7NEJBQUdkLEdBQUc7d0JBQUU7d0JBQzVCRSxXQUFVOzswQ0FFViw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDYTt3Q0FBR2IsV0FBVTtrREFBaUQ7Ozs7OztrREFHL0QsOERBQUNjO3dDQUFFZCxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUsvQiw4REFBQ3JMLHNFQUFrQkE7Z0NBQ2pCZ0osY0FBYzFHO2dDQUNkMkcsY0FBYTs7Ozs7OzBDQUdmLDhEQUFDbUM7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDck4sa0RBQU1BLENBQUNvTyxNQUFNO3dDQUNaQyxTQUFTLElBQU03QixpQkFBaUI7d0NBQ2hDYSxXQUFVO3dDQUNWaUIsWUFBWTs0Q0FBRUMsT0FBTzt3Q0FBSzt3Q0FDMUJDLFVBQVU7NENBQUVELE9BQU87d0NBQUs7OzBEQUV4Qiw4REFBQ25OLDRPQUFNQTtnREFBQ2lNLFdBQVU7Ozs7Ozs0Q0FBWTs7Ozs7OztrREFJaEMsOERBQUNyTixrREFBTUEsQ0FBQ29PLE1BQU07d0NBQ1pDLFNBQVMzQjt3Q0FDVFcsV0FBVTt3Q0FDVmlCLFlBQVk7NENBQUVDLE9BQU87d0NBQUs7d0NBQzFCQyxVQUFVOzRDQUFFRCxPQUFPO3dDQUFLOzswREFFeEIsOERBQUNsTiw0T0FBVUE7Z0RBQUNnTSxXQUFVOzs7Ozs7NENBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVFoRDtJQUVBLDZDQUE2QztJQUM3QyxJQUFJN0osbUJBQW1CSSxZQUFZO1FBQ2pDLHFCQUNFLDhEQUFDd0o7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7OEJBR2pCLDhEQUFDVjs7Ozs7OEJBRUQsOERBQUNTO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDck4sa0RBQU1BLENBQUNvTixHQUFHO3dCQUNUQyxXQUFVO3dCQUNWVyxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHZCxHQUFHO3dCQUFHO3dCQUM3Qk8sU0FBUzs0QkFBRU8sU0FBUzs0QkFBR2QsR0FBRzt3QkFBRTt3QkFDNUJRLFlBQVk7NEJBQUVWLFVBQVU7d0JBQUk7OzBDQUc1Qiw4REFBQ0c7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDck4sa0RBQU1BLENBQUNvTixHQUFHO3dDQUNUWSxTQUFTOzRDQUFFTyxPQUFPO3dDQUFFO3dDQUNwQmIsU0FBUzs0Q0FBRWEsT0FBTzt3Q0FBRTt3Q0FDcEJaLFlBQVk7NENBQUVWLFVBQVU7NENBQUt3QixPQUFPO3dDQUFJO3dDQUN4Q3BCLFdBQVU7a0RBRVYsNEVBQUM1TSw0T0FBUUE7NENBQUM0TSxXQUFVOzs7Ozs7Ozs7OztrREFHdEIsOERBQUNhO3dDQUFHYixXQUFVO2tEQUFzQzs7Ozs7O2tEQUdwRCw4REFBQ2M7d0NBQUVkLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7MENBTXZDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBRWIsOERBQUNyTixrREFBTUEsQ0FBQ29OLEdBQUc7d0NBQ1RDLFdBQVU7d0NBQ1ZXLFNBQVM7NENBQUVDLFNBQVM7NENBQUdmLEdBQUcsQ0FBQzt3Q0FBRzt3Q0FDOUJRLFNBQVM7NENBQUVPLFNBQVM7NENBQUdmLEdBQUc7d0NBQUU7d0NBQzVCUyxZQUFZOzRDQUFFVixVQUFVOzRDQUFLd0IsT0FBTzt3Q0FBSTs7MERBRXhDLDhEQUFDQztnREFBR3JCLFdBQVU7MERBQXlDOzs7Ozs7MERBQ3ZELDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQzNMLGtFQUFjQTtvREFBQ2lOLE9BQU83SyxZQUFZO29EQUFJOEssTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS2pELDhEQUFDNU8sa0RBQU1BLENBQUNvTixHQUFHO3dDQUNUQyxXQUFVO3dDQUNWVyxTQUFTOzRDQUFFQyxTQUFTOzRDQUFHZixHQUFHO3dDQUFHO3dDQUM3QlEsU0FBUzs0Q0FBRU8sU0FBUzs0Q0FBR2YsR0FBRzt3Q0FBRTt3Q0FDNUJTLFlBQVk7NENBQUVWLFVBQVU7NENBQUt3QixPQUFPO3dDQUFJOzswREFFeEMsOERBQUNDO2dEQUFHckIsV0FBVTswREFBNkI7Ozs7OzswREFDM0MsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNackosWUFBWTRFLE1BQU0sR0FBRyxJQUNwQjVFLFlBQVk2SyxLQUFLLENBQUMsR0FBRyxHQUFHeEQsR0FBRyxDQUFDLENBQUN5RCxZQUFZakgsc0JBQ3ZDLDhEQUFDN0gsa0RBQU1BLENBQUNvTixHQUFHO3dEQUVUQyxXQUFVO3dEQUNWVyxTQUFTOzREQUFFQyxTQUFTOzREQUFHZCxHQUFHO3dEQUFHO3dEQUM3Qk8sU0FBUzs0REFBRU8sU0FBUzs0REFBR2QsR0FBRzt3REFBRTt3REFDNUJRLFlBQVk7NERBQUVWLFVBQVU7NERBQUt3QixPQUFPLE1BQU01RyxRQUFRO3dEQUFJOzswRUFFdEQsOERBQUN1RjtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDYztnRUFBRWQsV0FBVTswRUFDVixPQUFPeUIsZUFBZSxXQUFXQSxhQUFhQSxXQUFXcEUsT0FBTyxJQUFJb0U7Ozs7Ozs7dURBUmxFakg7Ozs7bUZBYVQsOERBQUNzRztvREFBRWQsV0FBVTs4REFBNEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU9qRCw4REFBQ3JOLGtEQUFNQSxDQUFDb04sR0FBRztnQ0FDVEMsV0FBVTtnQ0FDVlcsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR2QsR0FBRztnQ0FBRztnQ0FDN0JPLFNBQVM7b0NBQUVPLFNBQVM7b0NBQUdkLEdBQUc7Z0NBQUU7Z0NBQzVCUSxZQUFZO29DQUFFVixVQUFVO29DQUFLd0IsT0FBTztnQ0FBRTs7a0RBRXRDLDhEQUFDN00sK0RBQVdBO3dDQUNWMEQsVUFBVUE7d0NBQ1YxQixZQUFZQTt3Q0FDWjZGLFlBQVl2RTs7Ozs7O2tEQUdkLDhEQUFDckQscUVBQWdCQTt3Q0FDZnlELFVBQVVBO3dDQUNWMUIsWUFBWUE7d0NBQ1o2RixZQUFZdkU7Ozs7OztrREFHZCw4REFBQ2xGLGtEQUFNQSxDQUFDb08sTUFBTTt3Q0FDWkMsU0FBUyxJQUFNbEssZUFBZSxDQUFDRDt3Q0FDL0JtSixXQUFVO3dDQUNWaUIsWUFBWTs0Q0FBRUMsT0FBTzt3Q0FBSzt3Q0FDMUJDLFVBQVU7NENBQUVELE9BQU87d0NBQUs7OzBEQUV4Qiw4REFBQzdOLDRPQUFHQTtnREFBQzJNLFdBQVU7Ozs7Ozs0Q0FDZG5KLGNBQWMsaUJBQWlCOzs7Ozs7O2tEQUdsQyw4REFBQ2xFLGtEQUFNQSxDQUFDb08sTUFBTTt3Q0FDWkMsU0FBUzs0Q0FDUDVLLG1CQUFtQjs0Q0FDbkJJLGNBQWM7NENBQ2RFLFlBQVk7NENBQ1pFLGVBQWUsRUFBRTs0Q0FDakJkLGVBQWU7d0NBQ2pCO3dDQUNBa0ssV0FBVTt3Q0FDVmlCLFlBQVk7NENBQUVDLE9BQU87d0NBQUs7d0NBQzFCQyxVQUFVOzRDQUFFRCxPQUFPO3dDQUFLOzswREFFeEIsOERBQUM1Tiw0T0FBS0E7Z0RBQUMwTSxXQUFVOzs7Ozs7NENBQVk7Ozs7Ozs7Ozs7Ozs7NEJBTWhDbkosNkJBQ0MsOERBQUNsRSxrREFBTUEsQ0FBQ29OLEdBQUc7Z0NBQ1RZLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUduQixRQUFRO2dDQUFFO2dDQUNqQ1ksU0FBUztvQ0FBRU8sU0FBUztvQ0FBR25CLFFBQVE7Z0NBQU87Z0NBQ3RDaUMsTUFBTTtvQ0FBRWQsU0FBUztvQ0FBR25CLFFBQVE7Z0NBQUU7Z0NBQzlCTyxXQUFVOzBDQUVWLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQzFMLHlFQUFxQkE7d0NBQUMyRCxVQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBUWpEO0lBRUEscUJBQ0UsOERBQUM4SDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ3hLLCtEQUFVQTtnQkFDVEssYUFBYUE7Z0JBQ2I4TCxZQUFZekgsTUFBTXFCLE1BQU07Z0JBQ3hCckIsT0FBT0E7Z0JBQ1AwSCxRQUFRLElBQU1DLE9BQU9DLE9BQU8sQ0FBQ0MsSUFBSTtnQkFDakNDLFdBQVcsSUFBTWxMLGVBQWUsQ0FBQ0Q7Z0JBQ2pDb0wsUUFBUSxJQUFNdEcsUUFBUUMsR0FBRyxDQUFDO2dCQUMxQi9FLGFBQWFBOzs7Ozs7MEJBSWYsOERBQUNrSjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7OzBCQUdqQiw4REFBQ1Y7Ozs7OzBCQUVELDhEQUFDbEwsK0RBQVdBO2dCQUNWOE4sV0FBV2pNO2dCQUNYa00sWUFBWTVFOzs7Ozs7MEJBR2QsOERBQUN3QztnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNyTixrREFBTUEsQ0FBQ29OLEdBQUc7d0JBQ1RDLFdBQVU7d0JBQ1ZXLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdkLEdBQUcsQ0FBQzt3QkFBRzt3QkFDOUJPLFNBQVM7NEJBQUVPLFNBQVM7NEJBQUdkLEdBQUc7d0JBQUU7d0JBQzVCUSxZQUFZOzRCQUFFVixVQUFVO3dCQUFJOzswQ0FFNUIsOERBQUNHO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDOUwscUdBQVlBO2dEQUFDOEwsV0FBVTs7Ozs7OzBEQUN4Qiw4REFBQ0Q7Z0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7OztrREFFakIsOERBQUNhO3dDQUFHYixXQUFVO2tEQUF3RDs7Ozs7Ozs7Ozs7OzBDQUl4RSw4REFBQ2M7Z0NBQUVkLFdBQVU7MENBQTREOzs7Ozs7NEJBS3hFakosZ0JBQWdCLDBCQUNmLDhEQUFDZ0o7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDb0M7d0NBQUtwQyxXQUFVOzs0Q0FBd0I7NENBQU1uSyxjQUFjOzRDQUFFOzRDQUFLcUUsTUFBTXFCLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU9yRnhFLENBQUFBLGdCQUFnQixZQUFZbEIsZ0JBQWdCLE1BQU0sQ0FBQ00saUNBQ25ELDhEQUFDMUIsMkVBQXVCQTt3QkFDdEI0TixhQUFhdEw7d0JBQ2J1TCxjQUFjbkQ7Ozs7OztvQkFLaEJwSSxDQUFBQSxnQkFBZ0IsWUFBWUEsZ0JBQWdCLFNBQVEsS0FBTSxDQUFDSSxnQkFBZ0IsQ0FBQ0kseUNBQzVFLDhEQUFDNUUsa0RBQU1BLENBQUNvTixHQUFHO3dCQUNUWSxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHZCxHQUFHO3dCQUFHO3dCQUM3Qk8sU0FBUzs0QkFBRU8sU0FBUzs0QkFBR2QsR0FBRzt3QkFBRTt3QkFDNUJFLFdBQVU7a0NBRVYsNEVBQUN0TCxnRUFBWUE7NEJBQ1g2TixvQkFBb0I3RTs0QkFDcEJFLGNBQWM3RyxnQkFBZ0IsWUFBWSxVQUFVOzs7Ozs7Ozs7OztvQkFNekRBLGdCQUFnQixZQUFZUSwyQkFBMkJOLGdDQUN0RCw4REFBQ3RFLGtEQUFNQSxDQUFDb04sR0FBRzt3QkFDVFksU0FBUzs0QkFBRUMsU0FBUzs0QkFBR2QsR0FBRzt3QkFBRzt3QkFDN0JPLFNBQVM7NEJBQUVPLFNBQVM7NEJBQUdkLEdBQUc7d0JBQUU7d0JBQzVCRSxXQUFVOzswQ0FFViw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDd0M7d0NBQUd4QyxXQUFVO2tEQUFxQzs7Ozs7O2tEQUNuRCw4REFBQ2M7d0NBQUVkLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7MENBRy9CLDhEQUFDcEwsd0VBQW1CQTtnQ0FDbEI2Tix3QkFBd0IvRDtnQ0FDeEJnRSxXQUFXakw7Ozs7OzswQ0FHYiw4REFBQ3NJO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDck4sa0RBQU1BLENBQUNvTyxNQUFNO29DQUNaQyxTQUFTO3dDQUNQeEosMkJBQTJCO3dDQUMzQjFCLGVBQWU7b0NBQ2pCO29DQUNBa0ssV0FBVTtvQ0FDVmlCLFlBQVk7d0NBQUVDLE9BQU87b0NBQUs7b0NBQzFCQyxVQUFVO3dDQUFFRCxPQUFPO29DQUFLOzhDQUN6Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBUU5uSyxnQkFBZ0IsWUFBWUksZ0JBQWdCRixnQ0FDM0MsOERBQUN0RSxrREFBTUEsQ0FBQ29OLEdBQUc7d0JBQ1RZLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdkLEdBQUc7d0JBQUc7d0JBQzdCTyxTQUFTOzRCQUFFTyxTQUFTOzRCQUFHZCxHQUFHO3dCQUFFO3dCQUM1QkUsV0FBVTs7MENBRVYsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3dDO3dDQUFHeEMsV0FBVTtrREFBcUM7Ozs7OztrREFDbkQsOERBQUNjO3dDQUFFZCxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUcvQiw4REFBQ3JMLHNFQUFrQkE7Z0NBQ2pCZ0osY0FBYzFHO2dDQUNkMkcsY0FBYTs7Ozs7OzBDQUdmLDhEQUFDbUM7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNyTixrREFBTUEsQ0FBQ29PLE1BQU07b0NBQ1pDLFNBQVMsSUFBTWxMLGVBQWU7b0NBQzlCa0ssV0FBVTtvQ0FDVmlCLFlBQVk7d0NBQUVDLE9BQU87b0NBQUs7b0NBQzFCQyxVQUFVO3dDQUFFRCxPQUFPO29DQUFLOztzREFFeEIsOERBQUMvTiw0T0FBVUE7NENBQUM2TSxXQUFVOzs7Ozs7d0NBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFReENqSixDQUFBQSxnQkFBZ0IsWUFBYUEsZ0JBQWdCLFlBQVlsQixlQUFlLEtBQUssQ0FBQzBCLHVCQUF1QixLQUFNLENBQUNwQixpQ0FDNUc7OzBDQUVFLDhEQUFDeEQsa0RBQU1BLENBQUNvTixHQUFHO2dDQUNUQyxXQUFVO2dDQUNWVyxTQUFTO29DQUFFQyxTQUFTO29DQUFHZCxHQUFHLENBQUM7Z0NBQUc7Z0NBQzlCTyxTQUFTO29DQUFFTyxTQUFTO29DQUFHZCxHQUFHO2dDQUFFO2dDQUM1QlEsWUFBWTtvQ0FBRVYsVUFBVTtnQ0FBSTswQ0FFNUIsNEVBQUN0SyxrRUFBYUE7b0NBQ1pPLGFBQWFBO29DQUNiOEwsWUFBWXpILE1BQU1xQixNQUFNO29DQUN4QnJCLE9BQU9BO29DQUNQeUksYUFBYSxDQUFDQzt3Q0FDWixJQUFJQSxhQUFhL00sYUFBYTs0Q0FDNUJDLGVBQWU4TTt3Q0FDakI7b0NBQ0Y7b0NBQ0FDLHNCQUFzQjs7Ozs7Ozs7Ozs7MENBTzlCLDhEQUFDOUM7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3JOLGtEQUFNQSxDQUFDb04sR0FBRzs0Q0FDVEMsV0FBVTs0Q0FDVlcsU0FBUztnREFBRUMsU0FBUztnREFBR2YsR0FBRyxDQUFDOzRDQUFHOzRDQUM5QlEsU0FBUztnREFBRU8sU0FBUztnREFBR2YsR0FBRzs0Q0FBRTs0Q0FDNUJTLFlBQVk7Z0RBQUVWLFVBQVU7Z0RBQUt3QixPQUFPOzRDQUFJOzs4REFHeEMsOERBQUNyQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDWnZOLDBEQUFtQixDQUFDeUgsS0FBSyxDQUFDckUsWUFBWSxDQUFDc0UsSUFBSSxFQUFFO29FQUM1QzZGLFdBQVc7Z0VBQ2I7OEVBQ0EsOERBQUN3QztvRUFBR3hDLFdBQVU7OEVBQ1g5RixLQUFLLENBQUNyRSxZQUFZLENBQUN3RCxLQUFLOzs7Ozs7Ozs7Ozs7c0VBRzdCLDhEQUFDeUg7NERBQUVkLFdBQVU7c0VBQ1Y5RixLQUFLLENBQUNyRSxZQUFZLENBQUMyRCxXQUFXOzs7Ozs7Ozs7Ozs7OERBSW5DLDhEQUFDNUcsMkRBQWVBO29EQUFDd00sTUFBSzs7d0RBQ25CdkosZ0JBQWdCLG1CQUFLLDhEQUFDa047NERBQWlCOUssVUFBVUE7NERBQVVtQyxnQkFBZ0JBOzREQUFnQkgsYUFBYUE7Ozs7Ozt3REFDeEdwRSxnQkFBZ0IsbUJBQUssOERBQUNtTjs0REFBYy9LLFVBQVVBOzREQUFVbUMsZ0JBQWdCQTs0REFBZ0JTLGNBQWNBOzREQUFjTSxpQkFBaUJBOzREQUFpQmxCLGFBQWFBOzs7Ozs7d0RBQ25LcEUsZ0JBQWdCLG1CQUFLLDhEQUFDSiw2RUFBY0E7NERBQUN3QyxVQUFVQTs0REFBVW1DLGdCQUFnQkE7NERBQWdCUyxjQUFjQTs0REFBY00saUJBQWlCQTs0REFBaUJsQixhQUFhQTs7Ozs7O3dEQUNwS3BFLGdCQUFnQixtQkFBSyw4REFBQ0gsaUZBQWtCQTs0REFBQ3VDLFVBQVVBOzREQUFVbUMsZ0JBQWdCQTs0REFBZ0JTLGNBQWNBOzREQUFjTSxpQkFBaUJBOzREQUFpQmxCLGFBQWFBOzs7Ozs7d0RBQ3hLcEUsZ0JBQWdCLG1CQUFLLDhEQUFDRix5RUFBVUE7NERBQUNzQyxVQUFVQTs0REFBVWdDLGFBQWFBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNekUsOERBQUM4Rjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNyTixrREFBTUEsQ0FBQ29OLEdBQUc7Z0RBQ1RDLFdBQVU7Z0RBQ1ZXLFNBQVM7b0RBQUVDLFNBQVM7b0RBQUdmLEdBQUc7Z0RBQUc7Z0RBQzdCUSxTQUFTO29EQUFFTyxTQUFTO29EQUFHZixHQUFHO2dEQUFFO2dEQUM1QlMsWUFBWTtvREFBRVYsVUFBVTtvREFBS3dCLE9BQU87Z0RBQUk7O2tFQUV4Qyw4REFBQ3JCO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ3FCO2dFQUFHckIsV0FBVTs7a0ZBQ1osOERBQUMzTSw0T0FBR0E7d0VBQUMyTSxXQUFVOzs7Ozs7b0VBQTJDOzs7Ozs7OzBFQUc1RCw4REFBQ3JOLGtEQUFNQSxDQUFDb08sTUFBTTtnRUFDWkMsU0FBUyxJQUFNbEssZUFBZSxDQUFDRDtnRUFDL0JtSixXQUFVO2dFQUNWaUIsWUFBWTtvRUFBRUMsT0FBTztnRUFBSztnRUFDMUJDLFVBQVU7b0VBQUVELE9BQU87Z0VBQUs7MEVBRXZCckssY0FBYyxTQUFTOzs7Ozs7Ozs7Ozs7b0RBSTNCQSw0QkFDQyw4REFBQ2tKO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDMUwseUVBQXFCQTs0REFBQzJELFVBQVVBOzs7Ozs7Ozs7O2tGQUduQyw4REFBQzhIO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDck4sa0RBQU1BLENBQUNvTixHQUFHOzREQUNUWSxTQUFTO2dFQUFFTyxPQUFPO2dFQUFLTixTQUFTOzREQUFFOzREQUNsQ1AsU0FBUztnRUFBRWEsT0FBTztnRUFBR04sU0FBUzs0REFBRTs0REFDaENOLFlBQVk7Z0VBQUVWLFVBQVU7NERBQUk7OzhFQUU1Qiw4REFBQ3ZNLDRPQUFHQTtvRUFBQzJNLFdBQVU7Ozs7Ozs4RUFDZiw4REFBQ2M7b0VBQUVkLFdBQVU7OEVBQTRCOzs7Ozs7OEVBQ3pDLDhEQUFDYztvRUFBRWQsV0FBVTs4RUFBbUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQU94RCw4REFBQ3JOLGtEQUFNQSxDQUFDb04sR0FBRztnREFDVEMsV0FBVTtnREFDVlcsU0FBUztvREFBRUMsU0FBUztvREFBR2QsR0FBRztnREFBRztnREFDN0JPLFNBQVM7b0RBQUVPLFNBQVM7b0RBQUdkLEdBQUc7Z0RBQUU7Z0RBQzVCUSxZQUFZO29EQUFFVixVQUFVO29EQUFLd0IsT0FBTztnREFBSTswREFFeEMsNEVBQUNoTSwrREFBVUE7OERBQ1QsNEVBQUNMLHlFQUFvQkE7d0RBQ25Ca0QsVUFBVUE7d0RBQ1Z4QixVQUFVd0QsWUFBWWdKLFlBQVk7d0RBQ2xDdE0sYUFBYXNELFlBQVl1RSxlQUFlO3dEQUN4QzBFLGtCQUFrQmpKOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVE1Qiw4REFBQzhGO2dDQUFJQyxXQUFVOzs7Ozs7OztvQkFLZGpJLHNDQUNDLDhEQUFDL0MscUVBQWdCQTt3QkFDZjZDLGtCQUFrQkE7d0JBQ2xCc0wsa0JBQWtCckw7d0JBQ2xCc0wsU0FBUyxJQUFNcEwsd0JBQXdCOzs7Ozs7a0NBSzNDLDhEQUFDekMscUVBQWdCQTt3QkFDZk0sYUFBYUE7d0JBQ2I4TCxZQUFZekgsTUFBTXFCLE1BQU07d0JBQ3hCOEgsWUFBWTs0QkFDVixJQUFJeE4sY0FBYyxHQUFHO2dDQUNuQkMsZUFBZUQsY0FBYzs0QkFDL0I7d0JBQ0Y7d0JBQ0F5TixRQUFROzRCQUNOLElBQUl6TixjQUFjcUUsTUFBTXFCLE1BQU0sR0FBRyxHQUFHO2dDQUNsQ3pGLGVBQWVELGNBQWM7NEJBQy9CO3dCQUNGO3dCQUNBME4sWUFBWTlIO3dCQUNaMUYsY0FBY0E7d0JBQ2R5TixZQUFZO3dCQUNadEosT0FBT0E7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtqQjtHQTUrQk10RTs7UUFnRmdCViw4REFBY0E7OztLQWhGOUJVO0FBOCtCTixzQ0FBc0M7QUFDdEMsTUFBTW1OLG1CQUFtQjtRQUFDLEVBQUU5SyxRQUFRLEVBQUVtQyxjQUFjLEVBQUVILFdBQVcsRUFBRTtRQXdCN0NBLDRCQW9CQUEsNkJBcUJBQSw2QkE0RUFBO3lCQTVJcEIsOERBQUN0SCxrREFBTUEsQ0FBQ29OLEdBQUc7UUFDVFksU0FBUztZQUFFQyxTQUFTO1lBQUdkLEdBQUc7UUFBRztRQUM3Qk8sU0FBUztZQUFFTyxTQUFTO1lBQUdkLEdBQUc7UUFBRTtRQUM1QjRCLE1BQU07WUFBRWQsU0FBUztZQUFHZCxHQUFHLENBQUM7UUFBRztRQUMzQkUsV0FBVTtrQkFFViw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEOztzQ0FDQyw4REFBQzBEOzRCQUFNekQsV0FBVTs7Z0NBQW9FOzhDQUVuRiw4REFBQzNLLCtEQUFVQTtvQ0FBQ3FPLFdBQVU7b0NBQVkxRCxXQUFVOzs7Ozs7Ozs7Ozs7c0NBRTlDLDhEQUFDMkQ7NEJBQ0NDLE1BQUs7NEJBQ0xySixPQUFPdEMsU0FBU0UsUUFBUSxDQUFDQyxTQUFTOzRCQUNsQ3lMLFVBQVUsQ0FBQ0MsSUFBTTFKLGVBQWUsWUFBWSxhQUFhMEosRUFBRUMsTUFBTSxDQUFDeEosS0FBSzs0QkFDdkV5RixXQUFVOzRCQUNWZ0UsYUFBWTs7Ozs7O3NDQUVkLDhEQUFDNU8sK0RBQVVBO3NDQUNULDRFQUFDRCxzRUFBaUJBO2dDQUNoQjhPLFdBQVU7Z0NBQ1YxSixPQUFPdEMsU0FBU0UsUUFBUSxDQUFDQyxTQUFTO2dDQUNsQ21HLFFBQVEsR0FBRXRFLDZCQUFBQSxZQUFZaUssYUFBYSxjQUF6QmpLLGlEQUFBQSwyQkFBMkI3QixTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLcEQsOERBQUMySDs7c0NBQ0MsOERBQUMwRDs0QkFBTXpELFdBQVU7c0NBQW9FOzs7Ozs7c0NBR3JGLDhEQUFDMkQ7NEJBQ0NDLE1BQUs7NEJBQ0xySixPQUFPdEMsU0FBU0UsUUFBUSxDQUFDRSxRQUFROzRCQUNqQ3dMLFVBQVUsQ0FBQ0MsSUFBTTFKLGVBQWUsWUFBWSxZQUFZMEosRUFBRUMsTUFBTSxDQUFDeEosS0FBSzs0QkFDdEV5RixXQUFVOzRCQUNWZ0UsYUFBWTs7Ozs7O3NDQUVkLDhEQUFDNU8sK0RBQVVBO3NDQUNULDRFQUFDRCxzRUFBaUJBO2dDQUNoQjhPLFdBQVU7Z0NBQ1YxSixPQUFPdEMsU0FBU0UsUUFBUSxDQUFDRSxRQUFRO2dDQUNqQ2tHLFFBQVEsRUFBRXRFLHdCQUFBQSxtQ0FBQUEsOEJBQUFBLFlBQWFpSyxhQUFhLGNBQTFCakssa0RBQUFBLDRCQUE0QjVCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUtwRCw4REFBQzBIOztzQ0FDQyw4REFBQzBEOzRCQUFNekQsV0FBVTs7OENBQ2YsOERBQUNwTSw0T0FBSUE7b0NBQUNvTSxXQUFVOzs7Ozs7Z0NBQXdCOzs7Ozs7O3NDQUcxQyw4REFBQzJEOzRCQUNDQyxNQUFLOzRCQUNMckosT0FBT3RDLFNBQVNFLFFBQVEsQ0FBQ0csS0FBSzs0QkFDOUJ1TCxVQUFVLENBQUNDLElBQU0xSixlQUFlLFlBQVksU0FBUzBKLEVBQUVDLE1BQU0sQ0FBQ3hKLEtBQUs7NEJBQ25FeUYsV0FBVTs0QkFDVmdFLGFBQVk7Ozs7OztzQ0FFZCw4REFBQzVPLCtEQUFVQTtzQ0FDVCw0RUFBQ0Qsc0VBQWlCQTtnQ0FDaEI4TyxXQUFVO2dDQUNWMUosT0FBT3RDLFNBQVNFLFFBQVEsQ0FBQ0csS0FBSztnQ0FDOUJpRyxRQUFRLEVBQUV0RSx3QkFBQUEsbUNBQUFBLDhCQUFBQSxZQUFhaUssYUFBYSxjQUExQmpLLGtEQUFBQSw0QkFBNEIzQixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLakQsOERBQUN5SDs7c0NBQ0MsOERBQUMwRDs0QkFBTXpELFdBQVU7OzhDQUNmLDhEQUFDbk0sNE9BQUtBO29DQUFDbU0sV0FBVTs7Ozs7O2dDQUF3Qjs7Ozs7OztzQ0FHM0MsOERBQUMyRDs0QkFDQ0MsTUFBSzs0QkFDTHJKLE9BQU90QyxTQUFTRSxRQUFRLENBQUNJLEtBQUs7NEJBQzlCc0wsVUFBVSxDQUFDQyxJQUFNMUosZUFBZSxZQUFZLFNBQVMwSixFQUFFQyxNQUFNLENBQUN4SixLQUFLOzRCQUNuRXlGLFdBQVU7NEJBQ1ZnRSxhQUFZOzs7Ozs7Ozs7Ozs7OEJBSWhCLDhEQUFDakU7O3NDQUNDLDhEQUFDMEQ7NEJBQU16RCxXQUFVOzs4Q0FDZiw4REFBQ3ZNLDRPQUFNQTtvQ0FBQ3VNLFdBQVU7Ozs7OztnQ0FBd0I7Ozs7Ozs7c0NBRzVDLDhEQUFDMkQ7NEJBQ0NDLE1BQUs7NEJBQ0xySixPQUFPdEMsU0FBU0UsUUFBUSxDQUFDSyxRQUFROzRCQUNqQ3FMLFVBQVUsQ0FBQ0MsSUFBTTFKLGVBQWUsWUFBWSxZQUFZMEosRUFBRUMsTUFBTSxDQUFDeEosS0FBSzs0QkFDdEV5RixXQUFVOzRCQUNWZ0UsYUFBWTs7Ozs7Ozs7Ozs7OzhCQUloQiw4REFBQ2pFOztzQ0FDQyw4REFBQzBEOzRCQUFNekQsV0FBVTs7OENBQ2YsOERBQUNsTSw0T0FBSUE7b0NBQUNrTSxXQUFVOzs7Ozs7Z0NBQXdCOzs7Ozs7O3NDQUcxQyw4REFBQzJEOzRCQUNDQyxNQUFLOzRCQUNMckosT0FBT3RDLFNBQVNFLFFBQVEsQ0FBQ00sUUFBUTs0QkFDakNvTCxVQUFVLENBQUNDLElBQU0xSixlQUFlLFlBQVksWUFBWTBKLEVBQUVDLE1BQU0sQ0FBQ3hKLEtBQUs7NEJBQ3RFeUYsV0FBVTs0QkFDVmdFLGFBQVk7Ozs7Ozs7Ozs7Ozs4QkFJaEIsOERBQUNqRTtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUN5RDs0QkFBTXpELFdBQVU7OzhDQUNmLDhEQUFDck0sNE9BQUtBO29DQUFDcU0sV0FBVTs7Ozs7O2dDQUF3Qjs7Ozs7OztzQ0FHM0MsOERBQUMyRDs0QkFDQ0MsTUFBSzs0QkFDTHJKLE9BQU90QyxTQUFTRSxRQUFRLENBQUNPLFNBQVM7NEJBQ2xDbUwsVUFBVSxDQUFDQyxJQUFNMUosZUFBZSxZQUFZLGFBQWEwSixFQUFFQyxNQUFNLENBQUN4SixLQUFLOzRCQUN2RXlGLFdBQVU7NEJBQ1ZnRSxhQUFZOzs7Ozs7Ozs7Ozs7OEJBSWhCLDhEQUFDakU7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDeUQ7NEJBQU16RCxXQUFVO3NDQUFvRTs7Ozs7O3NDQUdyRiw4REFBQ21FOzRCQUNDNUosT0FBT3RDLFNBQVNFLFFBQVEsQ0FBQ1EsT0FBTzs0QkFDaENrTCxVQUFVLENBQUNDLElBQU0xSixlQUFlLFlBQVksV0FBVzBKLEVBQUVDLE1BQU0sQ0FBQ3hKLEtBQUs7NEJBQ3JFNkosTUFBTTs0QkFDTnBFLFdBQVU7NEJBQ1ZnRSxhQUFZOzs7Ozs7c0NBRWQsOERBQUM1TywrREFBVUE7c0NBQ1QsNEVBQUNELHNFQUFpQkE7Z0NBQ2hCOE8sV0FBVTtnQ0FDVjFKLE9BQU90QyxTQUFTRSxRQUFRLENBQUNRLE9BQU87Z0NBQ2hDNEYsUUFBUSxFQUFFdEUsd0JBQUFBLG1DQUFBQSw4QkFBQUEsWUFBYWlLLGFBQWEsY0FBMUJqSyxrREFBQUEsNEJBQTRCdEIsT0FBTztnQ0FDN0MwTCxhQUFhOzs7Ozs7Ozs7OztzQ0FLakIsOERBQUN0RTs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ2xMLHlFQUFvQkE7Z0NBQ25CNE8sV0FBVTtnQ0FDVlksY0FBY3JNLFNBQVNFLFFBQVEsQ0FBQ1EsT0FBTztnQ0FDdkM0TCxtQkFBbUIsQ0FBQzlDLGFBQWVySCxlQUFlLFlBQVksV0FBV3FIO2dDQUN6RStDLFNBQVM7b0NBQ1BwTSxXQUFXSCxTQUFTRSxRQUFRLENBQUNDLFNBQVM7b0NBQ3RDQyxVQUFVSixTQUFTRSxRQUFRLENBQUNFLFFBQVE7b0NBQ3BDZSxZQUFZbkIsU0FBU21CLFVBQVU7b0NBQy9CSyxRQUFReEIsU0FBU3dCLE1BQU07Z0NBQ3pCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztNQTdKTnNKO0FBcUtOLDJCQUEyQjtBQUMzQixNQUFNQyxnQkFBZ0I7UUFBQyxFQUFFL0ssUUFBUSxFQUFFbUMsY0FBYyxFQUFFUyxZQUFZLEVBQUVNLGVBQWUsRUFBRWxCLFdBQVcsRUFBRTt5QkFDN0YsOERBQUN0SCxrREFBTUEsQ0FBQ29OLEdBQUc7UUFDVFksU0FBUztZQUFFQyxTQUFTO1lBQUdkLEdBQUc7UUFBRztRQUM3Qk8sU0FBUztZQUFFTyxTQUFTO1lBQUdkLEdBQUc7UUFBRTtRQUM1QjRCLE1BQU07WUFBRWQsU0FBUztZQUFHZCxHQUFHLENBQUM7UUFBRztRQUMzQkUsV0FBVTs7MEJBRVYsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDbE4sNE9BQWFBO2dDQUFDa04sV0FBVTs7Ozs7OzBDQUN6Qiw4REFBQ3dDO2dDQUFHeEMsV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7OztrQ0FFckMsOERBQUNlO3dCQUNDQyxTQUFTLElBQU1uRyxhQUFhLGFBQWE7Z0NBQ3ZDL0IsUUFBUTtnQ0FDUkMsYUFBYTtnQ0FDYlAsVUFBVTtnQ0FDVlEsV0FBVztnQ0FDWEMsU0FBUztnQ0FDVEMsS0FBSztnQ0FDTEMsVUFBVTs0QkFDWjt3QkFDQTZHLFdBQVU7OzBDQUVWLDhEQUFDek0sNE9BQUlBO2dDQUFDeU0sV0FBVTs7Ozs7OzRCQUFZOzs7Ozs7Ozs7Ozs7OzBCQUtoQyw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ1ovSCxTQUFTVyxTQUFTLENBQUNvRixHQUFHLENBQUMsQ0FBQ0MsS0FBS3pEO3dCQThCUlAsNEJBb0JBQTt5Q0FqRHBCLDhEQUFDOEY7d0JBQWlCQyxXQUFVOzswQ0FDMUIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3FCO3dDQUFHckIsV0FBVTs7NENBQXdCOzRDQUFXeEYsUUFBUTs7Ozs7OztvQ0FDeER2QyxTQUFTVyxTQUFTLENBQUMyQyxNQUFNLEdBQUcsbUJBQzNCLDhEQUFDd0Y7d0NBQ0NDLFNBQVMsSUFBTTdGLGdCQUFnQixhQUFhOEMsSUFBSXBGLEVBQUU7d0NBQ2xEbUgsV0FBVTtrREFFViw0RUFBQ3hNLDRPQUFNQTs0Q0FBQ3dNLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUt4Qiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUMwRDtnREFBTXpELFdBQVU7MERBQStDOzs7Ozs7MERBR2hFLDhEQUFDMkQ7Z0RBQ0NDLE1BQUs7Z0RBQ0xySixPQUFPMEQsSUFBSW5GLE1BQU07Z0RBQ2pCK0ssVUFBVSxDQUFDQyxJQUFNMUosZUFBZSxhQUFhLFVBQVUwSixFQUFFQyxNQUFNLENBQUN4SixLQUFLLEVBQUVDO2dEQUN2RXdGLFdBQVU7Z0RBQ1ZnRSxhQUFZOzs7Ozs7MERBRWQsOERBQUM1TywrREFBVUE7MERBQ1QsNEVBQUNELHNFQUFpQkE7b0RBQ2hCOE8sV0FBVTtvREFDVjFKLE9BQU8wRCxJQUFJbkYsTUFBTTtvREFDakJ5RixRQUFRLEVBQUV0RSx3QkFBQUEsbUNBQUFBLDZCQUFBQSxZQUFhaUssYUFBYSxjQUExQmpLLGlEQUFBQSwwQkFBNEIsQ0FBQyxvQkFBMEIsT0FBTk8sT0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS3pFLDhEQUFDdUY7OzBEQUNDLDhEQUFDMEQ7Z0RBQU16RCxXQUFVOzBEQUErQzs7Ozs7OzBEQUdoRSw4REFBQzJEO2dEQUNDQyxNQUFLO2dEQUNMckosT0FBTzBELElBQUlsRixXQUFXO2dEQUN0QjhLLFVBQVUsQ0FBQ0MsSUFBTTFKLGVBQWUsYUFBYSxlQUFlMEosRUFBRUMsTUFBTSxDQUFDeEosS0FBSyxFQUFFQztnREFDNUV3RixXQUFVO2dEQUNWZ0UsYUFBWTs7Ozs7OzBEQUVkLDhEQUFDNU8sK0RBQVVBOzBEQUNULDRFQUFDRCxzRUFBaUJBO29EQUNoQjhPLFdBQVU7b0RBQ1YxSixPQUFPMEQsSUFBSWxGLFdBQVc7b0RBQ3RCd0YsUUFBUSxFQUFFdEUsd0JBQUFBLG1DQUFBQSw4QkFBQUEsWUFBYWlLLGFBQWEsY0FBMUJqSyxrREFBQUEsMkJBQTRCLENBQUMseUJBQStCLE9BQU5PLE9BQVE7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUs5RSw4REFBQ3VGOzswREFDQyw4REFBQzBEO2dEQUFNekQsV0FBVTs7a0VBQ2YsOERBQUN2TSw0T0FBTUE7d0RBQUN1TSxXQUFVOzs7Ozs7b0RBQXdCOzs7Ozs7OzBEQUc1Qyw4REFBQzJEO2dEQUNDQyxNQUFLO2dEQUNMckosT0FBTzBELElBQUl6RixRQUFRO2dEQUNuQnFMLFVBQVUsQ0FBQ0MsSUFBTTFKLGVBQWUsYUFBYSxZQUFZMEosRUFBRUMsTUFBTSxDQUFDeEosS0FBSyxFQUFFQztnREFDekV3RixXQUFVO2dEQUNWZ0UsYUFBWTs7Ozs7Ozs7Ozs7O2tEQUloQiw4REFBQ2pFOzswREFDQyw4REFBQzBEO2dEQUFNekQsV0FBVTswREFBK0M7Ozs7OzswREFHaEUsOERBQUMyRDtnREFDQ0MsTUFBSztnREFDTHJKLE9BQU8wRCxJQUFJL0UsR0FBRztnREFDZDJLLFVBQVUsQ0FBQ0MsSUFBTTFKLGVBQWUsYUFBYSxPQUFPMEosRUFBRUMsTUFBTSxDQUFDeEosS0FBSyxFQUFFQztnREFDcEV3RixXQUFVO2dEQUNWZ0UsYUFBWTs7Ozs7Ozs7Ozs7O2tEQUloQiw4REFBQ2pFOzswREFDQyw4REFBQzBEO2dEQUFNekQsV0FBVTs7a0VBQ2YsOERBQUN0TSw0T0FBUUE7d0RBQUNzTSxXQUFVOzs7Ozs7b0RBQXdCOzs7Ozs7OzBEQUc5Qyw4REFBQzJEO2dEQUNDQyxNQUFLO2dEQUNMckosT0FBTzBELElBQUlqRixTQUFTO2dEQUNwQjZLLFVBQVUsQ0FBQ0MsSUFBTTFKLGVBQWUsYUFBYSxhQUFhMEosRUFBRUMsTUFBTSxDQUFDeEosS0FBSyxFQUFFQztnREFDMUV3RixXQUFVOzs7Ozs7Ozs7Ozs7a0RBSWQsOERBQUNEOzswREFDQyw4REFBQzBEO2dEQUFNekQsV0FBVTs7a0VBQ2YsOERBQUN0TSw0T0FBUUE7d0RBQUNzTSxXQUFVOzs7Ozs7b0RBQXdCOzs7Ozs7OzBEQUc5Qyw4REFBQzJEO2dEQUNDQyxNQUFLO2dEQUNMckosT0FBTzBELElBQUloRixPQUFPO2dEQUNsQjRLLFVBQVUsQ0FBQ0MsSUFBTTFKLGVBQWUsYUFBYSxXQUFXMEosRUFBRUMsTUFBTSxDQUFDeEosS0FBSyxFQUFFQztnREFDeEV3RixXQUFVOzs7Ozs7Ozs7Ozs7a0RBSWQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3lEO2dEQUFNekQsV0FBVTswREFBK0M7Ozs7OzswREFHaEUsOERBQUNtRTtnREFDQzVKLE9BQU8wRCxJQUFJOUUsUUFBUTtnREFDbkIwSyxVQUFVLENBQUNDLElBQU0xSixlQUFlLGFBQWEsWUFBWTBKLEVBQUVDLE1BQU0sQ0FBQ3hKLEtBQUssRUFBRUM7Z0RBQ3pFNEosTUFBTTtnREFDTnBFLFdBQVU7Z0RBQ1ZnRSxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VCQXBIVi9GLElBQUlwRixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7TUEvQmxCbUs7QUErSk4saUVBQWVwTixhQUFhQSxFQUFDIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXEJsaW5rRmluZFxcQmxpbmtGaW5kLVdlYlxcZnJvbnRlbmRcXHNyY1xcYXBwXFxyZXN1bWUtYnVpbGRlclxccGFnZS5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSBcImZyYW1lci1tb3Rpb25cIjtcbmltcG9ydCB7IFxuICBVc2VyLCBcbiAgR3JhZHVhdGlvbkNhcCwgXG4gIEJyaWVmY2FzZSwgXG4gIEF3YXJkLFxuICBGaWxlVGV4dCwgXG4gIEFycm93TGVmdCxcbiAgQXJyb3dSaWdodCxcbiAgU3BhcmtsZXMsXG4gIEV5ZSxcbiAgV2FuZDIsXG4gIFBsdXMsXG4gIFRyYXNoMixcbiAgTWFwUGluLFxuICBDYWxlbmRhcixcbiAgR2xvYmUsXG4gIE1haWwsXG4gIFBob25lLFxuICBMaW5rLFxuICBVcGxvYWQsXG4gIFBsdXNDaXJjbGUsXG4gIFBhbGV0dGVcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHsgU3BhcmtsZXNJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9zb2xpZCc7XG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gXCJyZWFjdC1ob3QtdG9hc3RcIjtcbmltcG9ydCBQcm9ncmVzc0JhciBmcm9tIFwiQC9jb21wb25lbnRzL1Byb2dyZXNzQmFyXCI7XG5pbXBvcnQgQVRTU2NvcmVDaXJjbGUgZnJvbSBcIkAvY29tcG9uZW50cy9BVFNTY29yZUNpcmNsZVwiO1xuaW1wb3J0IEVuaGFuY2VkUmVzdW1lUHJldmlldyBmcm9tIFwiQC9jb21wb25lbnRzL0VuaGFuY2VkUmVzdW1lUHJldmlld1wiO1xuaW1wb3J0IFBERkRvd25sb2FkLCB7IFZpZXdSZXN1bWVCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL1BERkRvd25sb2FkXCI7XG5pbXBvcnQgUmVzdW1lQnVpbGRlck1vZGVUb2dnbGUgZnJvbSBcIkAvY29tcG9uZW50cy9SZXN1bWVCdWlsZGVyTW9kZVRvZ2dsZVwiO1xuaW1wb3J0IFJlc3VtZVVwbG9hZCBmcm9tIFwiQC9jb21wb25lbnRzL1Jlc3VtZVVwbG9hZFwiO1xuaW1wb3J0IEFUU0FuYWx5c2lzRGlzcGxheSBmcm9tIFwiQC9jb21wb25lbnRzL0FUU0FuYWx5c2lzRGlzcGxheVwiO1xuaW1wb3J0IEpvYkRlc2NyaXB0aW9uSW5wdXQgZnJvbSBcIkAvY29tcG9uZW50cy9Kb2JEZXNjcmlwdGlvbklucHV0XCI7XG5pbXBvcnQgQmVmb3JlQWZ0ZXJDb21wYXJpc29uIGZyb20gXCJAL2NvbXBvbmVudHMvQmVmb3JlQWZ0ZXJDb21wYXJpc29uXCI7XG5pbXBvcnQgQUlDb250ZW50U3VnZ2VzdGlvbnMgZnJvbSBcIkAvY29tcG9uZW50cy9BSUNvbnRlbnRTdWdnZXN0aW9uc1wiO1xuaW1wb3J0IEFUU09wdGltaXphdGlvblBhbmVsIGZyb20gXCJAL2NvbXBvbmVudHMvQVRTT3B0aW1pemF0aW9uUGFuZWxcIjtcbmltcG9ydCBUZW1wbGF0ZVNlbGVjdG9yIGZyb20gXCJAL2NvbXBvbmVudHMvVGVtcGxhdGVTZWxlY3RvclwiO1xuaW1wb3J0IFVwbG9hZEVuaGFuY2VtZW50V29ya2Zsb3cgZnJvbSBcIkAvY29tcG9uZW50cy9VcGxvYWRFbmhhbmNlbWVudFdvcmtmbG93XCI7XG5pbXBvcnQgdXNlQVRTQW5hbHlzaXMgZnJvbSBcIkAvaG9va3MvdXNlQVRTQW5hbHlzaXNcIjtcbmltcG9ydCBBVFNGaWVsZEluZGljYXRvciBmcm9tIFwiQC9jb21wb25lbnRzL0FUU0ZpZWxkSW5kaWNhdG9yXCI7XG5pbXBvcnQgQ2xpZW50T25seSBmcm9tIFwiQC9jb21wb25lbnRzL0NsaWVudE9ubHlcIjtcbmltcG9ydCBBVFNUb29sdGlwIGZyb20gXCJAL2NvbXBvbmVudHMvQVRTVG9vbHRpcFwiO1xuaW1wb3J0IFN0ZXBJbmRpY2F0b3IgZnJvbSBcIkAvY29tcG9uZW50cy9TdGVwSW5kaWNhdG9yXCI7XG5pbXBvcnQgU3RpY2t5TmF2aWdhdGlvbiBmcm9tIFwiQC9jb21wb25lbnRzL1N0aWNreU5hdmlnYXRpb25cIjtcbmltcG9ydCBGb3JtSGVhZGVyIGZyb20gXCJAL2NvbXBvbmVudHMvRm9ybUhlYWRlclwiO1xuLy8gaW1wb3J0IFN1Y2Nlc3NTY3JlZW4gZnJvbSBcIkAvY29tcG9uZW50cy9TdWNjZXNzU2NyZWVuXCI7XG5pbXBvcnQgeyBFeHBlcmllbmNlRm9ybSwgU2tpbGxzUHJvamVjdHNGb3JtLCBSZXZpZXdGb3JtIH0gZnJvbSBcIkAvY29tcG9uZW50cy9SZXN1bWVGb3JtQ29tcG9uZW50c1wiO1xuXG5jb25zdCBSZXN1bWVCdWlsZGVyID0gKCkgPT4ge1xuICAvLyBNYWluIHN0YXRlIG1hbmFnZW1lbnRcbiAgY29uc3QgW2N1cnJlbnRTdGVwLCBzZXRDdXJyZW50U3RlcF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW2lzR2VuZXJhdGluZywgc2V0SXNHZW5lcmF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dQcm9ncmVzc0Jhciwgc2V0U2hvd1Byb2dyZXNzQmFyXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Jlc3VtZUdlbmVyYXRlZCwgc2V0UmVzdW1lR2VuZXJhdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Jlc3VtZVVybCwgc2V0UmVzdW1lVXJsXSA9IHVzZVN0YXRlKFwiXCIpO1xuICBjb25zdCBbcmVzdW1lRGF0YSwgc2V0UmVzdW1lRGF0YV0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2F0c1Njb3JlLCBzZXRBdHNTY29yZV0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3N1Z2dlc3Rpb25zLCBzZXRTdWdnZXN0aW9uc10gPSB1c2VTdGF0ZShbXSk7XG4gIGNvbnN0IFtzaG93UHJldmlldywgc2V0U2hvd1ByZXZpZXddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIE5ldyBzdGF0ZSBmb3IgdXBsb2FkIGZ1bmN0aW9uYWxpdHlcbiAgY29uc3QgW2J1aWxkZXJNb2RlLCBzZXRCdWlsZGVyTW9kZV0gPSB1c2VTdGF0ZSgnY3JlYXRlJyk7IC8vICdjcmVhdGUnLCAndXBsb2FkJywgJ2FuYWx5emUnXG4gIGNvbnN0IFt1cGxvYWRBbmFseXNpcywgc2V0VXBsb2FkQW5hbHlzaXNdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtzaG93QW5hbHlzaXMsIHNldFNob3dBbmFseXNpc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93VXBsb2FkV29ya2Zsb3csIHNldFNob3dVcGxvYWRXb3JrZmxvd10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gRW5oYW5jZWQgdXBsb2FkIHdvcmtmbG93IHN0YXRlXG4gIGNvbnN0IFtzaG93Sm9iRGVzY3JpcHRpb25JbnB1dCwgc2V0U2hvd0pvYkRlc2NyaXB0aW9uSW5wdXRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNHZW5lcmF0aW5nVGFyZ2V0ZWQsIHNldElzR2VuZXJhdGluZ1RhcmdldGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3RhcmdldGVkUmVzdW1lRGF0YSwgc2V0VGFyZ2V0ZWRSZXN1bWVEYXRhXSA9IHVzZVN0YXRlKG51bGwpO1xuXG4gIC8vIFRlbXBsYXRlIHNlbGVjdGlvbiBzdGF0ZVxuICBjb25zdCBbc2VsZWN0ZWRUZW1wbGF0ZSwgc2V0U2VsZWN0ZWRUZW1wbGF0ZV0gPSB1c2VTdGF0ZSgnbW9kZXJuJyk7XG4gIGNvbnN0IFtzaG93VGVtcGxhdGVTZWxlY3Rvciwgc2V0U2hvd1RlbXBsYXRlU2VsZWN0b3JdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEZvcm0gZGF0YSBzdGF0ZVxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBwZXJzb25hbDoge1xuICAgICAgZmlyc3ROYW1lOiBcIlwiLFxuICAgICAgbGFzdE5hbWU6IFwiXCIsXG4gICAgICBlbWFpbDogXCJcIixcbiAgICAgIHBob25lOiBcIlwiLFxuICAgICAgbG9jYXRpb246IFwiXCIsXG4gICAgICBsaW5rZWRpbjogXCJcIixcbiAgICAgIHBvcnRmb2xpbzogXCJcIixcbiAgICAgIHN1bW1hcnk6IFwiXCJcbiAgICB9LFxuICAgIGVkdWNhdGlvbjogW1xuICAgICAge1xuICAgICAgICBpZDogMSxcbiAgICAgICAgZGVncmVlOiBcIlwiLFxuICAgICAgICBpbnN0aXR1dGlvbjogXCJcIixcbiAgICAgICAgbG9jYXRpb246IFwiXCIsXG4gICAgICAgIHN0YXJ0RGF0ZTogXCJcIixcbiAgICAgICAgZW5kRGF0ZTogXCJcIixcbiAgICAgICAgZ3BhOiBcIlwiLFxuICAgICAgICByZWxldmFudDogXCJcIlxuICAgICAgfVxuICAgIF0sXG4gICAgZXhwZXJpZW5jZTogW1xuICAgICAge1xuICAgICAgICBpZDogMSxcbiAgICAgICAgdGl0bGU6IFwiXCIsXG4gICAgICAgIGNvbXBhbnk6IFwiXCIsXG4gICAgICAgIGxvY2F0aW9uOiBcIlwiLFxuICAgICAgICBzdGFydERhdGU6IFwiXCIsXG4gICAgICAgIGVuZERhdGU6IFwiXCIsXG4gICAgICAgIGN1cnJlbnQ6IGZhbHNlLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJcIlxuICAgICAgfVxuICAgIF0sXG4gICAgc2tpbGxzOiB7XG4gICAgICB0ZWNobmljYWw6IFtdLFxuICAgICAgbGFuZ3VhZ2VzOiBbXSxcbiAgICAgIGNlcnRpZmljYXRpb25zOiBbXVxuICAgIH0sXG4gICAgcHJvamVjdHM6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6IDEsXG4gICAgICAgIG5hbWU6IFwiXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlwiLFxuICAgICAgICB0ZWNobm9sb2dpZXM6IFwiXCIsXG4gICAgICAgIGxpbms6IFwiXCJcbiAgICAgIH1cbiAgICBdXG4gIH0pO1xuXG4gIC8vIFJlYWwtdGltZSBBVFMgYW5hbHlzaXNcbiAgY29uc3QgYXRzQW5hbHlzaXMgPSB1c2VBVFNBbmFseXNpcyhmb3JtRGF0YSk7XG5cbiAgLy8gU3RlcHMgY29uZmlndXJhdGlvblxuICBjb25zdCBzdGVwcyA9IFtcbiAgICB7XG4gICAgICBpZDogMCxcbiAgICAgIHRpdGxlOiBcIlBlcnNvbmFsIEluZm9ybWF0aW9uXCIsXG4gICAgICBpY29uOiBVc2VyLFxuICAgICAgZGVzY3JpcHRpb246IFwiVGVsbCB1cyBhYm91dCB5b3Vyc2VsZlwiXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogMSxcbiAgICAgIHRpdGxlOiBcIkVkdWNhdGlvblwiLFxuICAgICAgaWNvbjogR3JhZHVhdGlvbkNhcCxcbiAgICAgIGRlc2NyaXB0aW9uOiBcIllvdXIgYWNhZGVtaWMgYmFja2dyb3VuZFwiXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogMixcbiAgICAgIHRpdGxlOiBcIkV4cGVyaWVuY2VcIixcbiAgICAgIGljb246IEJyaWVmY2FzZSxcbiAgICAgIGRlc2NyaXB0aW9uOiBcIllvdXIgd29yayBleHBlcmllbmNlXCJcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAzLFxuICAgICAgdGl0bGU6IFwiU2tpbGxzICYgUHJvamVjdHNcIixcbiAgICAgIGljb246IEF3YXJkLFxuICAgICAgZGVzY3JpcHRpb246IFwiU2hvd2Nhc2UgeW91ciBhYmlsaXRpZXNcIlxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDQsXG4gICAgICB0aXRsZTogXCJSZXZpZXcgJiBHZW5lcmF0ZVwiLFxuICAgICAgaWNvbjogRmlsZVRleHQsXG4gICAgICBkZXNjcmlwdGlvbjogXCJGaW5hbGl6ZSB5b3VyIHJlc3VtZVwiXG4gICAgfVxuICBdO1xuXG4gIC8vIFVwZGF0ZSBmb3JtIGRhdGFcbiAgY29uc3QgdXBkYXRlRm9ybURhdGEgPSAoc2VjdGlvbiwgZmllbGQsIHZhbHVlLCBpbmRleCA9IG51bGwpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+IHtcbiAgICAgIGlmIChpbmRleCAhPT0gbnVsbCAmJiBBcnJheS5pc0FycmF5KHByZXZbc2VjdGlvbl0pKSB7XG4gICAgICAgIGNvbnN0IG5ld0FycmF5ID0gWy4uLnByZXZbc2VjdGlvbl1dO1xuICAgICAgICBuZXdBcnJheVtpbmRleF0gPSB7IC4uLm5ld0FycmF5W2luZGV4XSwgW2ZpZWxkXTogdmFsdWUgfTtcbiAgICAgICAgcmV0dXJuIHsgLi4ucHJldiwgW3NlY3Rpb25dOiBuZXdBcnJheSB9O1xuICAgICAgfSBlbHNlIGlmICh0eXBlb2YgcHJldltzZWN0aW9uXSA9PT0gJ29iamVjdCcgJiYgIUFycmF5LmlzQXJyYXkocHJldltzZWN0aW9uXSkpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIFtzZWN0aW9uXTogeyAuLi5wcmV2W3NlY3Rpb25dLCBbZmllbGRdOiB2YWx1ZSB9XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICByZXR1cm4gcHJldjtcbiAgICB9KTtcbiAgfTtcblxuICAvLyBBZGQgbmV3IGl0ZW0gdG8gYXJyYXkgc2VjdGlvbnNcbiAgY29uc3QgYWRkQXJyYXlJdGVtID0gKHNlY3Rpb24sIHRlbXBsYXRlKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtzZWN0aW9uXTogWy4uLnByZXZbc2VjdGlvbl0sIHsgLi4udGVtcGxhdGUsIGlkOiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgMTEpIH1dXG4gICAgfSkpO1xuICB9O1xuXG4gIC8vIFJlbW92ZSBpdGVtIGZyb20gYXJyYXkgc2VjdGlvbnNcbiAgY29uc3QgcmVtb3ZlQXJyYXlJdGVtID0gKHNlY3Rpb24sIGlkKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtzZWN0aW9uXTogcHJldltzZWN0aW9uXS5maWx0ZXIoaXRlbSA9PiBpdGVtLmlkICE9PSBpZClcbiAgICB9KSk7XG4gIH07XG5cbiAgLy8gTmF2aWdhdGlvbiBmdW5jdGlvbnNcbiAgY29uc3QgbmV4dFN0ZXAgPSAoKSA9PiB7XG4gICAgaWYgKGN1cnJlbnRTdGVwIDwgc3RlcHMubGVuZ3RoIC0gMSkge1xuICAgICAgc2V0Q3VycmVudFN0ZXAoY3VycmVudFN0ZXAgKyAxKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcHJldlN0ZXAgPSAoKSA9PiB7XG4gICAgaWYgKGN1cnJlbnRTdGVwID4gMCkge1xuICAgICAgc2V0Q3VycmVudFN0ZXAoY3VycmVudFN0ZXAgLSAxKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gR2VuZXJhdGUgcmVzdW1lIHdpdGggQUlcbiAgY29uc3QgZ2VuZXJhdGVSZXN1bWUgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5qAIFN0YXJ0aW5nIHJlc3VtZSBnZW5lcmF0aW9uLi4uJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TnSBGb3JtIGRhdGE6JywgZm9ybURhdGEpO1xuXG4gICAgICBzZXRJc0dlbmVyYXRpbmcodHJ1ZSk7XG4gICAgICBzZXRTaG93UHJvZ3Jlc3NCYXIodHJ1ZSk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZ2VuZXJhdGUtcmVzdW1lJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBmb3JtRGF0YSxcbiAgICAgICAgICB0ZW1wbGF0ZUlkOiBzZWxlY3RlZFRlbXBsYXRlXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OhIEFQSSByZXNwb25zZSBzdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OhIEFQSSByZXNwb25zZSBoZWFkZXJzOicsIE9iamVjdC5mcm9tRW50cmllcyhyZXNwb25zZS5oZWFkZXJzLmVudHJpZXMoKSkpO1xuXG4gICAgICAvLyBDaGVjayBpZiByZXNwb25zZSBpcyBhY3R1YWxseSBKU09OXG4gICAgICBjb25zdCBjb250ZW50VHlwZSA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCdjb250ZW50LXR5cGUnKTtcbiAgICAgIGlmICghY29udGVudFR5cGUgfHwgIWNvbnRlbnRUeXBlLmluY2x1ZGVzKCdhcHBsaWNhdGlvbi9qc29uJykpIHtcbiAgICAgICAgY29uc3QgdGV4dFJlc3BvbnNlID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgTm9uLUpTT04gcmVzcG9uc2UgcmVjZWl2ZWQ6JywgdGV4dFJlc3BvbnNlKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTZXJ2ZXIgcmV0dXJuZWQgbm9uLUpTT04gcmVzcG9uc2UnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OKIEFQSSByZXNwb25zZSBkYXRhOicsIGRhdGEpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihkYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gZ2VuZXJhdGUgcmVzdW1lJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFN0b3JlIHRoZSBlbmhhbmNlZCByZXN1bWUgZGF0YSBhbmQgQVRTIGluZm9ybWF0aW9uXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFNldHRpbmcgcmVzdW1lIGRhdGEuLi4nKTtcbiAgICAgIHNldFJlc3VtZVVybChkYXRhLmRvd25sb2FkVXJsKTtcbiAgICAgIHNldFJlc3VtZURhdGEoZGF0YS5yZXN1bWVEYXRhKTtcbiAgICAgIHNldEF0c1Njb3JlKGRhdGEuYXRzU2NvcmUgfHwgZGF0YS5yZXN1bWVEYXRhPy5hdHNTY29yZT8ub3ZlcmFsbCB8fCA3NSk7XG4gICAgICBzZXRTdWdnZXN0aW9ucyhkYXRhLnN1Z2dlc3Rpb25zIHx8IGRhdGEucmVzdW1lRGF0YT8uYXRzU2NvcmU/LmltcHJvdmVtZW50cyB8fCBbXSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn46JIFJlc3VtZSBnZW5lcmF0aW9uIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHknKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwi8J+SpSBFcnJvciBnZW5lcmF0aW5nIHJlc3VtZTpcIiwgZXJyb3IpO1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGRldGFpbHM6XCIsIHtcbiAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgc3RhY2s6IGVycm9yLnN0YWNrLFxuICAgICAgICBuYW1lOiBlcnJvci5uYW1lXG4gICAgICB9KTtcblxuICAgICAgdG9hc3QuZXJyb3IoZXJyb3IubWVzc2FnZSB8fCBcIkZhaWxlZCB0byBnZW5lcmF0ZSByZXN1bWVcIik7XG4gICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpO1xuICAgICAgc2V0U2hvd1Byb2dyZXNzQmFyKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUHJvZ3Jlc3NDb21wbGV0ZSA9ICgpID0+IHtcbiAgICBzZXRTaG93UHJvZ3Jlc3NCYXIoZmFsc2UpO1xuICAgIHNldFJlc3VtZUdlbmVyYXRlZCh0cnVlKTtcbiAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpO1xuXG4gICAgLy8gU2hvdyBzdWNjZXNzIG1lc3NhZ2Ugd2l0aCBBVFMgc2NvcmVcbiAgICBjb25zdCBzY29yZU1lc3NhZ2UgPSBhdHNTY29yZVxuICAgICAgPyBgUmVzdW1lIGdlbmVyYXRlZCEgQVRTIFNjb3JlOiAke2F0c1Njb3JlfSVgXG4gICAgICA6IFwiWW91ciByZXN1bWUgaGFzIGJlZW4gZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSFcIjtcbiAgICB0b2FzdC5zdWNjZXNzKHNjb3JlTWVzc2FnZSk7XG4gIH07XG5cbiAgLy8gSGFuZGxlIHVwbG9hZCBhbmFseXNpcyBjb21wbGV0aW9uXG4gIGNvbnN0IGhhbmRsZVVwbG9hZEFuYWx5c2lzID0gKGFuYWx5c2lzRGF0YSkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5OKIFVwbG9hZCBhbmFseXNpcyByZWNlaXZlZDonLCBhbmFseXNpc0RhdGEpO1xuICAgIHNldFVwbG9hZEFuYWx5c2lzKGFuYWx5c2lzRGF0YSk7XG5cbiAgICAvLyBGb3IgdXBsb2FkICYgZW5oYW5jZSBtb2RlLCBzaG93IGpvYiBkZXNjcmlwdGlvbiBpbnB1dFxuICAgIGlmIChidWlsZGVyTW9kZSA9PT0gJ3VwbG9hZCcpIHtcbiAgICAgIHNldFNob3dKb2JEZXNjcmlwdGlvbklucHV0KHRydWUpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBGb3IgcXVpY2sgYW5hbHlzaXMsIHNob3cgcmVzdWx0cyBpbW1lZGlhdGVseVxuICAgICAgc2V0U2hvd0FuYWx5c2lzKHRydWUpO1xuICAgIH1cblxuICAgIC8vIElmIGl0J3MgYSBmdWxsIGFuYWx5c2lzLCBwb3B1bGF0ZSBmb3JtIGRhdGEgKGV2ZW4gd2l0aCBtaW5pbWFsIGRhdGEpXG4gICAgaWYgKGFuYWx5c2lzRGF0YS5hbmFseXNpc1R5cGUgPT09ICdmdWxsJyAmJiBhbmFseXNpc0RhdGEuZXh0cmFjdGVkRGF0YSkge1xuICAgICAgY29uc3QgZXh0cmFjdGVkID0gYW5hbHlzaXNEYXRhLmV4dHJhY3RlZERhdGE7XG4gICAgICBjb25zb2xlLmxvZygn8J+TiyBFeHRyYWN0ZWQgZGF0YSBmb3IgZm9ybSBwb3B1bGF0aW9uOicsIGV4dHJhY3RlZCk7XG5cbiAgICAgIC8vIFVwZGF0ZSBmb3JtIGRhdGEgd2l0aCBleHRyYWN0ZWQgaW5mb3JtYXRpb25cbiAgICAgIHNldEZvcm1EYXRhKHByZXZEYXRhID0+ICh7XG4gICAgICAgIC4uLnByZXZEYXRhLFxuICAgICAgICBwZXJzb25hbDoge1xuICAgICAgICAgIC4uLnByZXZEYXRhLnBlcnNvbmFsLFxuICAgICAgICAgIGZpcnN0TmFtZTogZXh0cmFjdGVkLnBlcnNvbmFsPy5maXJzdE5hbWUgfHwgcHJldkRhdGEucGVyc29uYWwuZmlyc3ROYW1lLFxuICAgICAgICAgIGxhc3ROYW1lOiBleHRyYWN0ZWQucGVyc29uYWw/Lmxhc3ROYW1lIHx8IHByZXZEYXRhLnBlcnNvbmFsLmxhc3ROYW1lLFxuICAgICAgICAgIGVtYWlsOiBleHRyYWN0ZWQucGVyc29uYWw/LmVtYWlsIHx8IHByZXZEYXRhLnBlcnNvbmFsLmVtYWlsLFxuICAgICAgICAgIHBob25lOiBleHRyYWN0ZWQucGVyc29uYWw/LnBob25lIHx8IHByZXZEYXRhLnBlcnNvbmFsLnBob25lLFxuICAgICAgICAgIGxvY2F0aW9uOiBleHRyYWN0ZWQucGVyc29uYWw/LmxvY2F0aW9uIHx8IHByZXZEYXRhLnBlcnNvbmFsLmxvY2F0aW9uLFxuICAgICAgICAgIGxpbmtlZGluOiBleHRyYWN0ZWQucGVyc29uYWw/LmxpbmtlZGluIHx8IHByZXZEYXRhLnBlcnNvbmFsLmxpbmtlZGluLFxuICAgICAgICAgIHBvcnRmb2xpbzogZXh0cmFjdGVkLnBlcnNvbmFsPy5wb3J0Zm9saW8gfHwgcHJldkRhdGEucGVyc29uYWwucG9ydGZvbGlvLFxuICAgICAgICAgIHN1bW1hcnk6IGV4dHJhY3RlZC5wZXJzb25hbD8uc3VtbWFyeSB8fCBwcmV2RGF0YS5wZXJzb25hbC5zdW1tYXJ5XG4gICAgICAgIH0sXG4gICAgICAgIGVkdWNhdGlvbjogZXh0cmFjdGVkLmVkdWNhdGlvbj8ubGVuZ3RoID4gMCA/IGV4dHJhY3RlZC5lZHVjYXRpb24ubWFwKGVkdSA9PiAoe1xuICAgICAgICAgIC4uLmVkdSxcbiAgICAgICAgICBpZDogZWR1LmlkIHx8IERhdGUubm93KCkgKyBNYXRoLnJhbmRvbSgpXG4gICAgICAgIH0pKSA6IHByZXZEYXRhLmVkdWNhdGlvbixcbiAgICAgICAgZXhwZXJpZW5jZTogZXh0cmFjdGVkLmV4cGVyaWVuY2U/Lmxlbmd0aCA+IDAgPyBleHRyYWN0ZWQuZXhwZXJpZW5jZS5tYXAoZXhwID0+ICh7XG4gICAgICAgICAgLi4uZXhwLFxuICAgICAgICAgIGlkOiBleHAuaWQgfHwgRGF0ZS5ub3coKSArIE1hdGgucmFuZG9tKClcbiAgICAgICAgfSkpIDogcHJldkRhdGEuZXhwZXJpZW5jZSxcbiAgICAgICAgc2tpbGxzOiB7XG4gICAgICAgICAgdGVjaG5pY2FsOiBleHRyYWN0ZWQuc2tpbGxzPy50ZWNobmljYWw/Lmxlbmd0aCA+IDAgPyBleHRyYWN0ZWQuc2tpbGxzLnRlY2huaWNhbCA6IHByZXZEYXRhLnNraWxscy50ZWNobmljYWwsXG4gICAgICAgICAgbGFuZ3VhZ2VzOiBleHRyYWN0ZWQuc2tpbGxzPy5sYW5ndWFnZXM/Lmxlbmd0aCA+IDAgPyBleHRyYWN0ZWQuc2tpbGxzLmxhbmd1YWdlcyA6IHByZXZEYXRhLnNraWxscy5sYW5ndWFnZXMsXG4gICAgICAgICAgY2VydGlmaWNhdGlvbnM6IGV4dHJhY3RlZC5za2lsbHM/LmNlcnRpZmljYXRpb25zPy5sZW5ndGggPiAwID8gZXh0cmFjdGVkLnNraWxscy5jZXJ0aWZpY2F0aW9ucyA6IHByZXZEYXRhLnNraWxscy5jZXJ0aWZpY2F0aW9uc1xuICAgICAgICB9LFxuICAgICAgICBwcm9qZWN0czogZXh0cmFjdGVkLnByb2plY3RzPy5sZW5ndGggPiAwID8gZXh0cmFjdGVkLnByb2plY3RzLm1hcChwcm9qID0+ICh7XG4gICAgICAgICAgLi4ucHJvaixcbiAgICAgICAgICBpZDogcHJvai5pZCB8fCBEYXRlLm5vdygpICsgTWF0aC5yYW5kb20oKVxuICAgICAgICB9KSkgOiBwcmV2RGF0YS5wcm9qZWN0c1xuICAgICAgfSkpO1xuXG4gICAgICAvLyBTZXQgQVRTIHNjb3JlIGFuZCBzdWdnZXN0aW9uc1xuICAgICAgaWYgKGFuYWx5c2lzRGF0YS5hdHNTY29yZSkge1xuICAgICAgICBzZXRBdHNTY29yZShhbmFseXNpc0RhdGEuYXRzU2NvcmUub3ZlcmFsbCk7XG4gICAgICAgIHNldFN1Z2dlc3Rpb25zKGFuYWx5c2lzRGF0YS5lbmhhbmNlbWVudHM/LnN1Z2dlc3Rpb25zIHx8IGFuYWx5c2lzRGF0YS5hbmFseXNpcz8ucmVjb21tZW5kYXRpb25zIHx8IFtdKTtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ+KchSBGb3JtIGRhdGEgdXBkYXRlZCB3aXRoIGV4dHJhY3RlZCBpbmZvcm1hdGlvbicpO1xuICAgIH0gZWxzZSBpZiAoYW5hbHlzaXNEYXRhLmZhbGxiYWNrKSB7XG4gICAgICBjb25zb2xlLmxvZygn4pqg77iPIFVzaW5nIGZhbGxiYWNrIGRhdGEgLSBtaW5pbWFsIGV4dHJhY3Rpb24nKTtcbiAgICAgIC8vIEV2ZW4gd2l0aCBmYWxsYmFjaywgdHJ5IHRvIGV4dHJhY3QgYW55IGF2YWlsYWJsZSBpbmZvcm1hdGlvblxuICAgICAgaWYgKGFuYWx5c2lzRGF0YS5leHRyYWN0ZWREYXRhKSB7XG4gICAgICAgIGNvbnN0IGV4dHJhY3RlZCA9IGFuYWx5c2lzRGF0YS5leHRyYWN0ZWREYXRhO1xuICAgICAgICBzZXRGb3JtRGF0YShwcmV2RGF0YSA9PiAoe1xuICAgICAgICAgIC4uLnByZXZEYXRhLFxuICAgICAgICAgIHBlcnNvbmFsOiB7XG4gICAgICAgICAgICAuLi5wcmV2RGF0YS5wZXJzb25hbCxcbiAgICAgICAgICAgIHN1bW1hcnk6IGV4dHJhY3RlZC5wZXJzb25hbD8uc3VtbWFyeSB8fCAnUGxlYXNlIGFkZCB5b3VyIHByb2Zlc3Npb25hbCBzdW1tYXJ5IGhlcmUuJ1xuICAgICAgICAgIH1cbiAgICAgICAgfSkpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyBIYW5kbGUgam9iIGRlc2NyaXB0aW9uIHN1Ym1pc3Npb24gZm9yIHRhcmdldGVkIHJlc3VtZSBnZW5lcmF0aW9uXG4gIGNvbnN0IGhhbmRsZUpvYkRlc2NyaXB0aW9uU3VibWl0ID0gYXN5bmMgKGpvYkRhdGEpID0+IHtcbiAgICBpZiAoIXVwbG9hZEFuYWx5c2lzPy5leHRyYWN0ZWREYXRhKSB7XG4gICAgICB0b2FzdC5lcnJvcignTm8gcmVzdW1lIGRhdGEgYXZhaWxhYmxlLiBQbGVhc2UgdXBsb2FkIGEgcmVzdW1lIGZpcnN0LicpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBzZXRJc0dlbmVyYXRpbmdUYXJnZXRlZCh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn46vIEdlbmVyYXRpbmcgdGFyZ2V0ZWQgcmVzdW1lIHdpdGggam9iIGRhdGE6Jywgam9iRGF0YSk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZ2VuZXJhdGUtdGFyZ2V0ZWQtcmVzdW1lJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBleHRyYWN0ZWRSZXN1bWVEYXRhOiB1cGxvYWRBbmFseXNpcy5leHRyYWN0ZWREYXRhLFxuICAgICAgICAgIGpvYkRlc2NyaXB0aW9uOiBqb2JEYXRhLmRlc2NyaXB0aW9uLFxuICAgICAgICAgIGpvYlRpdGxlOiBqb2JEYXRhLmpvYlRpdGxlLFxuICAgICAgICAgIGNvbXBhbnk6IGpvYkRhdGEuY29tcGFueVxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4ogVGFyZ2V0ZWQgcmVzdW1lIHJlc3BvbnNlOicsIGRhdGEpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihkYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gZ2VuZXJhdGUgdGFyZ2V0ZWQgcmVzdW1lJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFVwZGF0ZSBmb3JtIGRhdGEgd2l0aCBlbmhhbmNlZCByZXN1bWVcbiAgICAgIGlmIChkYXRhLmVuaGFuY2VkUmVzdW1lKSB7XG4gICAgICAgIC8vIFByb3Blcmx5IHN0cnVjdHVyZSB0aGUgZW5oYW5jZWQgcmVzdW1lIGRhdGEgdG8gbWF0Y2ggZm9ybSBkYXRhIHN0cnVjdHVyZVxuICAgICAgICBjb25zdCBlbmhhbmNlZEZvcm1EYXRhID0ge1xuICAgICAgICAgIHBlcnNvbmFsOiB7XG4gICAgICAgICAgICBmaXJzdE5hbWU6IGRhdGEuZW5oYW5jZWRSZXN1bWUucGVyc29uYWw/LmZpcnN0TmFtZSB8fCAnJyxcbiAgICAgICAgICAgIGxhc3ROYW1lOiBkYXRhLmVuaGFuY2VkUmVzdW1lLnBlcnNvbmFsPy5sYXN0TmFtZSB8fCAnJyxcbiAgICAgICAgICAgIGVtYWlsOiBkYXRhLmVuaGFuY2VkUmVzdW1lLnBlcnNvbmFsPy5lbWFpbCB8fCAnJyxcbiAgICAgICAgICAgIHBob25lOiBkYXRhLmVuaGFuY2VkUmVzdW1lLnBlcnNvbmFsPy5waG9uZSB8fCAnJyxcbiAgICAgICAgICAgIGxvY2F0aW9uOiBkYXRhLmVuaGFuY2VkUmVzdW1lLnBlcnNvbmFsPy5sb2NhdGlvbiB8fCAnJyxcbiAgICAgICAgICAgIGxpbmtlZGluOiBkYXRhLmVuaGFuY2VkUmVzdW1lLnBlcnNvbmFsPy5saW5rZWRpbiB8fCAnJyxcbiAgICAgICAgICAgIHBvcnRmb2xpbzogZGF0YS5lbmhhbmNlZFJlc3VtZS5wZXJzb25hbD8ucG9ydGZvbGlvIHx8ICcnLFxuICAgICAgICAgICAgc3VtbWFyeTogZGF0YS5lbmhhbmNlZFJlc3VtZS5wZXJzb25hbD8uc3VtbWFyeSB8fCAnJ1xuICAgICAgICAgIH0sXG4gICAgICAgICAgZWR1Y2F0aW9uOiBkYXRhLmVuaGFuY2VkUmVzdW1lLmVkdWNhdGlvbj8ubGVuZ3RoID4gMFxuICAgICAgICAgICAgPyBkYXRhLmVuaGFuY2VkUmVzdW1lLmVkdWNhdGlvbi5tYXAoZWR1ID0+ICh7XG4gICAgICAgICAgICAgICAgaWQ6IGVkdS5pZCB8fCBEYXRlLm5vdygpICsgTWF0aC5yYW5kb20oKSxcbiAgICAgICAgICAgICAgICBkZWdyZWU6IGVkdS5kZWdyZWUgfHwgJycsXG4gICAgICAgICAgICAgICAgaW5zdGl0dXRpb246IGVkdS5pbnN0aXR1dGlvbiB8fCAnJyxcbiAgICAgICAgICAgICAgICBsb2NhdGlvbjogZWR1LmxvY2F0aW9uIHx8ICcnLFxuICAgICAgICAgICAgICAgIHN0YXJ0RGF0ZTogZWR1LnN0YXJ0RGF0ZSB8fCAnJyxcbiAgICAgICAgICAgICAgICBlbmREYXRlOiBlZHUuZW5kRGF0ZSB8fCAnJyxcbiAgICAgICAgICAgICAgICBncGE6IGVkdS5ncGEgfHwgJycsXG4gICAgICAgICAgICAgICAgcmVsZXZhbnQ6IGVkdS5yZWxldmFudCB8fCAnJ1xuICAgICAgICAgICAgICB9KSlcbiAgICAgICAgICAgIDogW3sgaWQ6IDEsIGRlZ3JlZTogXCJcIiwgaW5zdGl0dXRpb246IFwiXCIsIGxvY2F0aW9uOiBcIlwiLCBzdGFydERhdGU6IFwiXCIsIGVuZERhdGU6IFwiXCIsIGdwYTogXCJcIiwgcmVsZXZhbnQ6IFwiXCIgfV0sXG4gICAgICAgICAgZXhwZXJpZW5jZTogZGF0YS5lbmhhbmNlZFJlc3VtZS5leHBlcmllbmNlPy5sZW5ndGggPiAwXG4gICAgICAgICAgICA/IGRhdGEuZW5oYW5jZWRSZXN1bWUuZXhwZXJpZW5jZS5tYXAoZXhwID0+ICh7XG4gICAgICAgICAgICAgICAgaWQ6IGV4cC5pZCB8fCBEYXRlLm5vdygpICsgTWF0aC5yYW5kb20oKSxcbiAgICAgICAgICAgICAgICB0aXRsZTogZXhwLnRpdGxlIHx8ICcnLFxuICAgICAgICAgICAgICAgIGNvbXBhbnk6IGV4cC5jb21wYW55IHx8ICcnLFxuICAgICAgICAgICAgICAgIGxvY2F0aW9uOiBleHAubG9jYXRpb24gfHwgJycsXG4gICAgICAgICAgICAgICAgc3RhcnREYXRlOiBleHAuc3RhcnREYXRlIHx8ICcnLFxuICAgICAgICAgICAgICAgIGVuZERhdGU6IGV4cC5lbmREYXRlIHx8ICcnLFxuICAgICAgICAgICAgICAgIGN1cnJlbnQ6IGV4cC5jdXJyZW50IHx8IGZhbHNlLFxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBleHAuZGVzY3JpcHRpb24gfHwgKGV4cC5hY2hpZXZlbWVudHMgPyBleHAuYWNoaWV2ZW1lbnRzLmpvaW4oJ1xcbicpIDogJycpXG4gICAgICAgICAgICAgIH0pKVxuICAgICAgICAgICAgOiBbeyBpZDogMSwgdGl0bGU6IFwiXCIsIGNvbXBhbnk6IFwiXCIsIGxvY2F0aW9uOiBcIlwiLCBzdGFydERhdGU6IFwiXCIsIGVuZERhdGU6IFwiXCIsIGN1cnJlbnQ6IGZhbHNlLCBkZXNjcmlwdGlvbjogXCJcIiB9XSxcbiAgICAgICAgICBza2lsbHM6IHtcbiAgICAgICAgICAgIHRlY2huaWNhbDogZGF0YS5lbmhhbmNlZFJlc3VtZS5za2lsbHM/LnRlY2huaWNhbCB8fCBbXSxcbiAgICAgICAgICAgIGxhbmd1YWdlczogZGF0YS5lbmhhbmNlZFJlc3VtZS5za2lsbHM/Lmxhbmd1YWdlcyB8fCBbXSxcbiAgICAgICAgICAgIGNlcnRpZmljYXRpb25zOiBkYXRhLmVuaGFuY2VkUmVzdW1lLnNraWxscz8uY2VydGlmaWNhdGlvbnMgfHwgW11cbiAgICAgICAgICB9LFxuICAgICAgICAgIHByb2plY3RzOiBkYXRhLmVuaGFuY2VkUmVzdW1lLnByb2plY3RzPy5sZW5ndGggPiAwXG4gICAgICAgICAgICA/IGRhdGEuZW5oYW5jZWRSZXN1bWUucHJvamVjdHMubWFwKHByb2ogPT4gKHtcbiAgICAgICAgICAgICAgICBpZDogcHJvai5pZCB8fCBEYXRlLm5vdygpICsgTWF0aC5yYW5kb20oKSxcbiAgICAgICAgICAgICAgICBuYW1lOiBwcm9qLm5hbWUgfHwgJycsXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IHByb2ouZGVzY3JpcHRpb24gfHwgJycsXG4gICAgICAgICAgICAgICAgdGVjaG5vbG9naWVzOiBwcm9qLnRlY2hub2xvZ2llcyB8fCAnJyxcbiAgICAgICAgICAgICAgICBsaW5rOiBwcm9qLmxpbmsgfHwgJydcbiAgICAgICAgICAgICAgfSkpXG4gICAgICAgICAgICA6IFt7IGlkOiAxLCBuYW1lOiBcIlwiLCBkZXNjcmlwdGlvbjogXCJcIiwgdGVjaG5vbG9naWVzOiBcIlwiLCBsaW5rOiBcIlwiIH1dXG4gICAgICAgIH07XG5cbiAgICAgICAgc2V0Rm9ybURhdGEoZW5oYW5jZWRGb3JtRGF0YSk7XG4gICAgICAgIHNldFRhcmdldGVkUmVzdW1lRGF0YShkYXRhKTtcbiAgICAgICAgc2V0QXRzU2NvcmUoZGF0YS5hdHNTY29yZT8ub3ZlcmFsbCB8fCA4NSk7XG4gICAgICAgIHNldFN1Z2dlc3Rpb25zKGRhdGEucmVjb21tZW5kYXRpb25zIHx8IFtdKTtcblxuICAgICAgICAvLyBTaG93IHN1Y2Nlc3MgYW5kIG1vdmUgdG8gZm9ybSBlZGl0aW5nXG4gICAgICAgIHNldFNob3dKb2JEZXNjcmlwdGlvbklucHV0KGZhbHNlKTtcbiAgICAgICAgc2V0Q3VycmVudFN0ZXAoMCk7XG4gICAgICAgIHRvYXN0LnN1Y2Nlc3MoJ1Jlc3VtZSBvcHRpbWl6ZWQgZm9yIHRoZSB0YXJnZXQgam9iIScpO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1RhcmdldGVkIHJlc3VtZSBnZW5lcmF0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBnZW5lcmF0ZSB0YXJnZXRlZCByZXN1bWUnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNHZW5lcmF0aW5nVGFyZ2V0ZWQoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBIYW5kbGUgbW9kZSBjaGFuZ2VcbiAgY29uc3QgaGFuZGxlTW9kZUNoYW5nZSA9IChtb2RlKSA9PiB7XG4gICAgc2V0QnVpbGRlck1vZGUobW9kZSk7XG4gICAgc2V0VXBsb2FkQW5hbHlzaXMobnVsbCk7XG4gICAgc2V0U2hvd0FuYWx5c2lzKGZhbHNlKTtcbiAgICBzZXRSZXN1bWVHZW5lcmF0ZWQoZmFsc2UpO1xuICAgIHNldEN1cnJlbnRTdGVwKDApO1xuICAgIHNldFNob3dKb2JEZXNjcmlwdGlvbklucHV0KGZhbHNlKTtcbiAgICBzZXRJc0dlbmVyYXRpbmdUYXJnZXRlZChmYWxzZSk7XG4gICAgc2V0VGFyZ2V0ZWRSZXN1bWVEYXRhKG51bGwpO1xuXG4gICAgLy8gU2hvdyBlbmhhbmNlZCB3b3JrZmxvdyBmb3IgdXBsb2FkIG1vZGVcbiAgICBpZiAobW9kZSA9PT0gJ3VwbG9hZCcpIHtcbiAgICAgIHNldFNob3dVcGxvYWRXb3JrZmxvdyh0cnVlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0U2hvd1VwbG9hZFdvcmtmbG93KGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gUmVzZXQgdG8gY3JlYXRlIG1vZGVcbiAgY29uc3QgcmVzZXRUb0NyZWF0ZU1vZGUgPSAoKSA9PiB7XG4gICAgc2V0QnVpbGRlck1vZGUoJ2NyZWF0ZScpO1xuICAgIHNldFVwbG9hZEFuYWx5c2lzKG51bGwpO1xuICAgIHNldFNob3dBbmFseXNpcyhmYWxzZSk7XG4gICAgc2V0UmVzdW1lR2VuZXJhdGVkKGZhbHNlKTtcbiAgICBzZXRDdXJyZW50U3RlcCgwKTtcbiAgICBzZXRGb3JtRGF0YSh7XG4gICAgICBwZXJzb25hbDoge1xuICAgICAgICBmaXJzdE5hbWU6IFwiXCIsXG4gICAgICAgIGxhc3ROYW1lOiBcIlwiLFxuICAgICAgICBlbWFpbDogXCJcIixcbiAgICAgICAgcGhvbmU6IFwiXCIsXG4gICAgICAgIGxvY2F0aW9uOiBcIlwiLFxuICAgICAgICBsaW5rZWRpbjogXCJcIixcbiAgICAgICAgcG9ydGZvbGlvOiBcIlwiLFxuICAgICAgICBzdW1tYXJ5OiBcIlwiXG4gICAgICB9LFxuICAgICAgZWR1Y2F0aW9uOiBbeyBpZDogMSwgZGVncmVlOiBcIlwiLCBpbnN0aXR1dGlvbjogXCJcIiwgbG9jYXRpb246IFwiXCIsIHN0YXJ0RGF0ZTogXCJcIiwgZW5kRGF0ZTogXCJcIiwgZ3BhOiBcIlwiLCByZWxldmFudDogXCJcIiB9XSxcbiAgICAgIGV4cGVyaWVuY2U6IFt7IGlkOiAxLCB0aXRsZTogXCJcIiwgY29tcGFueTogXCJcIiwgbG9jYXRpb246IFwiXCIsIHN0YXJ0RGF0ZTogXCJcIiwgZW5kRGF0ZTogXCJcIiwgY3VycmVudDogZmFsc2UsIGRlc2NyaXB0aW9uOiBcIlwiIH1dLFxuICAgICAgc2tpbGxzOiB7IHRlY2huaWNhbDogW10sIGxhbmd1YWdlczogW10sIGNlcnRpZmljYXRpb25zOiBbXSB9LFxuICAgICAgcHJvamVjdHM6IFt7IGlkOiAxLCBuYW1lOiBcIlwiLCBkZXNjcmlwdGlvbjogXCJcIiwgdGVjaG5vbG9naWVzOiBcIlwiLCBsaW5rOiBcIlwiIH1dXG4gICAgfSk7XG4gIH07XG5cbiAgLy8gRmxvYXRpbmcgYmFja2dyb3VuZCBlbGVtZW50cyB3aXRoIGRldGVybWluaXN0aWMgdmFsdWVzIHRvIGF2b2lkIGh5ZHJhdGlvbiBtaXNtYXRjaFxuICBjb25zdCBGbG9hdGluZ0VsZW1lbnRzID0gKCkgPT4ge1xuICAgIC8vIFVzZSBkZXRlcm1pbmlzdGljIHZhbHVlcyB0byBhdm9pZCBoeWRyYXRpb24gbWlzbWF0Y2hcbiAgICBjb25zdCBlbGVtZW50cyA9IFtcbiAgICAgIHsgd2lkdGg6IDE4MCwgaGVpZ2h0OiAxNjAsIGxlZnQ6IDEwLCB0b3A6IDIwLCBkdXJhdGlvbjogMTUsIHg6IDMwLCB5OiA0MCB9LFxuICAgICAgeyB3aWR0aDogMjIwLCBoZWlnaHQ6IDE5MCwgbGVmdDogODAsIHRvcDogNjAsIGR1cmF0aW9uOiAxOCwgeDogLTI1LCB5OiAtMzAgfSxcbiAgICAgIHsgd2lkdGg6IDE1MCwgaGVpZ2h0OiAxNDAsIGxlZnQ6IDYwLCB0b3A6IDgwLCBkdXJhdGlvbjogMTIsIHg6IDM1LCB5OiAyNSB9LFxuICAgICAgeyB3aWR0aDogMjAwLCBoZWlnaHQ6IDE3MCwgbGVmdDogMzAsIHRvcDogNDAsIGR1cmF0aW9uOiAyMCwgeDogLTQwLCB5OiAzNSB9LFxuICAgICAgeyB3aWR0aDogMTcwLCBoZWlnaHQ6IDIwMCwgbGVmdDogOTAsIHRvcDogMTAsIGR1cmF0aW9uOiAxNiwgeDogMjAsIHk6IC00NSB9LFxuICAgICAgeyB3aWR0aDogMTkwLCBoZWlnaHQ6IDE1MCwgbGVmdDogMjAsIHRvcDogNzAsIGR1cmF0aW9uOiAxNCwgeDogLTMwLCB5OiAyMCB9LFxuICAgICAgeyB3aWR0aDogMTYwLCBoZWlnaHQ6IDE4MCwgbGVmdDogNzAsIHRvcDogMzAsIGR1cmF0aW9uOiAyMiwgeDogNDUsIHk6IC0yNSB9LFxuICAgICAgeyB3aWR0aDogMjEwLCBoZWlnaHQ6IDE2MCwgbGVmdDogNTAsIHRvcDogOTAsIGR1cmF0aW9uOiAxNywgeDogLTIwLCB5OiAzMCB9XG4gICAgXTtcblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgb3ZlcmZsb3ctaGlkZGVuIHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAge2VsZW1lbnRzLm1hcCgoZWxlbWVudCwgaSkgPT4gKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBrZXk9e2l9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSByb3VuZGVkLWZ1bGwgb3BhY2l0eS0xMCBibHVyLXhsXCJcbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyM4MzJFRDMnLFxuICAgICAgICAgICAgICB3aWR0aDogZWxlbWVudC53aWR0aCxcbiAgICAgICAgICAgICAgaGVpZ2h0OiBlbGVtZW50LmhlaWdodCxcbiAgICAgICAgICAgICAgbGVmdDogZWxlbWVudC5sZWZ0ICsgJyUnLFxuICAgICAgICAgICAgICB0b3A6IGVsZW1lbnQudG9wICsgJyUnLFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgICAgeDogWzAsIGVsZW1lbnQueCwgMF0sXG4gICAgICAgICAgICAgIHk6IFswLCBlbGVtZW50LnksIDBdLFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3tcbiAgICAgICAgICAgICAgZHVyYXRpb246IGVsZW1lbnQuZHVyYXRpb24sXG4gICAgICAgICAgICAgIHJlcGVhdDogSW5maW5pdHksXG4gICAgICAgICAgICAgIHJlcGVhdFR5cGU6ICdyZXZlcnNlJyxcbiAgICAgICAgICAgICAgZWFzZTogXCJlYXNlSW5PdXRcIlxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAvPlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgLy8gU2hvdyB1cGxvYWQgYW5hbHlzaXMgcmVzdWx0cyBmb3IgcXVpY2sgQVRTIGNoZWNrXG4gIGlmIChidWlsZGVyTW9kZSA9PT0gJ2FuYWx5emUnICYmIHNob3dBbmFseXNpcyAmJiB1cGxvYWRBbmFseXNpcykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iIGZyb20tYmxhY2sgdG8tWyMwQTBBMEFdIHRleHQtd2hpdGUgcmVsYXRpdmVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG9wYWNpdHktMjBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctW3VybCgnL2dyaWQuc3ZnJyldIFttYXNrLWltYWdlOnJhZGlhbC1ncmFkaWVudChlbGxpcHNlX2F0X2NlbnRlcix3aGl0ZSx0cmFuc3BhcmVudCldXCIgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPEZsb2F0aW5nRWxlbWVudHMgLz5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS0xNlwiPlxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLThcIj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5cbiAgICAgICAgICAgICAgICBBVFMgQW5hbHlzaXMgUmVzdWx0c1xuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgSGVyZSdzIHlvdXIgY29tcHJlaGVuc2l2ZSByZXN1bWUgYW5hbHlzaXMgYW5kIHJlY29tbWVuZGF0aW9uc1xuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPEFUU0FuYWx5c2lzRGlzcGxheVxuICAgICAgICAgICAgICBhbmFseXNpc0RhdGE9e3VwbG9hZEFuYWx5c2lzfVxuICAgICAgICAgICAgICBhbmFseXNpc1R5cGU9XCJxdWlja1wiXG4gICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTQganVzdGlmeS1jZW50ZXIgbXQtOFwiPlxuICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU1vZGVDaGFuZ2UoJ3VwbG9hZCcpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC04IHB5LTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLW5ldXJhbC1wdXJwbGUgdG8tbmV1cmFsLXBpbmsgdGV4dC13aGl0ZSBmb250LWJvbGQgcm91bmRlZC14bCBob3ZlcjpvcGFjaXR5LTkwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsXCJcbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjAyIH19XG4gICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxVcGxvYWQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgRW5oYW5jZSBUaGlzIFJlc3VtZVxuICAgICAgICAgICAgICA8L21vdGlvbi5idXR0b24+XG5cbiAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtyZXNldFRvQ3JlYXRlTW9kZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtOCBweS00IGJnLWdyYXktODAwLzgwIGhvdmVyOmJnLWdyYXktNzAwIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCBob3Zlcjpib3JkZXItZ3JheS01MDBcIlxuICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDIgfX1cbiAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45OCB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFBsdXNDaXJjbGUgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgU3RhcnQgRnJlc2hcbiAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICAvLyBTaG93IHN1Y2Nlc3Mgc2NyZWVuIGlmIHJlc3VtZSBpcyBnZW5lcmF0ZWRcbiAgaWYgKHJlc3VtZUdlbmVyYXRlZCAmJiByZXN1bWVEYXRhKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1ibGFjayB0by1bIzBBMEEwQV0gdGV4dC13aGl0ZSByZWxhdGl2ZVwiPlxuICAgICAgICB7LyogR3JpZCBiYWNrZ3JvdW5kICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgb3BhY2l0eS0yMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1bdXJsKCcvZ3JpZC5zdmcnKV0gW21hc2staW1hZ2U6cmFkaWFsLWdyYWRpZW50KGVsbGlwc2VfYXRfY2VudGVyLHdoaXRlLHRyYW5zcGFyZW50KV1cIiAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8RmxvYXRpbmdFbGVtZW50cyAvPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBjb250YWluZXIgbXgtYXV0byBweC00IHB5LTE2XCI+XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHRleHQtY2VudGVyXCJcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgey8qIFN1Y2Nlc3MgSGVhZGVyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBzY2FsZTogMCB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgc2NhbGU6IDEgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiAwLjIgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTIwIGgtMjAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTQwMCB0by1ncmVlbi02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNlwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIG1kOnRleHQtNXhsIGZvbnQtYm9sZCBtYi00XCI+XG4gICAgICAgICAgICAgICAgUmVzdW1lIEdlbmVyYXRlZCBTdWNjZXNzZnVsbHkhXG4gICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgIFlvdXIgQUktb3B0aW1pemVkIHJlc3VtZSBpcyByZWFkeSBmb3IgZG93bmxvYWRcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBBVFMgU2NvcmUgRGlzcGxheSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtOCBtYi04XCI+XG4gICAgICAgICAgICAgIHsvKiBBVFMgU2NvcmUgQ2lyY2xlICovfVxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktOTAwLzYwIGJhY2tkcm9wLWJsdXItbWQgcm91bmRlZC0yeGwgcC04IGJvcmRlciBib3JkZXItd2hpdGUvMTBcIlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMC40IH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTYgdGV4dC1jZW50ZXJcIj5BVFMgQ29tcGF0aWJpbGl0eSBTY29yZTwvaDM+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8QVRTU2NvcmVDaXJjbGUgc2NvcmU9e2F0c1Njb3JlIHx8IDc1fSBzaXplPXsxNTB9IC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgICB7LyogU3VnZ2VzdGlvbnMgKi99XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAvNjAgYmFja2Ryb3AtYmx1ci1tZCByb3VuZGVkLTJ4bCBwLTggYm9yZGVyIGJvcmRlci13aGl0ZS8xMFwiXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAyMCB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuNiB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi02XCI+QUkgUmVjb21tZW5kYXRpb25zPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyB0ZXh0LWxlZnRcIj5cbiAgICAgICAgICAgICAgICAgIHtzdWdnZXN0aW9ucy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgICAgICBzdWdnZXN0aW9ucy5zbGljZSgwLCA0KS5tYXAoKHN1Z2dlc3Rpb24sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0zIHAtMyBiZy1uZXVyYWwtcHVycGxlLzEwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1uZXVyYWwtcHVycGxlLzIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMTAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zLCBkZWxheTogMC44ICsgaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctbmV1cmFsLXB1cnBsZSByb3VuZGVkLWZ1bGwgbXQtMiBmbGV4LXNocmluay0wXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3R5cGVvZiBzdWdnZXN0aW9uID09PSAnc3RyaW5nJyA/IHN1Z2dlc3Rpb24gOiBzdWdnZXN0aW9uLm1lc3NhZ2UgfHwgc3VnZ2VzdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgICAgICkpXG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtY2VudGVyXCI+Tm8gc3BlY2lmaWMgcmVjb21tZW5kYXRpb25zIGF0IHRoaXMgdGltZS48L3A+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEFjdGlvbiBCdXR0b25zICovfVxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNCBqdXN0aWZ5LWNlbnRlciBtYi04XCJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMSB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UERGRG93bmxvYWRcbiAgICAgICAgICAgICAgICBmb3JtRGF0YT17Zm9ybURhdGF9XG4gICAgICAgICAgICAgICAgcmVzdW1lRGF0YT17cmVzdW1lRGF0YX1cbiAgICAgICAgICAgICAgICB0ZW1wbGF0ZUlkPXtzZWxlY3RlZFRlbXBsYXRlfVxuICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgIDxWaWV3UmVzdW1lQnV0dG9uXG4gICAgICAgICAgICAgICAgZm9ybURhdGE9e2Zvcm1EYXRhfVxuICAgICAgICAgICAgICAgIHJlc3VtZURhdGE9e3Jlc3VtZURhdGF9XG4gICAgICAgICAgICAgICAgdGVtcGxhdGVJZD17c2VsZWN0ZWRUZW1wbGF0ZX1cbiAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dQcmV2aWV3KCFzaG93UHJldmlldyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTggcHktNCBiZy1uZXVyYWwtYmx1ZS8yMCBob3ZlcjpiZy1uZXVyYWwtYmx1ZS8zMCBib3JkZXIgYm9yZGVyLW5ldXJhbC1ibHVlLzUwIHRleHQtbmV1cmFsLWJsdWUgaG92ZXI6dGV4dC13aGl0ZSBmb250LW1lZGl1bSByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wMiB9fVxuICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk4IH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgIHtzaG93UHJldmlldyA/ICdIaWRlIFByZXZpZXcnIDogJ1ZpZXcgUHJldmlldyd9XG4gICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cblxuICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIHNldFJlc3VtZUdlbmVyYXRlZChmYWxzZSk7XG4gICAgICAgICAgICAgICAgICBzZXRSZXN1bWVEYXRhKG51bGwpO1xuICAgICAgICAgICAgICAgICAgc2V0QXRzU2NvcmUobnVsbCk7XG4gICAgICAgICAgICAgICAgICBzZXRTdWdnZXN0aW9ucyhbXSk7XG4gICAgICAgICAgICAgICAgICBzZXRDdXJyZW50U3RlcCgwKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC04IHB5LTQgYmctZ3JheS04MDAvODAgaG92ZXI6YmctZ3JheS03MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBib3JkZXIgYm9yZGVyLWdyYXktNjAwIGhvdmVyOmJvcmRlci1ncmF5LTUwMFwiXG4gICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wMiB9fVxuICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk4IH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8V2FuZDIgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgQ3JlYXRlIEFub3RoZXIgUmVzdW1lXG4gICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgey8qIFJlc3VtZSBQcmV2aWV3ICovfVxuICAgICAgICAgICAge3Nob3dQcmV2aWV3ICYmIChcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIGhlaWdodDogMCB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgaGVpZ2h0OiAnYXV0bycgfX1cbiAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIGhlaWdodDogMCB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1iLThcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgPEVuaGFuY2VkUmVzdW1lUHJldmlldyBmb3JtRGF0YT17Zm9ybURhdGF9IC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1ibGFjayB0by1bIzBBMEEwQV0gdGV4dC13aGl0ZSByZWxhdGl2ZSBwdC0xNlwiPlxuICAgICAgey8qIEZvcm0gSGVhZGVyICovfVxuICAgICAgPEZvcm1IZWFkZXJcbiAgICAgICAgY3VycmVudFN0ZXA9e2N1cnJlbnRTdGVwfVxuICAgICAgICB0b3RhbFN0ZXBzPXtzdGVwcy5sZW5ndGh9XG4gICAgICAgIHN0ZXBzPXtzdGVwc31cbiAgICAgICAgb25CYWNrPXsoKSA9PiB3aW5kb3cuaGlzdG9yeS5iYWNrKCl9XG4gICAgICAgIG9uUHJldmlldz17KCkgPT4gc2V0U2hvd1ByZXZpZXcoIXNob3dQcmV2aWV3KX1cbiAgICAgICAgb25TYXZlPXsoKSA9PiBjb25zb2xlLmxvZygnU2F2ZSBkcmFmdCcpfVxuICAgICAgICBzaG93UHJldmlldz17c2hvd1ByZXZpZXd9XG4gICAgICAvPlxuXG4gICAgICB7LyogR3JpZCBiYWNrZ3JvdW5kICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG9wYWNpdHktMjBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLVt1cmwoJy9ncmlkLnN2ZycpXSBbbWFzay1pbWFnZTpyYWRpYWwtZ3JhZGllbnQoZWxsaXBzZV9hdF9jZW50ZXIsd2hpdGUsdHJhbnNwYXJlbnQpXVwiIC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPEZsb2F0aW5nRWxlbWVudHMgLz5cblxuICAgICAgPFByb2dyZXNzQmFyXG4gICAgICAgIGlzVmlzaWJsZT17c2hvd1Byb2dyZXNzQmFyfVxuICAgICAgICBvbkNvbXBsZXRlPXtoYW5kbGVQcm9ncmVzc0NvbXBsZXRlfVxuICAgICAgLz5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIGNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTggbWQ6bWItMTIgcmVsYXRpdmUgei0yMFwiXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtMjAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTMgbWItNCBtZDptYi02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxTcGFya2xlc0ljb24gY2xhc3NOYW1lPVwiaC04IHctOCBtZDpoLTEwIG1kOnctMTAgdGV4dC1uZXVyYWwtcGluayBhbmltYXRlLXB1bHNlXCIgLz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLW5ldXJhbC1waW5rIG9wYWNpdHktMjAgcm91bmRlZC1mdWxsIGJsdXItbWRcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGxnOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgIEFJIFJlc3VtZSBCdWlsZGVyXG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1iYXNlIG1kOnRleHQtbGcgbWF4LXctMnhsIG14LWF1dG8gcHgtNFwiPlxuICAgICAgICAgICAgQ3JlYXRlIHByb2Zlc3Npb25hbCwgQVRTLWZyaWVuZGx5IHJlc3VtZXMgaW4gbWludXRlcyB3aXRoIG91ciBBSS1wb3dlcmVkIGJ1aWxkZXJcbiAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICB7LyogUHJvZ3Jlc3MgaW5kaWNhdG9yIC0gb25seSBzaG93IGZvciBjcmVhdGUgbW9kZSAqL31cbiAgICAgICAgICB7YnVpbGRlck1vZGUgPT09ICdjcmVhdGUnICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzUwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1mdWxsIHB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+U3RlcCB7Y3VycmVudFN0ZXAgKyAxfSBvZiB7c3RlcHMubGVuZ3RofTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIE1vZGUgVG9nZ2xlIC0gb25seSBzaG93IGlmIG5vdCBpbiBjcmVhdGUgbW9kZSBvciBhdCBzdGVwIDAgKi99XG4gICAgICAgIHsoYnVpbGRlck1vZGUgIT09ICdjcmVhdGUnIHx8IGN1cnJlbnRTdGVwID09PSAwKSAmJiAhcmVzdW1lR2VuZXJhdGVkICYmIChcbiAgICAgICAgICA8UmVzdW1lQnVpbGRlck1vZGVUb2dnbGVcbiAgICAgICAgICAgIGN1cnJlbnRNb2RlPXtidWlsZGVyTW9kZX1cbiAgICAgICAgICAgIG9uTW9kZUNoYW5nZT17aGFuZGxlTW9kZUNoYW5nZX1cbiAgICAgICAgICAvPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBVcGxvYWQgU2VjdGlvbiAqL31cbiAgICAgICAgeyhidWlsZGVyTW9kZSA9PT0gJ3VwbG9hZCcgfHwgYnVpbGRlck1vZGUgPT09ICdhbmFseXplJykgJiYgIXNob3dBbmFseXNpcyAmJiAhc2hvd0pvYkRlc2NyaXB0aW9uSW5wdXQgJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIG1iLThcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxSZXN1bWVVcGxvYWRcbiAgICAgICAgICAgICAgb25BbmFseXNpc0NvbXBsZXRlPXtoYW5kbGVVcGxvYWRBbmFseXNpc31cbiAgICAgICAgICAgICAgYW5hbHlzaXNUeXBlPXtidWlsZGVyTW9kZSA9PT0gJ2FuYWx5emUnID8gJ3F1aWNrJyA6ICdmdWxsJ31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBKb2IgRGVzY3JpcHRpb24gSW5wdXQgZm9yIFVwbG9hZCAmIEVuaGFuY2UgKi99XG4gICAgICAgIHtidWlsZGVyTW9kZSA9PT0gJ3VwbG9hZCcgJiYgc2hvd0pvYkRlc2NyaXB0aW9uSW5wdXQgJiYgdXBsb2FkQW5hbHlzaXMgJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIG1iLThcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMlwiPlJlc3VtZSBVcGxvYWRlZCBTdWNjZXNzZnVsbHkhPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPk5vdyBwcm92aWRlIHRoZSBqb2IgZGVzY3JpcHRpb24gdG8gY3JlYXRlIGEgdGFyZ2V0ZWQsIEFUUy1vcHRpbWl6ZWQgcmVzdW1lPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxKb2JEZXNjcmlwdGlvbklucHV0XG4gICAgICAgICAgICAgIG9uSm9iRGVzY3JpcHRpb25TdWJtaXQ9e2hhbmRsZUpvYkRlc2NyaXB0aW9uU3VibWl0fVxuICAgICAgICAgICAgICBpc0xvYWRpbmc9e2lzR2VuZXJhdGluZ1RhcmdldGVkfVxuICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIG10LTZcIj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBzZXRTaG93Sm9iRGVzY3JpcHRpb25JbnB1dChmYWxzZSk7XG4gICAgICAgICAgICAgICAgICBzZXRDdXJyZW50U3RlcCgwKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC02IHB5LTMgYmctZ3JheS04MDAvODAgaG92ZXI6YmctZ3JheS03MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBib3JkZXIgYm9yZGVyLWdyYXktNjAwIGhvdmVyOmJvcmRlci1ncmF5LTUwMFwiXG4gICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wMiB9fVxuICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk4IH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBTa2lwIEpvYiBUYXJnZXRpbmdcbiAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBTaG93IGFuYWx5c2lzIHJlc3VsdHMgZm9yIHVwbG9hZCBtb2RlICovfVxuICAgICAgICB7YnVpbGRlck1vZGUgPT09ICd1cGxvYWQnICYmIHNob3dBbmFseXNpcyAmJiB1cGxvYWRBbmFseXNpcyAmJiAoXG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gbWItOFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+UmVzdW1lIEFuYWx5c2lzIENvbXBsZXRlPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlJldmlldyB0aGUgZXh0cmFjdGVkIGluZm9ybWF0aW9uIGFuZCBjb250aW51ZSB0byBlbmhhbmNlIHlvdXIgcmVzdW1lPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxBVFNBbmFseXNpc0Rpc3BsYXlcbiAgICAgICAgICAgICAgYW5hbHlzaXNEYXRhPXt1cGxvYWRBbmFseXNpc31cbiAgICAgICAgICAgICAgYW5hbHlzaXNUeXBlPVwiZnVsbFwiXG4gICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbXQtNlwiPlxuICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRTdGVwKDApfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC04IHB5LTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLW5ldXJhbC1wdXJwbGUgdG8tbmV1cmFsLXBpbmsgdGV4dC13aGl0ZSBmb250LWJvbGQgcm91bmRlZC14bCBob3ZlcjpvcGFjaXR5LTkwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsXCJcbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjAyIH19XG4gICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgIENvbnRpbnVlIHRvIEZvcm0gRWRpdG9yXG4gICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogUmVndWxhciBmb3JtIGZsb3cgLSBvbmx5IHNob3cgZm9yIGNyZWF0ZSBtb2RlIG9yIGFmdGVyIHVwbG9hZCBhbmFseXNpcyAqL31cbiAgICAgICAgeyhidWlsZGVyTW9kZSA9PT0gJ2NyZWF0ZScgfHwgKGJ1aWxkZXJNb2RlID09PSAndXBsb2FkJyAmJiBjdXJyZW50U3RlcCA+PSAwICYmICFzaG93Sm9iRGVzY3JpcHRpb25JbnB1dCkpICYmICFyZXN1bWVHZW5lcmF0ZWQgJiYgKFxuICAgICAgICAgIDw+XG4gICAgICAgICAgICB7LyogRW5oYW5jZWQgU3RlcCBJbmRpY2F0b3IgKi99XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi04IG1kOm1iLTEyXCJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8U3RlcEluZGljYXRvclxuICAgICAgICAgICAgICAgIGN1cnJlbnRTdGVwPXtjdXJyZW50U3RlcH1cbiAgICAgICAgICAgICAgICB0b3RhbFN0ZXBzPXtzdGVwcy5sZW5ndGh9XG4gICAgICAgICAgICAgICAgc3RlcHM9e3N0ZXBzfVxuICAgICAgICAgICAgICAgIG9uU3RlcENsaWNrPXsoc3RlcEluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAoc3RlcEluZGV4IDw9IGN1cnJlbnRTdGVwKSB7XG4gICAgICAgICAgICAgICAgICAgIHNldEN1cnJlbnRTdGVwKHN0ZXBJbmRleCk7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBhbGxvd0NsaWNrTmF2aWdhdGlvbj17dHJ1ZX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuXG5cbiAgICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgeGw6Z3JpZC1jb2xzLTMgZ2FwLTYgbGc6Z2FwLTggbWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgICAgICB7LyogRm9ybSBTZWN0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwieGw6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAvNjAgYmFja2Ryb3AtYmx1ci1tZCByb3VuZGVkLTJ4bCBwLTYgbWQ6cC04IGJvcmRlciBib3JkZXItd2hpdGUvMTAgc2hhZG93LTJ4bFwiXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjIgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIEZvcm0gSGVhZGVyICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgbWQ6bWItOFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgbWItMlwiPlxuICAgICAgICAgICAgICAgICAge1JlYWN0LmNyZWF0ZUVsZW1lbnQoc3RlcHNbY3VycmVudFN0ZXBdLmljb24sIHtcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lOiBcImgtNiB3LTYgbWQ6aC03IG1kOnctNyB0ZXh0LW5ldXJhbC1wdXJwbGVcIlxuICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBtZDp0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7c3RlcHNbY3VycmVudFN0ZXBdLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc20gbWQ6dGV4dC1iYXNlXCI+XG4gICAgICAgICAgICAgICAgICB7c3RlcHNbY3VycmVudFN0ZXBdLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPEFuaW1hdGVQcmVzZW5jZSBtb2RlPVwid2FpdFwiPlxuICAgICAgICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gMCAmJiA8UGVyc29uYWxJbmZvRm9ybSBmb3JtRGF0YT17Zm9ybURhdGF9IHVwZGF0ZUZvcm1EYXRhPXt1cGRhdGVGb3JtRGF0YX0gYXRzQW5hbHlzaXM9e2F0c0FuYWx5c2lzfSAvPn1cbiAgICAgICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDEgJiYgPEVkdWNhdGlvbkZvcm0gZm9ybURhdGE9e2Zvcm1EYXRhfSB1cGRhdGVGb3JtRGF0YT17dXBkYXRlRm9ybURhdGF9IGFkZEFycmF5SXRlbT17YWRkQXJyYXlJdGVtfSByZW1vdmVBcnJheUl0ZW09e3JlbW92ZUFycmF5SXRlbX0gYXRzQW5hbHlzaXM9e2F0c0FuYWx5c2lzfSAvPn1cbiAgICAgICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDIgJiYgPEV4cGVyaWVuY2VGb3JtIGZvcm1EYXRhPXtmb3JtRGF0YX0gdXBkYXRlRm9ybURhdGE9e3VwZGF0ZUZvcm1EYXRhfSBhZGRBcnJheUl0ZW09e2FkZEFycmF5SXRlbX0gcmVtb3ZlQXJyYXlJdGVtPXtyZW1vdmVBcnJheUl0ZW19IGF0c0FuYWx5c2lzPXthdHNBbmFseXNpc30gLz59XG4gICAgICAgICAgICAgICAge2N1cnJlbnRTdGVwID09PSAzICYmIDxTa2lsbHNQcm9qZWN0c0Zvcm0gZm9ybURhdGE9e2Zvcm1EYXRhfSB1cGRhdGVGb3JtRGF0YT17dXBkYXRlRm9ybURhdGF9IGFkZEFycmF5SXRlbT17YWRkQXJyYXlJdGVtfSByZW1vdmVBcnJheUl0ZW09e3JlbW92ZUFycmF5SXRlbX0gYXRzQW5hbHlzaXM9e2F0c0FuYWx5c2lzfSAvPn1cbiAgICAgICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDQgJiYgPFJldmlld0Zvcm0gZm9ybURhdGE9e2Zvcm1EYXRhfSBhdHNBbmFseXNpcz17YXRzQW5hbHlzaXN9IC8+fVxuICAgICAgICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBQcmV2aWV3IFNlY3Rpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ4bDpjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC82MCBiYWNrZHJvcC1ibHVyLW1kIHJvdW5kZWQtMnhsIHAtNCBtZDpwLTYgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCBzdGlja3kgdG9wLTQgbWQ6dG9wLTggc2hhZG93LTJ4bFwiXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuNCB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00IG1kOm1iLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1iYXNlIG1kOnRleHQtbGcgZm9udC1zZW1pYm9sZCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTQgdy00IG1kOmgtNSBtZDp3LTUgdGV4dC1uZXVyYWwtYmx1ZVwiIC8+XG4gICAgICAgICAgICAgICAgICBMaXZlIFByZXZpZXdcbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93UHJldmlldyghc2hvd1ByZXZpZXcpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xLjUgbWQ6cHgtNCBtZDpweS0yIGJnLW5ldXJhbC1ibHVlLzIwIGhvdmVyOmJnLW5ldXJhbC1ibHVlLzMwIGJvcmRlciBib3JkZXItbmV1cmFsLWJsdWUvNTAgcm91bmRlZC1sZyB0ZXh0LW5ldXJhbC1ibHVlIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRleHQtc20gZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wNSB9fVxuICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7c2hvd1ByZXZpZXcgPyAnSGlkZScgOiAnU2hvdyd9XG4gICAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7c2hvd1ByZXZpZXcgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtaC1bNjAwcHhdIG1kOm1heC1oLVs3MDBweF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICA8RW5oYW5jZWRSZXN1bWVQcmV2aWV3IGZvcm1EYXRhPXtmb3JtRGF0YX0gLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyIG1kOnB5LTE2IHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgc2NhbGU6IDAuOCwgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IHNjYWxlOiAxLCBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtMTYgdy0xNiBtZDpoLTIwIG1kOnctMjAgbXgtYXV0byBtYi00IG9wYWNpdHktMzBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIG1kOnRleHQtYmFzZSBtYi0yXCI+UHJldmlldyB5b3VyIHJlc3VtZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBtZDp0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5DbGljayBcIlNob3dcIiB0byBzZWUgbGl2ZSB1cGRhdGVzPC9wPlxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgICB7LyogQVRTIE9wdGltaXphdGlvbiBQYW5lbCAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTZcIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjYgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPENsaWVudE9ubHk+XG4gICAgICAgICAgICAgICAgPEFUU09wdGltaXphdGlvblBhbmVsXG4gICAgICAgICAgICAgICAgICBmb3JtRGF0YT17Zm9ybURhdGF9XG4gICAgICAgICAgICAgICAgICBhdHNTY29yZT17YXRzQW5hbHlzaXMub3ZlcmFsbFNjb3JlfVxuICAgICAgICAgICAgICAgICAgc3VnZ2VzdGlvbnM9e2F0c0FuYWx5c2lzLnJlY29tbWVuZGF0aW9uc31cbiAgICAgICAgICAgICAgICAgIHJlYWxUaW1lQW5hbHlzaXM9e2F0c0FuYWx5c2lzfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvQ2xpZW50T25seT5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEFkZCBib3R0b20gcGFkZGluZyB0byBwcmV2ZW50IGNvbnRlbnQgZnJvbSBiZWluZyBoaWRkZW4gYmVoaW5kIHN0aWNreSBuYXZpZ2F0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInBiLTI0XCI+PC9kaXY+XG4gICAgICAgIDwvPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBUZW1wbGF0ZSBTZWxlY3RvciBNb2RhbCAqL31cbiAgICAgICAge3Nob3dUZW1wbGF0ZVNlbGVjdG9yICYmIChcbiAgICAgICAgICA8VGVtcGxhdGVTZWxlY3RvclxuICAgICAgICAgICAgc2VsZWN0ZWRUZW1wbGF0ZT17c2VsZWN0ZWRUZW1wbGF0ZX1cbiAgICAgICAgICAgIG9uVGVtcGxhdGVTZWxlY3Q9e3NldFNlbGVjdGVkVGVtcGxhdGV9XG4gICAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93VGVtcGxhdGVTZWxlY3RvcihmYWxzZSl9XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogU3RpY2t5IE5hdmlnYXRpb24gKi99XG4gICAgICAgIDxTdGlja3lOYXZpZ2F0aW9uXG4gICAgICAgICAgY3VycmVudFN0ZXA9e2N1cnJlbnRTdGVwfVxuICAgICAgICAgIHRvdGFsU3RlcHM9e3N0ZXBzLmxlbmd0aH1cbiAgICAgICAgICBvblByZXZpb3VzPXsoKSA9PiB7XG4gICAgICAgICAgICBpZiAoY3VycmVudFN0ZXAgPiAwKSB7XG4gICAgICAgICAgICAgIHNldEN1cnJlbnRTdGVwKGN1cnJlbnRTdGVwIC0gMSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfX1cbiAgICAgICAgICBvbk5leHQ9eygpID0+IHtcbiAgICAgICAgICAgIGlmIChjdXJyZW50U3RlcCA8IHN0ZXBzLmxlbmd0aCAtIDEpIHtcbiAgICAgICAgICAgICAgc2V0Q3VycmVudFN0ZXAoY3VycmVudFN0ZXAgKyAxKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uR2VuZXJhdGU9e2dlbmVyYXRlUmVzdW1lfVxuICAgICAgICAgIGlzR2VuZXJhdGluZz17aXNHZW5lcmF0aW5nfVxuICAgICAgICAgIGNhblByb2NlZWQ9e3RydWV9XG4gICAgICAgICAgc3RlcHM9e3N0ZXBzfVxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG4vLyBQZXJzb25hbCBJbmZvcm1hdGlvbiBGb3JtIENvbXBvbmVudFxuY29uc3QgUGVyc29uYWxJbmZvRm9ybSA9ICh7IGZvcm1EYXRhLCB1cGRhdGVGb3JtRGF0YSwgYXRzQW5hbHlzaXMgfSkgPT4gKFxuICA8bW90aW9uLmRpdlxuICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHk6IC0yMCB9fVxuICAgIGNsYXNzTmFtZT1cInNwYWNlLXktNiBtZDpzcGFjZS15LThcIlxuICA+XG4gICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00IG1kOmdhcC02XCI+XG4gICAgICA8ZGl2PlxuICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBtZDp0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yIG1kOm1iLTNcIj5cbiAgICAgICAgICBGaXJzdCBOYW1lICpcbiAgICAgICAgICA8QVRTVG9vbHRpcCBmaWVsZFR5cGU9XCJmaXJzdE5hbWVcIiBjbGFzc05hbWU9XCJtbC0yXCIgLz5cbiAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgPGlucHV0XG4gICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wZXJzb25hbC5maXJzdE5hbWV9XG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgncGVyc29uYWwnLCAnZmlyc3ROYW1lJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IG1kOnB4LTUgcHktMyBtZDpweS00IGJnLWdyYXktODAwLzYwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC14bCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1uZXVyYWwtcHVycGxlIGZvY3VzOmJvcmRlci1uZXVyYWwtcHVycGxlIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOmJvcmRlci1ncmF5LTUwMCB0ZXh0LXNtIG1kOnRleHQtYmFzZVwiXG4gICAgICAgICAgcGxhY2Vob2xkZXI9XCJSYWh1bFwiXG4gICAgICAgIC8+XG4gICAgICAgIDxDbGllbnRPbmx5PlxuICAgICAgICAgIDxBVFNGaWVsZEluZGljYXRvclxuICAgICAgICAgICAgZmllbGROYW1lPVwiZmlyc3ROYW1lXCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wZXJzb25hbC5maXJzdE5hbWV9XG4gICAgICAgICAgICBhbmFseXNpcz17YXRzQW5hbHlzaXMuZmllbGRBbmFseXNpcz8uZmlyc3ROYW1lfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvQ2xpZW50T25seT5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2PlxuICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBtZDp0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yIG1kOm1iLTNcIj5cbiAgICAgICAgICBMYXN0IE5hbWUgKlxuICAgICAgICA8L2xhYmVsPlxuICAgICAgICA8aW5wdXRcbiAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvbmFsLmxhc3ROYW1lfVxuICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3BlcnNvbmFsJywgJ2xhc3ROYW1lJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IG1kOnB4LTUgcHktMyBtZDpweS00IGJnLWdyYXktODAwLzYwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC14bCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1uZXVyYWwtcHVycGxlIGZvY3VzOmJvcmRlci1uZXVyYWwtcHVycGxlIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOmJvcmRlci1ncmF5LTUwMCB0ZXh0LXNtIG1kOnRleHQtYmFzZVwiXG4gICAgICAgICAgcGxhY2Vob2xkZXI9XCJTaGFybWFcIlxuICAgICAgICAvPlxuICAgICAgICA8Q2xpZW50T25seT5cbiAgICAgICAgICA8QVRTRmllbGRJbmRpY2F0b3JcbiAgICAgICAgICAgIGZpZWxkTmFtZT1cImxhc3ROYW1lXCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wZXJzb25hbC5sYXN0TmFtZX1cbiAgICAgICAgICAgIGFuYWx5c2lzPXthdHNBbmFseXNpcz8uZmllbGRBbmFseXNpcz8ubGFzdE5hbWV9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9DbGllbnRPbmx5PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXY+XG4gICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIG1kOnRleHQtYmFzZSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTIgbWQ6bWItM1wiPlxuICAgICAgICAgIDxNYWlsIGNsYXNzTmFtZT1cImlubGluZSBoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgIEVtYWlsICpcbiAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgPGlucHV0XG4gICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGVyc29uYWwuZW1haWx9XG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgncGVyc29uYWwnLCAnZW1haWwnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgbWQ6cHgtNSBweS0zIG1kOnB5LTQgYmctZ3JheS04MDAvNjAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLXhsIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW5ldXJhbC1wdXJwbGUgZm9jdXM6Ym9yZGVyLW5ldXJhbC1wdXJwbGUgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6Ym9yZGVyLWdyYXktNTAwIHRleHQtc20gbWQ6dGV4dC1iYXNlXCJcbiAgICAgICAgICBwbGFjZWhvbGRlcj1cInJhaHVsLnNoYXJtYUBleGFtcGxlLmNvbVwiXG4gICAgICAgIC8+XG4gICAgICAgIDxDbGllbnRPbmx5PlxuICAgICAgICAgIDxBVFNGaWVsZEluZGljYXRvclxuICAgICAgICAgICAgZmllbGROYW1lPVwiZW1haWxcIlxuICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvbmFsLmVtYWlsfVxuICAgICAgICAgICAgYW5hbHlzaXM9e2F0c0FuYWx5c2lzPy5maWVsZEFuYWx5c2lzPy5lbWFpbH1cbiAgICAgICAgICAvPlxuICAgICAgICA8L0NsaWVudE9ubHk+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdj5cbiAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gbWQ6dGV4dC1iYXNlIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMiBtZDptYi0zXCI+XG4gICAgICAgICAgPFBob25lIGNsYXNzTmFtZT1cImlubGluZSBoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgIFBob25lXG4gICAgICAgIDwvbGFiZWw+XG4gICAgICAgIDxpbnB1dFxuICAgICAgICAgIHR5cGU9XCJ0ZWxcIlxuICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wZXJzb25hbC5waG9uZX1cbiAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdwZXJzb25hbCcsICdwaG9uZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBtZDpweC01IHB5LTMgbWQ6cHktNCBiZy1ncmF5LTgwMC82MCBib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQteGwgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctbmV1cmFsLXB1cnBsZSBmb2N1czpib3JkZXItbmV1cmFsLXB1cnBsZSB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBob3Zlcjpib3JkZXItZ3JheS01MDAgdGV4dC1zbSBtZDp0ZXh0LWJhc2VcIlxuICAgICAgICAgIHBsYWNlaG9sZGVyPVwiKzkxIDk4NzY1IDQzMjEwXCJcbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2PlxuICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBtZDp0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yIG1kOm1iLTNcIj5cbiAgICAgICAgICA8TWFwUGluIGNsYXNzTmFtZT1cImlubGluZSBoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgIExvY2F0aW9uXG4gICAgICAgIDwvbGFiZWw+XG4gICAgICAgIDxpbnB1dFxuICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGVyc29uYWwubG9jYXRpb259XG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgncGVyc29uYWwnLCAnbG9jYXRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgbWQ6cHgtNSBweS0zIG1kOnB5LTQgYmctZ3JheS04MDAvNjAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLXhsIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW5ldXJhbC1wdXJwbGUgZm9jdXM6Ym9yZGVyLW5ldXJhbC1wdXJwbGUgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6Ym9yZGVyLWdyYXktNTAwIHRleHQtc20gbWQ6dGV4dC1iYXNlXCJcbiAgICAgICAgICBwbGFjZWhvbGRlcj1cIk11bWJhaSwgTWFoYXJhc2h0cmFcIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXY+XG4gICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIG1kOnRleHQtYmFzZSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTIgbWQ6bWItM1wiPlxuICAgICAgICAgIDxMaW5rIGNsYXNzTmFtZT1cImlubGluZSBoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgIExpbmtlZEluXG4gICAgICAgIDwvbGFiZWw+XG4gICAgICAgIDxpbnB1dFxuICAgICAgICAgIHR5cGU9XCJ1cmxcIlxuICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wZXJzb25hbC5saW5rZWRpbn1cbiAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdwZXJzb25hbCcsICdsaW5rZWRpbicsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBtZDpweC01IHB5LTMgbWQ6cHktNCBiZy1ncmF5LTgwMC82MCBib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQteGwgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctbmV1cmFsLXB1cnBsZSBmb2N1czpib3JkZXItbmV1cmFsLXB1cnBsZSB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBob3Zlcjpib3JkZXItZ3JheS01MDAgdGV4dC1zbSBtZDp0ZXh0LWJhc2VcIlxuICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly9saW5rZWRpbi5jb20vaW4vcmFodWxzaGFybWFcIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6Y29sLXNwYW4tMlwiPlxuICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBtZDp0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yIG1kOm1iLTNcIj5cbiAgICAgICAgICA8R2xvYmUgY2xhc3NOYW1lPVwiaW5saW5lIGgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgUG9ydGZvbGlvL1dlYnNpdGVcbiAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgPGlucHV0XG4gICAgICAgICAgdHlwZT1cInVybFwiXG4gICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvbmFsLnBvcnRmb2xpb31cbiAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdwZXJzb25hbCcsICdwb3J0Zm9saW8nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgbWQ6cHgtNSBweS0zIG1kOnB5LTQgYmctZ3JheS04MDAvNjAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLXhsIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW5ldXJhbC1wdXJwbGUgZm9jdXM6Ym9yZGVyLW5ldXJhbC1wdXJwbGUgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6Ym9yZGVyLWdyYXktNTAwIHRleHQtc20gbWQ6dGV4dC1iYXNlXCJcbiAgICAgICAgICBwbGFjZWhvbGRlcj1cImh0dHBzOi8vcmFodWxzaGFybWEuZGV2XCJcbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gbWQ6dGV4dC1iYXNlIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMiBtZDptYi0zXCI+XG4gICAgICAgICAgUHJvZmVzc2lvbmFsIFN1bW1hcnlcbiAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvbmFsLnN1bW1hcnl9XG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgncGVyc29uYWwnLCAnc3VtbWFyeScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IG1kOnB4LTUgcHktMyBtZDpweS00IGJnLWdyYXktODAwLzYwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC14bCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1uZXVyYWwtcHVycGxlIGZvY3VzOmJvcmRlci1uZXVyYWwtcHVycGxlIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgcmVzaXplLW5vbmUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOmJvcmRlci1ncmF5LTUwMCB0ZXh0LXNtIG1kOnRleHQtYmFzZVwiXG4gICAgICAgICAgcGxhY2Vob2xkZXI9XCJFeHBlcmllbmNlZCBzb2Z0d2FyZSBlbmdpbmVlciB3aXRoIDUrIHllYXJzIGluIGZ1bGwtc3RhY2sgZGV2ZWxvcG1lbnQsIHNwZWNpYWxpemluZyBpbiBSZWFjdCwgTm9kZS5qcywgYW5kIGNsb3VkIHRlY2hub2xvZ2llcy4gUHJvdmVuIHRyYWNrIHJlY29yZCBvZiBkZWxpdmVyaW5nIHNjYWxhYmxlIHNvbHV0aW9ucyBhbmQgbGVhZGluZyBjcm9zcy1mdW5jdGlvbmFsIHRlYW1zLi4uXCJcbiAgICAgICAgLz5cbiAgICAgICAgPENsaWVudE9ubHk+XG4gICAgICAgICAgPEFUU0ZpZWxkSW5kaWNhdG9yXG4gICAgICAgICAgICBmaWVsZE5hbWU9XCJzdW1tYXJ5XCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wZXJzb25hbC5zdW1tYXJ5fVxuICAgICAgICAgICAgYW5hbHlzaXM9e2F0c0FuYWx5c2lzPy5maWVsZEFuYWx5c2lzPy5zdW1tYXJ5fVxuICAgICAgICAgICAgc2hvd0RldGFpbHM9e3RydWV9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9DbGllbnRPbmx5PlxuXG4gICAgICAgIHsvKiBBSSBDb250ZW50IFN1Z2dlc3Rpb25zIGZvciBTdW1tYXJ5ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTNcIj5cbiAgICAgICAgICA8QUlDb250ZW50U3VnZ2VzdGlvbnNcbiAgICAgICAgICAgIGZpZWxkVHlwZT1cInN1bW1hcnlcIlxuICAgICAgICAgICAgY3VycmVudFZhbHVlPXtmb3JtRGF0YS5wZXJzb25hbC5zdW1tYXJ5fVxuICAgICAgICAgICAgb25TdWdnZXN0aW9uQXBwbHk9eyhzdWdnZXN0aW9uKSA9PiB1cGRhdGVGb3JtRGF0YSgncGVyc29uYWwnLCAnc3VtbWFyeScsIHN1Z2dlc3Rpb24pfVxuICAgICAgICAgICAgY29udGV4dD17e1xuICAgICAgICAgICAgICBmaXJzdE5hbWU6IGZvcm1EYXRhLnBlcnNvbmFsLmZpcnN0TmFtZSxcbiAgICAgICAgICAgICAgbGFzdE5hbWU6IGZvcm1EYXRhLnBlcnNvbmFsLmxhc3ROYW1lLFxuICAgICAgICAgICAgICBleHBlcmllbmNlOiBmb3JtRGF0YS5leHBlcmllbmNlLFxuICAgICAgICAgICAgICBza2lsbHM6IGZvcm1EYXRhLnNraWxsc1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICA8L21vdGlvbi5kaXY+XG4pO1xuXG4vLyBFZHVjYXRpb24gRm9ybSBDb21wb25lbnRcbmNvbnN0IEVkdWNhdGlvbkZvcm0gPSAoeyBmb3JtRGF0YSwgdXBkYXRlRm9ybURhdGEsIGFkZEFycmF5SXRlbSwgcmVtb3ZlQXJyYXlJdGVtLCBhdHNBbmFseXNpcyB9KSA9PiAoXG4gIDxtb3Rpb24uZGl2XG4gICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeTogLTIwIH19XG4gICAgY2xhc3NOYW1lPVwic3BhY2UteS02XCJcbiAgPlxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgPEdyYWR1YXRpb25DYXAgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LW5ldXJhbC1wdXJwbGVcIiAvPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+RWR1Y2F0aW9uPC9oMj5cbiAgICAgIDwvZGl2PlxuICAgICAgPGJ1dHRvblxuICAgICAgICBvbkNsaWNrPXsoKSA9PiBhZGRBcnJheUl0ZW0oJ2VkdWNhdGlvbicsIHtcbiAgICAgICAgICBkZWdyZWU6IFwiXCIsXG4gICAgICAgICAgaW5zdGl0dXRpb246IFwiXCIsXG4gICAgICAgICAgbG9jYXRpb246IFwiXCIsXG4gICAgICAgICAgc3RhcnREYXRlOiBcIlwiLFxuICAgICAgICAgIGVuZERhdGU6IFwiXCIsXG4gICAgICAgICAgZ3BhOiBcIlwiLFxuICAgICAgICAgIHJlbGV2YW50OiBcIlwiXG4gICAgICAgIH0pfVxuICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC00IHB5LTIgYmctbmV1cmFsLXB1cnBsZS8yMCBob3ZlcjpiZy1uZXVyYWwtcHVycGxlLzMwIGJvcmRlciBib3JkZXItbmV1cmFsLXB1cnBsZS81MCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgID5cbiAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgIEFkZCBFZHVjYXRpb25cbiAgICAgIDwvYnV0dG9uPlxuICAgIDwvZGl2PlxuXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHtmb3JtRGF0YS5lZHVjYXRpb24ubWFwKChlZHUsIGluZGV4KSA9PiAoXG4gICAgICAgIDxkaXYga2V5PXtlZHUuaWR9IGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzMwIHAtNiByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS03MDBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi00XCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+RWR1Y2F0aW9uIHtpbmRleCArIDF9PC9oMz5cbiAgICAgICAgICAgIHtmb3JtRGF0YS5lZHVjYXRpb24ubGVuZ3RoID4gMSAmJiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVBcnJheUl0ZW0oJ2VkdWNhdGlvbicsIGVkdS5pZCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNDAwIGhvdmVyOnRleHQtcmVkLTMwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIERlZ3JlZSAqXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17ZWR1LmRlZ3JlZX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdlZHVjYXRpb24nLCAnZGVncmVlJywgZS50YXJnZXQudmFsdWUsIGluZGV4KX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLWdyYXktODAwLzUwIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1uZXVyYWwtcHVycGxlIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkJhY2hlbG9yIG9mIFNjaWVuY2VcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8Q2xpZW50T25seT5cbiAgICAgICAgICAgICAgICA8QVRTRmllbGRJbmRpY2F0b3JcbiAgICAgICAgICAgICAgICAgIGZpZWxkTmFtZT1cImRlZ3JlZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17ZWR1LmRlZ3JlZX1cbiAgICAgICAgICAgICAgICAgIGFuYWx5c2lzPXthdHNBbmFseXNpcz8uZmllbGRBbmFseXNpcz8uW2BlZHVjYXRpb25fZGVncmVlXyR7aW5kZXh9YF19XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9DbGllbnRPbmx5PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIEluc3RpdHV0aW9uICpcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtlZHUuaW5zdGl0dXRpb259XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnZWR1Y2F0aW9uJywgJ2luc3RpdHV0aW9uJywgZS50YXJnZXQudmFsdWUsIGluZGV4KX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLWdyYXktODAwLzUwIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1uZXVyYWwtcHVycGxlIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlVuaXZlcnNpdHkgb2YgVGVjaG5vbG9neVwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxDbGllbnRPbmx5PlxuICAgICAgICAgICAgICAgIDxBVFNGaWVsZEluZGljYXRvclxuICAgICAgICAgICAgICAgICAgZmllbGROYW1lPVwiaW5zdGl0dXRpb25cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2VkdS5pbnN0aXR1dGlvbn1cbiAgICAgICAgICAgICAgICAgIGFuYWx5c2lzPXthdHNBbmFseXNpcz8uZmllbGRBbmFseXNpcz8uW2BlZHVjYXRpb25faW5zdGl0dXRpb25fJHtpbmRleH1gXX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L0NsaWVudE9ubHk+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgPE1hcFBpbiBjbGFzc05hbWU9XCJpbmxpbmUgaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICBMb2NhdGlvblxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2VkdS5sb2NhdGlvbn1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdlZHVjYXRpb24nLCAnbG9jYXRpb24nLCBlLnRhcmdldC52YWx1ZSwgaW5kZXgpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYmctZ3JheS04MDAvNTAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW5ldXJhbC1wdXJwbGUgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDBcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTmV3IFlvcmssIE5ZXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBHUEEgKE9wdGlvbmFsKVxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2VkdS5ncGF9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnZWR1Y2F0aW9uJywgJ2dwYScsIGUudGFyZ2V0LnZhbHVlLCBpbmRleCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBiZy1ncmF5LTgwMC81MCBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctbmV1cmFsLXB1cnBsZSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIzLjgvNC4wXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwiaW5saW5lIGgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgU3RhcnQgRGF0ZVxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwibW9udGhcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtlZHUuc3RhcnREYXRlfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2VkdWNhdGlvbicsICdzdGFydERhdGUnLCBlLnRhcmdldC52YWx1ZSwgaW5kZXgpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYmctZ3JheS04MDAvNTAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW5ldXJhbC1wdXJwbGUgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJpbmxpbmUgaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICBFbmQgRGF0ZVxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwibW9udGhcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtlZHUuZW5kRGF0ZX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdlZHVjYXRpb24nLCAnZW5kRGF0ZScsIGUudGFyZ2V0LnZhbHVlLCBpbmRleCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBiZy1ncmF5LTgwMC81MCBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctbmV1cmFsLXB1cnBsZSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpjb2wtc3Bhbi0yXCI+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIFJlbGV2YW50IENvdXJzZXdvcmsvQWNoaWV2ZW1lbnRzXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgIHZhbHVlPXtlZHUucmVsZXZhbnR9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnZWR1Y2F0aW9uJywgJ3JlbGV2YW50JywgZS50YXJnZXQudmFsdWUsIGluZGV4KX1cbiAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYmctZ3JheS04MDAvNTAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW5ldXJhbC1wdXJwbGUgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgcmVzaXplLW5vbmVcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUmVsZXZhbnQgY291cnNld29yaywgaG9ub3JzLCBhY2hpZXZlbWVudHMuLi5cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIDwvbW90aW9uLmRpdj5cbik7XG5cblxuXG5leHBvcnQgZGVmYXVsdCBSZXN1bWVCdWlsZGVyOyJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwiVXNlciIsIkdyYWR1YXRpb25DYXAiLCJCcmllZmNhc2UiLCJBd2FyZCIsIkZpbGVUZXh0IiwiQXJyb3dMZWZ0IiwiQXJyb3dSaWdodCIsIlNwYXJrbGVzIiwiRXllIiwiV2FuZDIiLCJQbHVzIiwiVHJhc2gyIiwiTWFwUGluIiwiQ2FsZW5kYXIiLCJHbG9iZSIsIk1haWwiLCJQaG9uZSIsIkxpbmsiLCJVcGxvYWQiLCJQbHVzQ2lyY2xlIiwiUGFsZXR0ZSIsIlNwYXJrbGVzSWNvbiIsInRvYXN0IiwiUHJvZ3Jlc3NCYXIiLCJBVFNTY29yZUNpcmNsZSIsIkVuaGFuY2VkUmVzdW1lUHJldmlldyIsIlBERkRvd25sb2FkIiwiVmlld1Jlc3VtZUJ1dHRvbiIsIlJlc3VtZUJ1aWxkZXJNb2RlVG9nZ2xlIiwiUmVzdW1lVXBsb2FkIiwiQVRTQW5hbHlzaXNEaXNwbGF5IiwiSm9iRGVzY3JpcHRpb25JbnB1dCIsIkJlZm9yZUFmdGVyQ29tcGFyaXNvbiIsIkFJQ29udGVudFN1Z2dlc3Rpb25zIiwiQVRTT3B0aW1pemF0aW9uUGFuZWwiLCJUZW1wbGF0ZVNlbGVjdG9yIiwiVXBsb2FkRW5oYW5jZW1lbnRXb3JrZmxvdyIsInVzZUFUU0FuYWx5c2lzIiwiQVRTRmllbGRJbmRpY2F0b3IiLCJDbGllbnRPbmx5IiwiQVRTVG9vbHRpcCIsIlN0ZXBJbmRpY2F0b3IiLCJTdGlja3lOYXZpZ2F0aW9uIiwiRm9ybUhlYWRlciIsIkV4cGVyaWVuY2VGb3JtIiwiU2tpbGxzUHJvamVjdHNGb3JtIiwiUmV2aWV3Rm9ybSIsIlJlc3VtZUJ1aWxkZXIiLCJjdXJyZW50U3RlcCIsInNldEN1cnJlbnRTdGVwIiwiaXNHZW5lcmF0aW5nIiwic2V0SXNHZW5lcmF0aW5nIiwic2hvd1Byb2dyZXNzQmFyIiwic2V0U2hvd1Byb2dyZXNzQmFyIiwicmVzdW1lR2VuZXJhdGVkIiwic2V0UmVzdW1lR2VuZXJhdGVkIiwicmVzdW1lVXJsIiwic2V0UmVzdW1lVXJsIiwicmVzdW1lRGF0YSIsInNldFJlc3VtZURhdGEiLCJhdHNTY29yZSIsInNldEF0c1Njb3JlIiwic3VnZ2VzdGlvbnMiLCJzZXRTdWdnZXN0aW9ucyIsInNob3dQcmV2aWV3Iiwic2V0U2hvd1ByZXZpZXciLCJidWlsZGVyTW9kZSIsInNldEJ1aWxkZXJNb2RlIiwidXBsb2FkQW5hbHlzaXMiLCJzZXRVcGxvYWRBbmFseXNpcyIsInNob3dBbmFseXNpcyIsInNldFNob3dBbmFseXNpcyIsInNob3dVcGxvYWRXb3JrZmxvdyIsInNldFNob3dVcGxvYWRXb3JrZmxvdyIsInNob3dKb2JEZXNjcmlwdGlvbklucHV0Iiwic2V0U2hvd0pvYkRlc2NyaXB0aW9uSW5wdXQiLCJpc0dlbmVyYXRpbmdUYXJnZXRlZCIsInNldElzR2VuZXJhdGluZ1RhcmdldGVkIiwidGFyZ2V0ZWRSZXN1bWVEYXRhIiwic2V0VGFyZ2V0ZWRSZXN1bWVEYXRhIiwic2VsZWN0ZWRUZW1wbGF0ZSIsInNldFNlbGVjdGVkVGVtcGxhdGUiLCJzaG93VGVtcGxhdGVTZWxlY3RvciIsInNldFNob3dUZW1wbGF0ZVNlbGVjdG9yIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsInBlcnNvbmFsIiwiZmlyc3ROYW1lIiwibGFzdE5hbWUiLCJlbWFpbCIsInBob25lIiwibG9jYXRpb24iLCJsaW5rZWRpbiIsInBvcnRmb2xpbyIsInN1bW1hcnkiLCJlZHVjYXRpb24iLCJpZCIsImRlZ3JlZSIsImluc3RpdHV0aW9uIiwic3RhcnREYXRlIiwiZW5kRGF0ZSIsImdwYSIsInJlbGV2YW50IiwiZXhwZXJpZW5jZSIsInRpdGxlIiwiY29tcGFueSIsImN1cnJlbnQiLCJkZXNjcmlwdGlvbiIsInNraWxscyIsInRlY2huaWNhbCIsImxhbmd1YWdlcyIsImNlcnRpZmljYXRpb25zIiwicHJvamVjdHMiLCJuYW1lIiwidGVjaG5vbG9naWVzIiwibGluayIsImF0c0FuYWx5c2lzIiwic3RlcHMiLCJpY29uIiwidXBkYXRlRm9ybURhdGEiLCJzZWN0aW9uIiwiZmllbGQiLCJ2YWx1ZSIsImluZGV4IiwicHJldiIsIkFycmF5IiwiaXNBcnJheSIsIm5ld0FycmF5IiwiYWRkQXJyYXlJdGVtIiwidGVtcGxhdGUiLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHJpbmciLCJyZW1vdmVBcnJheUl0ZW0iLCJmaWx0ZXIiLCJpdGVtIiwibmV4dFN0ZXAiLCJsZW5ndGgiLCJwcmV2U3RlcCIsImdlbmVyYXRlUmVzdW1lIiwiZGF0YSIsImNvbnNvbGUiLCJsb2ciLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwidGVtcGxhdGVJZCIsInN0YXR1cyIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwiZW50cmllcyIsImNvbnRlbnRUeXBlIiwiZ2V0IiwiaW5jbHVkZXMiLCJ0ZXh0UmVzcG9uc2UiLCJ0ZXh0IiwiZXJyb3IiLCJFcnJvciIsImpzb24iLCJvayIsImRvd25sb2FkVXJsIiwib3ZlcmFsbCIsImltcHJvdmVtZW50cyIsIm1lc3NhZ2UiLCJzdGFjayIsImhhbmRsZVByb2dyZXNzQ29tcGxldGUiLCJzY29yZU1lc3NhZ2UiLCJzdWNjZXNzIiwiaGFuZGxlVXBsb2FkQW5hbHlzaXMiLCJhbmFseXNpc0RhdGEiLCJhbmFseXNpc1R5cGUiLCJleHRyYWN0ZWREYXRhIiwiZXh0cmFjdGVkIiwicHJldkRhdGEiLCJtYXAiLCJlZHUiLCJEYXRlIiwibm93IiwiZXhwIiwicHJvaiIsImVuaGFuY2VtZW50cyIsImFuYWx5c2lzIiwicmVjb21tZW5kYXRpb25zIiwiZmFsbGJhY2siLCJoYW5kbGVKb2JEZXNjcmlwdGlvblN1Ym1pdCIsImpvYkRhdGEiLCJleHRyYWN0ZWRSZXN1bWVEYXRhIiwiam9iRGVzY3JpcHRpb24iLCJqb2JUaXRsZSIsImVuaGFuY2VkUmVzdW1lIiwiZW5oYW5jZWRGb3JtRGF0YSIsImFjaGlldmVtZW50cyIsImpvaW4iLCJoYW5kbGVNb2RlQ2hhbmdlIiwibW9kZSIsInJlc2V0VG9DcmVhdGVNb2RlIiwiRmxvYXRpbmdFbGVtZW50cyIsImVsZW1lbnRzIiwid2lkdGgiLCJoZWlnaHQiLCJsZWZ0IiwidG9wIiwiZHVyYXRpb24iLCJ4IiwieSIsImRpdiIsImNsYXNzTmFtZSIsImVsZW1lbnQiLCJpIiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJhbmltYXRlIiwidHJhbnNpdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwicmVwZWF0VHlwZSIsImVhc2UiLCJpbml0aWFsIiwib3BhY2l0eSIsImgxIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ3aGlsZUhvdmVyIiwic2NhbGUiLCJ3aGlsZVRhcCIsImRlbGF5IiwiaDMiLCJzY29yZSIsInNpemUiLCJzbGljZSIsInN1Z2dlc3Rpb24iLCJleGl0IiwidG90YWxTdGVwcyIsIm9uQmFjayIsIndpbmRvdyIsImhpc3RvcnkiLCJiYWNrIiwib25QcmV2aWV3Iiwib25TYXZlIiwiaXNWaXNpYmxlIiwib25Db21wbGV0ZSIsInNwYW4iLCJjdXJyZW50TW9kZSIsIm9uTW9kZUNoYW5nZSIsIm9uQW5hbHlzaXNDb21wbGV0ZSIsImgyIiwib25Kb2JEZXNjcmlwdGlvblN1Ym1pdCIsImlzTG9hZGluZyIsIm9uU3RlcENsaWNrIiwic3RlcEluZGV4IiwiYWxsb3dDbGlja05hdmlnYXRpb24iLCJjcmVhdGVFbGVtZW50IiwiUGVyc29uYWxJbmZvRm9ybSIsIkVkdWNhdGlvbkZvcm0iLCJvdmVyYWxsU2NvcmUiLCJyZWFsVGltZUFuYWx5c2lzIiwib25UZW1wbGF0ZVNlbGVjdCIsIm9uQ2xvc2UiLCJvblByZXZpb3VzIiwib25OZXh0Iiwib25HZW5lcmF0ZSIsImNhblByb2NlZWQiLCJsYWJlbCIsImZpZWxkVHlwZSIsImlucHV0IiwidHlwZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwiZmllbGROYW1lIiwiZmllbGRBbmFseXNpcyIsInRleHRhcmVhIiwicm93cyIsInNob3dEZXRhaWxzIiwiY3VycmVudFZhbHVlIiwib25TdWdnZXN0aW9uQXBwbHkiLCJjb250ZXh0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ })

});