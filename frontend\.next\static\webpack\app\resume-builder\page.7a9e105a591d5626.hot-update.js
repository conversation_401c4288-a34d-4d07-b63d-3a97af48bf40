"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Download,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Phone,Plus,Sparkles,Trash2,User,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProgressBar */ \"(app-pages-browser)/./src/components/ProgressBar.jsx\");\n/* harmony import */ var _components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ResumeFormComponents */ \"(app-pages-browser)/./src/components/ResumeFormComponents.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ResumeBuilder = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    // Main state management\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ]\n    });\n    // Steps configuration\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"Tell us about yourself\"\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Your academic background\"\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Your work experience\"\n        },\n        {\n            id: 3,\n            title: \"Skills & Projects\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Showcase your abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Finalize your resume\"\n        }\n    ];\n    // Update form data\n    const updateFormData = function(section, field, value) {\n        let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        setFormData((prev)=>{\n            if (index !== null && Array.isArray(prev[section])) {\n                const newArray = [\n                    ...prev[section]\n                ];\n                newArray[index] = {\n                    ...newArray[index],\n                    [field]: value\n                };\n                return {\n                    ...prev,\n                    [section]: newArray\n                };\n            } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                return {\n                    ...prev,\n                    [section]: {\n                        ...prev[section],\n                        [field]: value\n                    }\n                };\n            }\n            return prev;\n        });\n    };\n    // Add new item to array sections\n    const addArrayItem = (section, template)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: [\n                    ...prev[section],\n                    {\n                        ...template,\n                        id: Date.now()\n                    }\n                ]\n            }));\n    };\n    // Remove item from array sections\n    const removeArrayItem = (section, id)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [section]: prev[section].filter((item)=>item.id !== id)\n            }));\n    };\n    // Navigation functions\n    const nextStep = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    // Generate resume with AI\n    const generateResume = async ()=>{\n        try {\n            setIsGenerating(true);\n            setShowProgressBar(true);\n            const response = await fetch('/api/generate-resume', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to generate resume');\n            }\n            setResumeUrl(data.downloadUrl);\n        } catch (error) {\n            console.error(\"Error generating resume:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || \"Failed to generate resume\");\n            setIsGenerating(false);\n            setShowProgressBar(false);\n        }\n    };\n    const handleProgressComplete = ()=>{\n        setShowProgressBar(false);\n        setResumeGenerated(true);\n        setIsGenerating(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Your resume has been generated successfully!\");\n    };\n    // Floating background elements (matching hero section)\n    const FloatingElements = ()=>{\n        _s1();\n        const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [elements, setElements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ResumeBuilder.FloatingElements.useEffect\": ()=>{\n                setMounted(true);\n                // Generate consistent random values only on client side\n                const newElements = [\n                    ...Array(6)\n                ].map({\n                    \"ResumeBuilder.FloatingElements.useEffect.newElements\": (_, i)=>({\n                            id: i,\n                            initialX: Math.random() * 1200,\n                            initialY: Math.random() * 800,\n                            targetX: Math.random() * 1200,\n                            targetY: Math.random() * 800,\n                            width: Math.random() * 200 + 100,\n                            height: Math.random() * 200 + 100,\n                            duration: Math.random() * 20 + 20\n                        })\n                }[\"ResumeBuilder.FloatingElements.useEffect.newElements\"]);\n                setElements(newElements);\n            }\n        }[\"ResumeBuilder.FloatingElements.useEffect\"], []);\n        if (!mounted || elements.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: elements.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"absolute rounded-full bg-neural-purple opacity-10 blur-xl\",\n                    initial: {\n                        x: element.initialX,\n                        y: element.initialY,\n                        width: element.width,\n                        height: element.height\n                    },\n                    animate: {\n                        x: element.targetX,\n                        y: element.targetY,\n                        transition: {\n                            duration: element.duration,\n                            repeat: Infinity,\n                            repeatType: 'reverse'\n                        }\n                    }\n                }, element.id, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(FloatingElements, \"OR3uaTnixrLRKhvlgMvLSlkofQU=\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElements, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: handleProgressComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"text-center mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-neural-pink\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                children: \"Create a professional, ATS-friendly resume in minutes with our AI-powered builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center space-x-4 overflow-x-auto pb-4\",\n                            children: steps.map((step, index)=>{\n                                const Icon = step.icon;\n                                const isActive = index === currentStep;\n                                const isCompleted = index < currentStep;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    className: \"flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-300 \".concat(isActive ? 'bg-gradient-to-r from-neural-purple to-neural-pink border-neural-purple shadow-lg shadow-neural-purple/25' : isCompleted ? 'bg-neural-purple/20 border-neural-purple/50' : 'bg-gray-800/50 border-gray-700'),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5 \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium hidden md:block \".concat(isActive || isCompleted ? 'text-white' : 'text-gray-400'),\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, step.id, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    className: \"bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: [\n                                            currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonalInfoForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EducationForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_4__.ExperienceForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_4__.SkillsProjectsForm, {\n                                                formData: formData,\n                                                updateFormData: updateFormData,\n                                                addArrayItem: addArrayItem,\n                                                removeArrayItem: removeArrayItem\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 39\n                                            }, undefined),\n                                            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_4__.ReviewForm, {\n                                                formData: formData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 39\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    className: \"bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10 sticky top-8\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 text-neural-blue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Live Preview\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowPreview(!showPreview),\n                                                    className: \"text-neural-blue hover:text-neural-pink transition-colors\",\n                                                    children: showPreview ? 'Hide' : 'Show'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResumePreview, {\n                                            formData: formData\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: 'Click \"Show\" to preview your resume'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevStep,\n                                disabled: currentStep === 0,\n                                className: \"flex items-center gap-2 px-6 py-3 rounded-lg transition-all \".concat(currentStep === 0 ? 'bg-gray-800 text-gray-500 cursor-not-allowed' : 'bg-gray-800 hover:bg-gray-700 text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Previous\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, undefined),\n                            currentStep === steps.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: generateResume,\n                                disabled: isGenerating,\n                                className: \"flex items-center gap-2 px-8 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity disabled:opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isGenerating ? 'Generating...' : 'Generate Resume'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextStep,\n                                className: \"flex items-center gap-2 px-6 py-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold hover:opacity-90 transition-opacity\",\n                                children: [\n                                    \"Next\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"Z7z82en7Y9n3Ocn/UsN3+8Tv4v0=\");\n_c = ResumeBuilder;\n// Personal Information Form Component\nconst PersonalInfoForm = (param)=>{\n    let { formData, updateFormData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-6 w-6 text-neural-purple\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 423,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"Personal Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 424,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 422,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"First Name *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 429,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.firstName,\n                                onChange: (e)=>updateFormData('personal', 'firstName', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"John\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 432,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 428,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Last Name *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 442,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.lastName,\n                                onChange: (e)=>updateFormData('personal', 'lastName', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"Doe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 445,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 441,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Email *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 455,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"email\",\n                                value: formData.personal.email,\n                                onChange: (e)=>updateFormData('personal', 'email', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 459,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 454,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Phone\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 469,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"tel\",\n                                value: formData.personal.phone,\n                                onChange: (e)=>updateFormData('personal', 'phone', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"+****************\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 473,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 468,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Location\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 483,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.personal.location,\n                                onChange: (e)=>updateFormData('personal', 'location', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"New York, NY\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 487,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 482,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Linkedin, {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"LinkedIn\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 497,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.personal.linkedin,\n                                onChange: (e)=>updateFormData('personal', 'linkedin', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"https://linkedin.com/in/johndoe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 501,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 496,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"inline h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Portfolio/Website\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 511,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.personal.portfolio,\n                                onChange: (e)=>updateFormData('personal', 'portfolio', e.target.value),\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                placeholder: \"https://johndoe.com\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 515,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 510,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Professional Summary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 525,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: formData.personal.summary,\n                                onChange: (e)=>updateFormData('personal', 'summary', e.target.value),\n                                rows: 4,\n                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                placeholder: \"Brief overview of your professional background and key achievements...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 528,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 524,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 427,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 416,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PersonalInfoForm;\n// Education Form Component\nconst EducationForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 550,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 551,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 549,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addArrayItem('education', {\n                                degree: \"\",\n                                institution: \"\",\n                                location: \"\",\n                                startDate: \"\",\n                                endDate: \"\",\n                                gpa: \"\",\n                                relevant: \"\"\n                            }),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 565,\n                                columnNumber: 9\n                            }, undefined),\n                            \"Add Education\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 553,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 548,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: formData.education.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 p-6 rounded-lg border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Education \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeArrayItem('education', edu.id),\n                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Degree *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.degree,\n                                                onChange: (e)=>updateFormData('education', 'degree', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"Bachelor of Science\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Institution *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.institution,\n                                                onChange: (e)=>updateFormData('education', 'institution', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"University of Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Location\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.location,\n                                                onChange: (e)=>updateFormData('education', 'location', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"New York, NY\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"GPA (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: edu.gpa,\n                                                onChange: (e)=>updateFormData('education', 'gpa', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\",\n                                                placeholder: \"3.8/4.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Start Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.startDate,\n                                                onChange: (e)=>updateFormData('education', 'startDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"inline h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"End Date\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"month\",\n                                                value: edu.endDate,\n                                                onChange: (e)=>updateFormData('education', 'endDate', e.target.value, index),\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Relevant Coursework/Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: edu.relevant,\n                                                onChange: (e)=>updateFormData('education', 'relevant', e.target.value, index),\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"Relevant coursework, honors, achievements...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, edu.id, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 572,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 570,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 542,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EducationForm;\n// Resume Preview Component\nconst ResumePreview = (param)=>{\n    let { formData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white text-black p-6 rounded-lg text-xs max-h-96 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center border-b border-gray-300 pb-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: [\n                            formData.personal.firstName,\n                            \" \",\n                            formData.personal.lastName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 689,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-2 mt-2 text-gray-600\",\n                        children: [\n                            formData.personal.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 693,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 694,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.phone\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 695,\n                                columnNumber: 37\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 696,\n                                columnNumber: 40\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.location\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 697,\n                                columnNumber: 40\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 692,\n                        columnNumber: 7\n                    }, undefined),\n                    formData.personal.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 text-blue-600\",\n                        children: formData.personal.linkedin\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 700,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 688,\n                columnNumber: 5\n            }, undefined),\n            formData.personal.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"PROFESSIONAL SUMMARY\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 707,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 leading-relaxed\",\n                        children: formData.personal.summary\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 710,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 706,\n                columnNumber: 7\n            }, undefined),\n            formData.experience.length > 0 && formData.experience[0].title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"EXPERIENCE\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 717,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.experience.map((exp, index)=>exp.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: exp.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: exp.company\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        exp.startDate,\n                                                        \" - \",\n                                                        exp.current ? 'Present' : exp.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                exp.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: exp.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, undefined),\n                                exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-gray-700 whitespace-pre-line\",\n                                    children: exp.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, exp.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 722,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 716,\n                columnNumber: 7\n            }, undefined),\n            formData.education.length > 0 && formData.education[0].degree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"EDUCATION\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 747,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.education.map((edu, index)=>edu.degree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: edu.degree\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: edu.institution\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        edu.startDate,\n                                                        \" - \",\n                                                        edu.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                edu.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: edu.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, undefined),\n                                edu.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"GPA: \",\n                                        edu.gpa\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 763,\n                                    columnNumber: 27\n                                }, undefined),\n                                edu.relevant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: edu.relevant\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 32\n                                }, undefined)\n                            ]\n                        }, edu.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 752,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 746,\n                columnNumber: 7\n            }, undefined),\n            (formData.skills.technical.length > 0 || formData.skills.languages.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"SKILLS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 774,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.skills.technical.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Technical: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 779,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.technical.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 780,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 778,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.skills.languages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Languages: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 785,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.languages.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 786,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 784,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.skills.certifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Certifications: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 791,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: formData.skills.certifications.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 792,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 790,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 773,\n                columnNumber: 7\n            }, undefined),\n            formData.projects.length > 0 && formData.projects[0].name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-sm font-bold text-gray-900 border-b border-gray-300 mb-2\",\n                        children: \"PROJECTS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 801,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.projects.map((project, index)=>project.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: project.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        project.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: project.link,\n                                            className: \"text-blue-600 hover:underline\",\n                                            children: \"Link\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 15\n                                }, undefined),\n                                project.technologies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 italic\",\n                                    children: project.technologies\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 816,\n                                    columnNumber: 17\n                                }, undefined),\n                                project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: project.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, project.id, true, {\n                            fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 800,\n                columnNumber: 7\n            }, undefined),\n            !formData.personal.firstName && !formData.personal.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Download_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Phone_Plus_Sparkles_Trash2_User_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-12 w-12 mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 830,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Start filling out the form to see your resume preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 831,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 829,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\BlinkFind\\\\BlinkFind-Web\\\\frontend\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 686,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = ResumePreview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ResumeBuilder\");\n$RefreshReg$(_c1, \"PersonalInfoForm\");\n$RefreshReg$(_c2, \"EducationForm\");\n$RefreshReg$(_c3, \"ResumePreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ })

});